<template>
  <div class="view-mode-selector" ref="selectorRef">
    <!-- Compact Toggle Button -->
    <button class="mode-toggle-btn" @click="toggleMenu" :class="{ 'active': isMenuOpen }"
      :title="`ปัจจุบัน: ${getCurrentModeText()}`" ref="toggleBtnRef">
      <div class="toggle-icon">
        <q-icon :name="getCurrentModeIcon()" size="18px" />
      </div>
      <span class="toggle-label">{{ getCurrentModeText() }}</span>
      <div class="toggle-arrow">
        <q-icon name="keyboard_arrow_down" size="16px" :class="{ 'rotated': isMenuOpen }" />
      </div>
    </button>

    <!-- Fixed Dropdown Menu Portal -->
    <teleport to="body">
      <transition name="dropdown">
        <div v-if="isMenuOpen" class="dropdown-menu-portal" :style="menuPosition">
          <div class="menu-title">
            <q-icon name="visibility" size="16px" />
            <span>เลือกรูปแบบการดู</span>
          </div>

          <div class="menu-items">
            <button v-for="option in menuOptions" :key="option.value" class="menu-item"
              :class="{ 'selected': props.selectedMenu === option.value }" @click="selectMode(option.value)">
              <div class="item-icon">
                <q-icon :name="option.icon" size="20px" />
              </div>
              <div class="item-content">
                <div class="item-title">{{ option.title }}</div>
                <div class="item-desc">{{ option.description }}</div>
              </div>
              <div class="item-check" v-if="props.selectedMenu === option.value">
                <q-icon name="check" size="16px" />
              </div>
            </button>
          </div>
        </div>
      </transition>

      <!-- Backdrop -->
      <div v-if="isMenuOpen" class="menu-backdrop-portal" @click="closeMenu"></div>
    </teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

// Types
interface MenuOption {
  value: string
  title: string
  description: string
  icon: string
}

interface Props {
  selectedMenu?: string
}

// Props & Emits
const props = withDefaults(defineProps<Props>(), {
  selectedMenu: 'all'
})

const emit = defineEmits<{
  'menu-selected': [menu: string]
}>()

// Reactive state
const isMenuOpen = ref(false)
const selectorRef = ref<HTMLElement>()
const toggleBtnRef = ref<HTMLElement>()

// Computed properties for positioning
const menuPosition = computed(() => {
  if (!toggleBtnRef.value) return {}

  const rect = toggleBtnRef.value.getBoundingClientRect()
  return {
    position: 'fixed' as const,
    top: `${rect.bottom + 4}px`,
    left: `${rect.left}px`,
    zIndex: 999999
  }
})

// Menu options configuration
const menuOptions: MenuOption[] = [
  {
    value: 'all',
    title: 'ฉบับเต็ม',
    description: 'อ่านเนื้อหาเอกสารทั้งหมดแบบละเอียดครบถ้วน',
    icon: 'article'
  },
  {
    value: 'summary',
    title: 'สรุปย่อ',
    description: 'อ่านเนื้อหาสำคัญที่สรุปแล้วแบบย่อๆ',
    icon: 'summarize'
  },
  {
    value: 'pdf',
    title: 'PDF ต้นฉบับ',
    description: 'ดูไฟล์เอกสารต้นฉบับในรูปแบบ PDF',
    icon: 'picture_as_pdf'
  }
]

// Computed properties
const currentOption = computed(() =>
  menuOptions.find(option => option.value === props.selectedMenu) || menuOptions[0]
)

// Methods
function toggleMenu() {
  isMenuOpen.value = !isMenuOpen.value
}

function closeMenu() {
  isMenuOpen.value = false
}

function selectMode(menu: string) {
  if (menu !== props.selectedMenu) {
    emit('menu-selected', menu)
  }
  closeMenu()
}

function getCurrentModeText(): string {
  return currentOption.value?.title || 'เลือกรูปแบบ'
}

function getCurrentModeIcon(): string {
  return currentOption.value?.icon || 'visibility'
}



// Keyboard navigation
function handleKeydown(event: KeyboardEvent) {
  if (event.key === 'Escape' && isMenuOpen.value) {
    closeMenu()
  }
}

// Lifecycle
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
/* Modern CSS Variables */
:root {
  --primary-color: #3b82f6;
  --primary-hover: #2563eb;
  --primary-light: #dbeafe;
  --success-color: #10b981;
  --surface-color: #ffffff;
  --surface-secondary: #f8fafc;
  --surface-tertiary: #f1f5f9;
  --border-color: #e2e8f0;
  --border-light: #f1f5f9;
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-muted: #94a3b8;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --gradient-primary: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  --gradient-surface: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

/* Main Container */
.view-mode-selector {
  position: sticky;
  top: 87px;
  left: 24px;
  z-index: 99999;
  font-family: 'Noto Sans Thai', sans-serif;
}

/* Toggle Button */
.mode-toggle-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  color: #334155;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1), 0 2px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 180px;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.mode-toggle-btn::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: inherit;
}

.mode-toggle-btn:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-color: #cbd5e1;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px) scale(1.02);
}

.mode-toggle-btn:hover::before {
  opacity: 0.05;
}

.mode-toggle-btn.active {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-color: #1d4ed8;
  color: white;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4), 0 4px 12px rgba(59, 130, 246, 0.2);
  transform: translateY(-2px) scale(1.02);
}

.mode-toggle-btn.active::before {
  opacity: 0.2;
}

.toggle-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: inherit;
}

.toggle-label {
  flex: 1;
  text-align: left;
  font-weight: 500;
}

.toggle-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease;
  opacity: 0.7;
}

.toggle-arrow .rotated {
  transform: rotate(180deg);
}

/* Portal Dropdown Menu */
.dropdown-menu-portal {
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25), 0 10px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  min-width: 280px;
  z-index: 999999;
  backdrop-filter: blur(10px);
}

.menu-title {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-size: 13px;
  font-weight: 600;
  color: #6b7280;
}

.menu-items {
  padding: 4px;
}

.menu-item {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 14px;
  border: none;
  background: transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  color: #374151;
  margin-bottom: 3px;
  position: relative;
}

.menu-item:hover {
  background: #f8fafc;
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.menu-item.selected {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  color: #1d4ed8;
  border: 1px solid #93c5fd;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.menu-item.selected::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 60%;
  background: #3b82f6;
  border-radius: 0 2px 2px 0;
}

.item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: #f1f5f9;
  border-radius: 8px;
  flex-shrink: 0;
  color: #64748b;
  transition: all 0.2s ease;
}

.menu-item.selected .item-icon {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.item-title {
  font-size: 15px;
  font-weight: 600;
  line-height: 1.3;
  color: inherit;
}

.item-desc {
  font-size: 12px;
  color: #64748b;
  line-height: 1.4;
  font-weight: 400;
}

.menu-item.selected .item-desc {
  color: #3b82f6;
}

.item-check {
  color: #10b981;
  display: flex;
  align-items: center;
  justify-content: center;
}



.menu-backdrop-portal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: 999998;
}

/* Animations */
.dropdown-enter-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-leave-active {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-enter-from {
  opacity: 0;
  transform: translateY(-12px) scale(0.9);
}

.dropdown-leave-to {
  opacity: 0;
  transform: translateY(-8px) scale(0.95);
}

/* Responsive */
@media (max-width: 1566px) {
  .mode-toggle-btn {
    min-width: 120px;
    padding: 6px 10px;
    font-size: 13px;
    border-radius: 6px;
  }

  .dropdown-menu-portal {
    min-width: 220px;
  }

  .menu-item {
    padding: 8px 10px;
  }

  .item-icon {
    width: 28px;
    height: 28px;
  }
}

@media (max-width: 1024px) {
  .view-mode-selector {
    top: 16px;
    left: 16px;
  }

  .mode-toggle-btn {
    min-width: 120px;
    padding: 6px 10px;
    font-size: 13px;
  }

  .dropdown-menu-portal {
    min-width: 220px;
  }

  .menu-item {
    padding: 8px 10px;
  }

  .item-icon {
    width: 28px;
    height: 28px;
  }
}

/* Additional responsive breakpoints */
@media (max-width: 200px) {
  .view-mode-selector {
    top: 65px;
    left: 12px;
  }

  .mode-toggle-btn {
    min-width: 100px;
    padding: 4px 8px;
    font-size: 12px;
  }

  .dropdown-menu-portal {
    min-width: 200px;
  }
}
</style>
