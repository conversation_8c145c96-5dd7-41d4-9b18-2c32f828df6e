
<template>
  <q-page-container>
    <q-page class="q-pa-md background-image">
      <q-card flat bordered class="transparent-card">
        <q-card-section class="transparent-card text-white">
          <div class="text-h5 row items-center">
            <div class="col-auto">
              <div class="text-h4">การจัดการผู้ใช้งาน</div>
            </div>
            <q-space />
            <div class="row q-gutter-md">
              <div>
                <q-select outlined dense v-model="departmentFilter" :options="departments" label="หน่วยงาน" placeholder="หน่วยงาน"
                  class="bg-white text-black wide-select" clearable />
              </div>
              <div>
                <q-select outlined dense v-model="positionFilter" :options="positions" label="ตำแหน่ง" placeholder="ตำแหน่ง"
                  class="bg-white text-black wide-select" clearable />
              </div>
              <div>
                <q-input outlined dense v-model="search" placeholder="ค้นหา"
                  class="custom-search bg-white text-black searcher wide-search border-none">
                  <template v-slot:prepend>
                    <q-icon name="search" />
                  </template>
                </q-input>
              </div>
              <q-btn color="info" label="เพิ่มผู้ใช้งาน" icon="person_add" @click="showAddUserDialog = true" />
            </div>
          </div>
        </q-card-section>

        <q-card-section class="transparent-card">
          <div class="table-container" style="max-height: 600px; overflow-y: auto">
            <q-table
              :rows="filteredUsers"
              :columns="columns"
              row-key="name"
              bordered
              dense
              v-model:pagination="pagination"
              :rows-per-page-options="[5, 10, 25, 50, 0]"
            >
              <template v-slot:body-cell-index="props">
                <q-td :props="props">
                  {{ props.rowIndex + 1 }}
                </q-td>
              </template>
              <template v-slot:body-cell-actions="props">
                <q-td :props="props">
                  <q-btn flat round dense icon="edit" color="black"
                    @click="openEditUserDialog(props.row, props.rowIndex)" />
                  <q-btn flat round dense icon="delete" color="red" @click="confirmDeleteUser(props.rowIndex)" />
                </q-td>
              </template>
            </q-table>
          </div>
        </q-card-section>
      </q-card>

      <!-- DELETE USER DIALOG -->
      <q-dialog v-model="showDeleteDialog">
        <q-card style="width: 400px; text-align: center">
          <q-card-section>
            <q-icon name="person_remove" size="80px" />
            <div class="text-h6 q-mt-md">DELETE USER</div>
            <p>คุณแน่ใจหรือไม่ว่าต้องการลบผู้ใช้นี้?</p>
          </q-card-section>
          <q-card-actions align="center">
            <q-btn label="ACCEPT" color="green" @click="deleteUser" />
            <q-btn label="CANCEL" color="red" @click="showDeleteDialog = false" />
          </q-card-actions>
        </q-card>
      </q-dialog>

      <q-dialog v-model="showEditUserDialog">
        <q-card style="width: 600px; text-align: center">
          <q-card-section>
            <div class="text-h6">EDIT USER</div>
          </q-card-section>

          <q-card-section class="add-user-container">
            <div class="user-form">
              <div class="input-row">
                <q-input v-model="firstName" label="ชื่อ" outlined dense class="full-width" />
                <q-input v-model="lastName" label="นามสกุล" outlined dense class="full-width" />
              </div>
              <div class="input-row">
                <q-select v-model="department" :options="departments" label="หน่วยงาน" outlined dense
                  class="full-width" />
                <q-select v-model="position" :options="positions" label="ตำแหน่ง" outlined dense class="full-width" />
              </div>
            </div>

            <!-- Avatar -->
            <div class="avatar-container column">
              <q-img v-if="avatarPreview" :src="avatarPreview" contain style="width: 150px; height: 150px; border-radius: 10px;" />
              <q-icon v-else name="person" size="80px" />
              <input ref="avatarInput" type="file" accept="image/*" class="hidden-input" @change="onAvatarSelected" />
              <q-btn v-if="!avatarPreview" class="q-mt-sm" label="เลือกรูป" color="primary" size="sm" @click="triggerAvatarInput" />
              <q-btn v-if="avatarPreview" flat round dense icon="close" color="negative" class="remove-avatar-btn" @click="clearAvatar" />
            </div>
          </q-card-section>

          <q-card-actions class="button-group">
            <q-btn label="SAVE" color="green" @click="saveEditedUser" />
            <q-btn label="CANCEL" color="red" @click="showEditUserDialog = false" />
          </q-card-actions>
        </q-card>
      </q-dialog>

      <!-- ADD USER DIALOG (Main Selection) -->
      <q-dialog v-model="showAddUserDialog">
        <q-card class="add-user-main-card">
          <q-card-section class="q-pt-lg q-pb-md">
            <div class="title-add-user">เพิ่มผู้ใช้งาน</div>
          </q-card-section>

          <q-card-section class="choice-grid">
            <div class="choice-panel">
              <q-icon name="person" size="150px" color="white" />
              <q-btn class="choice-btn" unelevated label="หนึ่งคน" @click="openOneUserDialog" />
            </div>
            <div class="choice-panel">
              <q-icon name="group" size="150px" color="white" />
              <q-btn class="choice-btn" unelevated label="หลายคน" @click="openManyUsersDialog" />
            </div>
          </q-card-section>
        </q-card>
      </q-dialog>

      <!-- ADD ONE USER DIALOG -->
      <q-dialog v-model="showOneUserDialog">
        <q-card style="width: 600px; text-align: center">
          <q-card-section>
            <div class="text-h6">เพิ่มผู้ใช้งาน</div>
          </q-card-section>

          <q-card-section class="add-user-container">
            <!-- ฟอร์มข้อมูลผู้ใช้ -->
            <div class="user-form">
              <div class="input-row">
                <q-input v-model="firstName" label="ชื่อ" outlined dense class="full-width">
                  <template v-slot:prepend>
                    <q-icon name="person" />
                  </template>
                </q-input>
                <q-input v-model="lastName" label="นามสกุล" outlined dense class="full-width">
                  <template v-slot:prepend>
                    <q-icon name="badge" />
                  </template>
                </q-input>
              </div>
              <div class="input-row">
                <q-input v-model="email" type="email" label="อีเมล" outlined dense class="full-width">
                  <template v-slot:prepend>
                    <q-icon name="mail" />
                  </template>
                </q-input>
                <q-select v-model="position" :options="positions" label="ตำแหน่ง" outlined dense class="full-width" />
              </div>
              <div class="input-row">
                <q-input v-model="password" type="password" label="รหัสผ่าน" outlined dense class="full-width">
                  <template v-slot:prepend>
                    <q-icon name="lock" />
                  </template>
                </q-input>
                <q-select v-model="department" :options="departments" label="หน่วยงาน" outlined dense
                  class="full-width" />
              </div>
              <div class="input-row">
                <q-input v-model="username" label="ชื่อผู้ใช้" outlined dense class="full-width">
                  <template v-slot:prepend>
                    <q-icon name="account_circle" />
                  </template>
                </q-input>
              </div>
            </div>

            <!-- Avatar -->
            <div class="avatar-container column">
              <q-img v-if="avatarPreview" :src="avatarPreview" contain style="width: 150px; height: 150px; border-radius: 10px;" />
              <q-icon v-else name="person" size="80px" />
              <input ref="avatarInput" type="file" accept="image/*" class="hidden-input" @change="onAvatarSelected" />
              <q-btn v-if="!avatarPreview" class="q-mt-sm" label="เลือกรูป" color="primary" size="sm" @click="triggerAvatarInput" />
              <q-btn v-if="avatarPreview" flat round dense icon="close" color="negative" class="remove-avatar-btn" @click="clearAvatar" />
            </div>
          </q-card-section>

          <q-card-actions class="button-group">
            <q-btn label="บันทึก" color="green" @click="saveUser" />
            <q-btn label="ยกเลิก" color="red" flat @click="showOneUserDialog = false" />
          </q-card-actions>
        </q-card>
      </q-dialog>

      <!-- ADD MANY USERS DIALOG -->
      <ManyUsersDialog />
    </q-page>
  </q-page-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useDialogOrPopupstore } from 'src/stores/dialogOrPopup';
import { useUserStore } from 'src/stores/user';
import ManyUsersDialog from 'src/components/ManyUsersDialog.vue';

const userStore = useUserStore()

const dialogOrPopupStore = useDialogOrPopupstore()

const search = ref('')
const showAddUserDialog = ref(false)
const showOneUserDialog = ref(false)

const showEditUserDialog = ref(false)
// const editedUserIndex = ref<number | null>(null)
const showDeleteDialog = ref(false)
const deleteUserIndex = ref<number | null>(null)

const firstName = ref<string>('')
const lastName = ref<string>('')
const department = ref<string>('')
const position = ref<string>('')
const email = ref<string>('')
const password = ref<string>('')
const username = ref<string>('')
const avatarFile = ref<File | null>(null)
const avatarPreview = ref<string>('')
const avatarInput = ref<HTMLInputElement | null>(null)

// Filters
const departmentFilter = ref<string>('')
const positionFilter = ref<string>('')

const departments = ['คณะวิศวกรรมศาสตร์', 'คณะวิทยาการสารสนเทศ', 'คณะบริหารธุรกิจ']
const positions = ['พนักงาน', 'ผู้จัดการ', 'ผู้บริหาร']


const columns = [
  { name: 'index', label: 'ลำดับ', field: 'index', align: 'left' as const },
  { name: 'username', label: 'Username', field: 'username', align: 'left' as const },
  { name: 'full_name', label: 'ชื่อ-นามสกุล', field: 'full_name', align: 'left' as const },
  { name: 'email', label: 'อีเมล', field: 'email', align: 'left' as const },
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  { name: 'role_name', label: 'หน้าที่', field: (row: any) => row.role?.role_name, align: 'left' as const },
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  { name: 'department_name', label: 'หน่วยงาน', field: (row: any) => row.department?.department_name, align: 'left' as const },
  { name: 'actions', label: 'Action', field: 'user_id', align: 'center' as const },
]

// Quasar table pagination: 0 means "All" rows
const pagination = ref({ page: 1, rowsPerPage: 0 })

onMounted(async () => {
  await userStore.getUsers()
})
const filteredUsers = computed(() => {
  return userStore.users.filter(
    (user) => user.full_name.includes(search.value),
  )
})


// eslint-disable-next-line @typescript-eslint/no-unused-vars
const openEditUserDialog = (user: (typeof userStore.users)[number], index: number) => {
  // userStore.editedUser = user

}

const saveEditedUser = () => {

  const formdata = new FormData()
  formdata.append('username', userStore.editedUser.username)
  formdata.append('password', userStore.editedUser.password)
  formdata.append('full_name', firstName.value + ' ' + lastName.value)
  formdata.append('role_id', '1')
  formdata.append('department_id', '1')
  formdata.append('email', userStore.editedUser.email)
  formdata.append('profile', '')

  showEditUserDialog.value = false
}

const confirmDeleteUser = (index: number) => {
  deleteUserIndex.value = index
  showDeleteDialog.value = true
}

const deleteUser = () => {
  // if (deleteUserIndex.value !== null) {
  //   users.value.splice(deleteUserIndex.value, 1) // ลบผู้ใช้จากลิสต์
  // }
  // showDeleteDialog.value = false
  // deleteUserIndex.value = null
}

const openOneUserDialog = () => {
  showAddUserDialog.value = false
  showOneUserDialog.value = true
}

const openManyUsersDialog = () => {
  showAddUserDialog.value = false
  dialogOrPopupStore.showManyUsersDialog = true
}

const saveUser = () => {
  // if (!firstName.value || !lastName.value || !department.value || !position.value) {
  //   alert('กรุณากรอกข้อมูลให้ครบถ้วน')
  //   return
  // }

  // users.value.push({
  //   name: `${firstName.value} ${lastName.value}`,
  //   department: department.value,
  //   position: position.value,
  // })

  // // รีเซ็ตค่าในฟอร์ม
  // firstName.value = ''
  // lastName.value = ''
  // department.value = ''
  // position.value = ''

  // showOneUserDialog.value = false // ปิด Dialog
}

function triggerAvatarInput() {
  avatarInput.value?.click()
}

function onAvatarSelected(event: Event) {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (!file) return
  avatarFile.value = file
  const reader = new FileReader()
  reader.onload = (event) => {
    const result = event.target?.result
    avatarPreview.value = typeof result === 'string' ? result : ''
  }
  reader.readAsDataURL(file)
}

function clearAvatar() {
  avatarFile.value = null
  avatarPreview.value = ''
  if (avatarInput.value) avatarInput.value.value = ''
}
</script>
<style scoped>
.background-image {
  background-image: url('/images/bg-night-window.png');
  /* เปลี่ยน path ให้ตรงกับรูปของคุณ */
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  min-height: 100vh;
}

.background-image::before {
  content: "";
  position: absolute;
  inset: 0;
  background-image: url('/src/assets/img/Document_BG.jpg');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center 16px;
  z-index: 0;
}

.background-image::after {
  content: "";
  position: absolute;
  inset: 0;
  background-color: rgba(17, 35, 173, 0.64);
  margin-top: 16px;
  z-index: 1;
}

.background-image>* {
  position: relative;
  z-index: 2;
  /* ✅ ข้อความอยู่บนสุด ไม่โดนจาง */

}

.transparent-card {
  background-color: transparent !important;
  box-shadow: none !important;
}

.add-user-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  gap: 24px;
}

.user-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
}

.input-row {
  display: flex;
  gap: 10px;
  align-items: center;
}

.input-row .full-width {
  flex: 1;
  /* ทำให้ทุกช่องยืดขนาดเท่ากัน */
}

.avatar-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 150px;
  height: 150px;
  border: 1px solid #ccc;
  border-radius: 10px;
  margin-left: 24px;
  position: relative;
}

.remove-avatar-btn {
  position: absolute;
  top: -10px;
  right: -10px;
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.15);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.button-group {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.wide-select {
  min-width: 220px;
}

.wide-search {
  min-width: 260px;
}

.hidden-input {
  display: none;
}

/* ==== Add User Main Selection (match screenshot) ==== */
.add-user-main-card {
  width: 1100px;
  max-width: 95vw;
  text-align: center;
  background: radial-gradient(120% 120% at 50% 0%, #ffffff 0%, #eef5ff 35%, #c7ddff 100%);
  border-radius: 10px;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.25);
  border: 1px solid #b9cdee;
  overflow: hidden;
}

.title-add-user {
  font-size: 44px;
  font-weight: 900;
  color: #0a2a74;
  text-shadow: 0 2px 0 rgba(0, 0, 0, 0.15);
}

.choice-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  justify-items: center;
}

.choice-panel {
  background: #08307e;
  width: 460px;
  height: 360px;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 26px rgba(0, 0, 0, 0.35);
}

.choice-btn {
  margin-top: 22px;
  background: #2166c5;
  color: #ffffff;
  border-radius: 8px;
  padding: 10px 24px;
  border: 1px solid #8fd14f;
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.25) inset;
}

@media (max-width: 1024px) {
  .choice-panel { width: 360px; height: 300px; }
}

@media (max-width: 820px) {
  .choice-grid { grid-template-columns: 1fr; gap: 24px; }
  .add-user-main-card { width: 95vw; }
}
</style>
