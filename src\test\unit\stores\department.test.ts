import { describe, it, expect, vi, beforeEach } from 'vitest'
import { setActiveP<PERSON>, createPinia } from 'pinia'
import { useDepartmentstore } from 'src/stores/department'
import departmentService from 'src/services/department'
import type { Department } from 'src/types/Department'
import { createAxiosResponse } from 'src/test/test-utils'

// Mock the department service
vi.mock('src/services/department')
const mockedDepartmentService = vi.mocked(departmentService)

describe('Department Store', () => {
  let departmentStore: ReturnType<typeof useDepartmentstore>

  const mockDepartment: Department = {
    department_id: 1,
    department_name: 'คณะวิศวกรรมศาสตร์',
  }

  const mockDepartments: Department[] = [
    mockDepartment,
    { department_id: 2, department_name: 'คณะวิทยาการสารสนเทศ' },
    { department_id: 3, department_name: 'คณะบริหารธุรกิจ' },
  ]

  beforeEach(() => {
    setActivePinia(createPinia())
    departmentStore = useDepartmentstore()
    vi.clearAllMocks()
  })

  describe('initial state', () => {
    it('should have correct initial state', () => {
      expect(departmentStore.departments).toBeDefined()
      expect(Array.isArray(departmentStore.departments)).toBe(true)
      expect(departmentStore.departments).toHaveLength(0)

      expect(departmentStore.initialdepartment).toBeDefined()
      expect(departmentStore.initialdepartment.department_id).toBe(0)
      expect(departmentStore.initialdepartment.department_name).toBe('')

      expect(departmentStore.editeddepartment).toBeDefined()
      expect(departmentStore.editeddepartment.department_id).toBe(0)
      expect(departmentStore.editeddepartment.department_name).toBe('')
    })

    it('should validate initial department structure', () => {
      const { initialdepartment } = departmentStore

      expect(typeof initialdepartment.department_id).toBe('number')
      expect(typeof initialdepartment.department_name).toBe('string')
    })

    it('should have editeddepartment as deep copy of initialdepartment', () => {
      expect(departmentStore.editeddepartment).toEqual(departmentStore.initialdepartment)
      expect(departmentStore.editeddepartment).not.toBe(departmentStore.initialdepartment)
    })
  })

  describe('getDepartment action', () => {
    it('should fetch department successfully', async () => {
      const mockResponse = createAxiosResponse(mockDepartment)
      mockedDepartmentService.getDepartment.mockResolvedValue(mockResponse)

      await departmentStore.getDepartment(1)

      expect(mockedDepartmentService.getDepartment).toHaveBeenCalledWith(1)
      expect(mockedDepartmentService.getDepartment).toHaveBeenCalledTimes(1)
    })

    it('should handle different department IDs', async () => {
      const mockResponse = createAxiosResponse(mockDepartments[1])
      mockedDepartmentService.getDepartment.mockResolvedValue(mockResponse)

      await departmentStore.getDepartment(2)

      expect(mockedDepartmentService.getDepartment).toHaveBeenCalledWith(2)
    })

    it('should handle error when fetching department', async () => {
      const mockError = new Error('Department not found')
      mockedDepartmentService.getDepartment.mockRejectedValue(mockError)

      await expect(departmentStore.getDepartment(999)).rejects.toThrow('Department not found')
      expect(mockedDepartmentService.getDepartment).toHaveBeenCalledWith(999)
    })
  })

  describe('getDepartments action', () => {
    it('should fetch all departments successfully', async () => {
      const mockResponse = createAxiosResponse(mockDepartments)
      mockedDepartmentService.getDepartments.mockResolvedValue(mockResponse)

      await departmentStore.getDepartments()

      expect(mockedDepartmentService.getDepartments).toHaveBeenCalledTimes(1)
      expect(departmentStore.departments).toEqual(mockDepartments)
    })

    it('should handle error when fetching departments', async () => {
      const mockError = new Error('Failed to fetch departments')
      mockedDepartmentService.getDepartments.mockRejectedValue(mockError)

      // Mock console.error to avoid error output in tests
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      await departmentStore.getDepartments()

      expect(mockedDepartmentService.getDepartments).toHaveBeenCalledTimes(1)
      expect(consoleSpy).toHaveBeenCalledWith('Error fetching user:', mockError)

      consoleSpy.mockRestore()
    })

    it('should handle empty departments response', async () => {
      const mockResponse = createAxiosResponse([])
      mockedDepartmentService.getDepartments.mockResolvedValue(mockResponse)

      await departmentStore.getDepartments()

      expect(departmentStore.departments).toEqual([])
      expect(departmentStore.departments).toHaveLength(0)
    })
  })

  describe('store structure', () => {
    it('should export correct properties and methods', () => {
      expect(departmentStore).toHaveProperty('departments')
      expect(departmentStore).toHaveProperty('initialdepartment')
      expect(departmentStore).toHaveProperty('editeddepartment')
      expect(departmentStore).toHaveProperty('getDepartment')
      expect(departmentStore).toHaveProperty('getDepartments')

      expect(typeof departmentStore.getDepartment).toBe('function')
      expect(typeof departmentStore.getDepartments).toBe('function')
    })

    it('should have reactive state', () => {
      expect(departmentStore.departments).toBeDefined()
      expect(departmentStore.initialdepartment).toBeDefined()
      expect(departmentStore.editeddepartment).toBeDefined()
    })
  })

  describe('state mutations', () => {
    it('should update departments when getDepartments is called', async () => {
      const mockResponse = createAxiosResponse(mockDepartments)
      mockedDepartmentService.getDepartments.mockResolvedValue(mockResponse)

      expect(departmentStore.departments).toHaveLength(0)

      await departmentStore.getDepartments()

      expect(departmentStore.departments).toHaveLength(3)
      expect(departmentStore.departments[0]?.department_name).toBe('คณะวิศวกรรมศาสตร์')
      expect(departmentStore.departments[1]?.department_name).toBe('คณะวิทยาการสารสนเทศ')
    })

    it('should update editeddepartment when getDepartment is called', async () => {
      const mockResponse = createAxiosResponse(mockDepartment)
      mockedDepartmentService.getDepartment.mockResolvedValue(mockResponse)

      expect(departmentStore.editeddepartment.department_id).toBe(0)

      await departmentStore.getDepartment(1)

      expect(departmentStore.editeddepartment).toEqual(mockDepartment)
    })
  })

  describe('data validation', () => {
    it('should maintain correct data types', async () => {
      const mockResponse = createAxiosResponse(mockDepartments)
      mockedDepartmentService.getDepartments.mockResolvedValue(mockResponse)

      await departmentStore.getDepartments()

      departmentStore.departments.forEach((department) => {
        expect(typeof department.department_id).toBe('number')
        expect(typeof department.department_name).toBe('string')
      })
    })
  })
})
