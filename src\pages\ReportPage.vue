<template>
  <q-page class="q-pa-md bg-grey-10 text-white">
    <!-- Header -->
    <div class="text-h4 text-weight-bold q-mb-xs">รายงานเอกสาร</div>
    <div class="text-subtitle2 q-mb-lg">
      แดชบอร์ดสรุปภาพรวมเอกสาร พร้อมกรองตามประเภท ส่วนงาน สถานะ และเดือน/ปี
    </div>

    <!-- Filters -->
    <div class="row q-col-gutter-md q-mb-md">
      <div class="col-12 col-md-2">
        <q-select v-model="filters.month" :options="monthOptions" label="เดือน" dense outlined dark emit-value
          map-options />
      </div>
      <div class="col-12 col-md-2">
        <q-select v-model="filters.year" :options="yearOptions" label="ปี" dense outlined dark emit-value map-options />
      </div>
      <div class="col-12 col-md-3">
        <q-select v-model="filters.department" :options="deptOptions" label="ส่วนงาน" dense outlined dark emit-value
          map-options />
      </div>
      <div class="col-12 col-md-3">
        <q-select v-model="filters.category" :options="categoryOptions" label="ประเภทเอกสาร" dense outlined dark
          emit-value map-options />
      </div>
      <div class="col-12 col-md-2">
        <q-select v-model="filters.status" :options="statusOptions" label="สถานะเอกสาร" dense outlined dark emit-value
          map-options />
      </div>
      <div class="col-12 col-md-4">
        <q-input v-model="filters.query" label="ค้นหาชื่อเอกสาร" dense outlined dark clearable debounce="300" />
      </div>
    </div>

    <!-- KPI Cards -->
    <div class="row q-col-gutter-md q-mb-md">
      <div class="col-12 col-md-4">
        <q-card flat bordered class="bg-grey-9 text-white">
          <q-card-section class="row items-center no-wrap">
            <q-icon name="description" size="28px" class="q-mr-md" />
            <div class="col">
              <div class="text-subtitle1">เอกสารทั้งหมด</div>
              <div class="text-h5 text-weight-bold">{{ kpis.total }}</div>
              <div class="text-caption text-positive">
                เติบโตเทียบปีก่อน {{ growthText }}
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
      <div class="col-12 col-md-4">
        <q-card flat bordered class="bg-grey-9 text-white">
          <q-card-section class="row items-center no-wrap">
            <q-icon name="task_alt" size="28px" class="q-mr-md" />
            <div class="col">
              <div class="text-subtitle1">ใช้งานอยู่</div>
              <div class="text-h5 text-weight-bold">{{ kpis.active }}</div>
            </div>
          </q-card-section>
        </q-card>
      </div>
      <div class="col-12 col-md-4">
        <q-card flat bordered class="bg-grey-9 text-white">
          <q-card-section class="row items-center no-wrap">
            <q-icon name="cancel" size="28px" class="q-mr-md" />
            <div class="col">
              <div class="text-subtitle1">ถูกยกเลิก</div>
              <div class="text-h5 text-weight-bold">{{ kpis.inactive }}</div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- Charts -->
    <div class="row q-col-gutter-md">
      <div class="col-12 col-lg-6">
        <q-card flat bordered class="bg-grey-9 text-white">
          <q-card-section>
            <div class="text-subtitle1 text-weight-medium">
              เอกสารที่เพิ่ม vs ถูกยกเลิก (รายเดือน)
            </div>
          </q-card-section>
          <q-card-section>
            <canvas ref="barRef" height="300" class="full-width"></canvas>
          </q-card-section>
        </q-card>
      </div>
      <div class="col-12 col-lg-6">
        <div class="row q-col-gutter-md">
          <div class="col-12 col-md-6">
            <q-card flat bordered class="bg-grey-9 text-white">
              <q-card-section>
                <div class="text-subtitle1">สัดส่วนตามแผนก</div>
              </q-card-section>
              <q-card-section>
                <canvas ref="deptPieRef" height="260" class="full-width"></canvas>
              </q-card-section>
            </q-card>
          </div>
          <div class="col-12 col-md-6">
            <q-card flat bordered class="bg-grey-9 text-white">
              <q-card-section>
                <div class="text-subtitle1">สัดส่วนตามประเภทเอกสาร</div>
              </q-card-section>
              <q-card-section>
                <canvas ref="typePieRef" height="260" class="full-width"></canvas>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </div>
    </div>

    <div class="text-caption q-mt-lg text-grey-5">
      * ตัวอย่างข้อมูลจำลอง — ต่อ API จริงภายหลังได้ (โค้ดรองรับฟิลเตอร์ครบ)
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, onBeforeUnmount } from 'vue'
import { Chart, registerables, type ChartConfiguration } from 'chart.js'
import type { Chart as ChartType } from 'chart.js'
Chart.register(...registerables)

/**
 * แปลงจาก React -> Quasar + Vue 3 + Chart.js
 * - ใช้ Mock data ชุดเดียวกับไฟล์ React (docs_report_dashboard_react.jsx)
 * - ฟิลเตอร์: ปี/เดือน/ส่วนงาน/ประเภท/สถานะ/ค้นหา
 * - กราฟแท่ง: เพิ่ม vs ยกเลิก (นับยกเลิกจาก is_active=false; ถ้าต้องแม่นยำควรมี cancelled_date)
 */

// Types
interface Document {
  doc_id: number
  doc_name: string
  content: string
  is_public: number
  created_date: string
  previous: number | null
  department_id: number
  category_id: number
  is_active: boolean
  views: number
}

interface FilterOption {
  label: string
  value: string | number
}

// Lookup (เหมือน React)
const departments: Record<number, string> = {
  1: 'งานวิชาการ',
  2: 'งานบุคคล',
  3: 'งานการเงิน',
  4: 'งานพัสดุ'
}
const categories: Record<number, string> = {
  1: 'ประกาศ',
  2: 'ระเบียบ',
  3: 'ข้อบังคับ',
  4: 'หนังสือเวียน'
}

const monthNames = ['ม.ค.', 'ก.พ.', 'มี.ค.', 'เม.ย.', 'พ.ค.', 'มิ.ย.', 'ก.ค.', 'ส.ค.', 'ก.ย.', 'ต.ค.', 'พ.ย.', 'ธ.ค.']

// Mock documents (ยกมาจาก React)
function d(y: number, m: number, day: number): string {
  return new Date(y, m - 1, day).toISOString()
}
const docs = ref<Document[]>([
  { doc_id: 1, doc_name: 'หลักเกณฑ์การจ่ายค่าตอบแทนการตรวจผลงานการผลิตสื่อ', content: '...', is_public: 1, created_date: d(2024, 7, 31), previous: null, department_id: 1, category_id: 1, is_active: true, views: 312 },
  { doc_id: 2, doc_name: 'หลักเกณฑ์การจ่ายเงินอุดหนุนกิจกรรมนิสิต', content: '...', is_public: 1, created_date: d(2024, 7, 31), previous: 1, department_id: 3, category_id: 2, is_active: true, views: 188 },
  { doc_id: 3, doc_name: 'ระเบียบการจัดทำงบประมาณและการเก็บรักษาใบเสร็จรับเงิน', content: '...', is_public: 0, created_date: d(2024, 5, 30), previous: null, department_id: 3, category_id: 2, is_active: true, views: 145 },
  { doc_id: 4, doc_name: 'แนวทางเวียนแจ้งการใช้งานห้องประชุม', content: '...', is_public: 1, created_date: d(2025, 1, 12), previous: null, department_id: 4, category_id: 4, is_active: true, views: 267 },
  { doc_id: 5, doc_name: 'ข้อบังคับการใช้อุปกรณ์สำนักงาน', content: '...', is_public: 0, created_date: d(2025, 2, 3), previous: null, department_id: 2, category_id: 3, is_active: false, views: 93 },
  { doc_id: 6, doc_name: 'ประกาศแนวทางการทำงานแบบยืดหยุ่น', content: '...', is_public: 1, created_date: d(2025, 3, 14), previous: null, department_id: 2, category_id: 1, is_active: true, views: 401 },
  { doc_id: 7, doc_name: 'ระเบียบความปลอดภัยข้อมูล', content: '...', is_public: 1, created_date: d(2025, 4, 9), previous: 3, department_id: 1, category_id: 2, is_active: true, views: 520 },
  { doc_id: 8, doc_name: 'หนังสือเวียนการเบิกจ่ายออนไลน์', content: '...', is_public: 1, created_date: d(2025, 5, 21), previous: null, department_id: 3, category_id: 4, is_active: true, views: 205 },
  { doc_id: 9, doc_name: 'ข้อบังคับการลาหยุดราชการ', content: '...', is_public: 0, created_date: d(2025, 6, 6), previous: 5, department_id: 2, category_id: 3, is_active: true, views: 129 },
  { doc_id: 10, doc_name: 'ประกาศเวรยามรักษาความปลอดภัย', content: '...', is_public: 1, created_date: d(2025, 7, 28), previous: null, department_id: 4, category_id: 1, is_active: true, views: 77 },
  { doc_id: 11, doc_name: 'หนังสือเวียนตารางฝึกซ้อมอพยพหนีไฟ', content: '...', is_public: 1, created_date: d(2025, 7, 12), previous: null, department_id: 1, category_id: 4, is_active: true, views: 228 },
  { doc_id: 12, doc_name: 'ระเบียบการใช้ระบบเอกสารกลาง', content: '...', is_public: 1, created_date: d(2025, 8, 2), previous: 7, department_id: 1, category_id: 2, is_active: true, views: 650 },
  { doc_id: 13, doc_name: 'แนวทางการจัดซื้อครุภัณฑ์ประจำปี', content: '...', is_public: 0, created_date: d(2025, 8, 10), previous: null, department_id: 4, category_id: 4, is_active: true, views: 74 }
])

// ----- Filter options -----
const years = Array.from(new Set(docs.value.map(d => new Date(d.created_date).getFullYear()))).sort((a, b) => a - b)
const yearOptions: FilterOption[] = years.map(y => ({ label: String(y), value: y }))

const monthOptions: FilterOption[] = [{ label: 'ทั้งหมด', value: 'all' }].concat(
  monthNames.map((m, i) => ({ label: m, value: i + 1 }))
)

const deptOptions: FilterOption[] = [{ label: 'ทั้งหมด', value: 'all' }].concat(
  Object.entries(departments).map(([id, name]) => ({ label: name, value: id }))
)

const categoryOptions: FilterOption[] = [{ label: 'ทั้งหมด', value: 'all' }].concat(
  Object.entries(categories).map(([id, name]) => ({ label: name, value: id }))
)

const statusOptions: FilterOption[] = [
  { label: 'ทั้งหมด', value: 'all' },
  { label: 'ใช้งานอยู่', value: 'active' },
  { label: 'ถูกยกเลิก', value: 'inactive' }
]

// ----- Filters reactive -----
const filters = reactive({
  year: years[years.length - 1],
  month: 'all' as string | number,
  department: 'all' as string | number,
  category: 'all' as string | number,
  status: 'all' as string,
  query: ''
})

// ----- Helpers to match filters -----
const matchStatus = (d: Document) => (filters.status === 'all' ? true : filters.status === 'active' ? d.is_active : !d.is_active)
const matchYear = (d: Document) => (filters.year ? new Date(d.created_date).getFullYear() === Number(filters.year) : true)
const matchMonth = (d: Document) => (filters.month === 'all' ? true : new Date(d.created_date).getMonth() + 1 === Number(filters.month))
const matchDept = (d: Document) => (filters.department === 'all' ? true : String(d.department_id) === String(filters.department))
const matchCat = (d: Document) => (filters.category === 'all' ? true : String(d.category_id) === String(filters.category))
const matchQuery = (d: Document) => (filters.query.trim() ? d.doc_name.toLowerCase().includes(filters.query.trim().toLowerCase()) : true)

// View data (เคารพฟิลเตอร์ทั้งหมด รวมเดือน)
const filtered = computed(() => {
  return docs.value.filter(d => matchYear(d) && matchMonth(d) && matchDept(d) && matchCat(d) && matchStatus(d) && matchQuery(d))
})

// KPI จาก filtered + growth จากปีที่เลือกเทียบปีก่อน
const kpis = reactive({ total: 0, active: 0, inactive: 0 })
const growthText = computed(() => {
  const thisYear = docs.value.filter(d => new Date(d.created_date).getFullYear() === Number(filters.year)).length
  const lastYear = docs.value.filter(d => new Date(d.created_date).getFullYear() === Number(filters.year) - 1).length
  const growth = lastYear === 0 ? 100 : Math.round(((thisYear - lastYear) / lastYear) * 100)
  return `${growth > 0 ? '+' : ''}${growth}%`
})

// Base สำหรับกราฟรายเดือน: เคารพฟิลเตอร์ทั้งหมด ยกเว้น "เดือน"
const monthlyBase = computed(() => {
  return docs.value.filter(d => matchYear(d) && matchDept(d) && matchCat(d) && matchStatus(d) && matchQuery(d))
})

// กราฟแท่ง เพิ่ม vs ยกเลิก (ถ้าเลือก month เฉพาะ จะโชว์เฉพาะเดือนนั้น ที่เหลือเป็น 0)
const monthLabels = ['ม.ค.', 'ก.พ.', 'มี.ค.', 'เม.ย.', 'พ.ค.', 'มิ.ย.', 'ก.ค.', 'ส.ค.', 'ก.ย.', 'ต.ค.', 'พ.ย.', 'ธ.ค.']
const monthMap: Record<number, number> = { 1: 0, 2: 1, 3: 2, 4: 3, 5: 4, 6: 5, 7: 6, 8: 7, 9: 8, 10: 9, 11: 10, 12: 11 }

function getMonthlyData() {
  const added = new Array(12).fill(0)
  const cancelled = new Array(12).fill(0)
  monthlyBase.value.forEach(d => {
    const m = new Date(d.created_date).getMonth() + 1
    const idx = monthMap[m]
    if (idx !== undefined) {
      added[idx] += 1
      if (!d.is_active) cancelled[idx] += 1
    }
  })
  if (filters.month !== 'all') {
    return {
      added: added.map((v, i) => (i + 1 === Number(filters.month) ? v : 0)),
      cancelled: cancelled.map((v, i) => (i + 1 === Number(filters.month) ? v : 0))
    }
  }
  return { added, cancelled }
}

// Pie datasets
function mapCountBy(field: keyof Document, dict?: Record<number, string>) {
  const map: Record<string, number> = {}
  filtered.value.forEach(d => {
    const fieldValue = d[field]
    const key = dict && typeof fieldValue === 'number' ? (dict[fieldValue] || `รหัส ${fieldValue}`) : String(fieldValue)
    map[key] = (map[key] || 0) + 1
  })
  return { labels: Object.keys(map), values: Object.values(map) }
}

// ----- Charts -----
const barRef = ref<HTMLCanvasElement | null>(null)
const deptPieRef = ref<HTMLCanvasElement | null>(null)
const typePieRef = ref<HTMLCanvasElement | null>(null)
let barChart: ChartType | null = null
let deptPie: ChartType | null = null
let typePie: ChartType | null = null

function buildCharts() {
  if (!barRef.value || !deptPieRef.value || !typePieRef.value) return

  const { added, cancelled } = getMonthlyData()

  // Bar
  const barContext = barRef.value.getContext('2d')
  if (barContext) {
    barChart = new Chart(barContext, {
      type: 'bar',
      data: {
        labels: monthLabels,
        datasets: [
          { label: 'เพิ่ม', data: added, backgroundColor: 'rgba(33, 150, 243, 0.9)' },
          { label: 'ยกเลิก', data: cancelled, backgroundColor: 'rgba(244, 67, 54, 0.9)' }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: { legend: { labels: { color: '#fff' } } },
        scales: {
          x: { ticks: { color: '#ddd' }, grid: { color: 'rgba(255,255,255,0.06)' } },
          y: { ticks: { color: '#ddd', precision: 0 }, grid: { color: 'rgba(255,255,255,0.06)' } }
        }
      }
    })
  }

  // Dept pie
  const byDept = mapCountBy('department_id', departments)
  const deptContext = deptPieRef.value.getContext('2d')
  if (deptContext) {
    deptPie = new Chart(deptContext, {
      type: 'pie',
      data: {
        labels: byDept.labels,
        datasets: [{
          data: byDept.values, backgroundColor: [
            'rgba(76, 175, 80, 0.9)', 'rgba(33, 150, 243, 0.9)', 'rgba(255, 193, 7, 0.9)', 'rgba(156, 39, 176, 0.9)'
          ]
        }]
      },
      options: { plugins: { legend: { labels: { color: '#fff' } } }, maintainAspectRatio: false }
    })
  }

  // Type pie
  const byType = mapCountBy('category_id', categories)
  const typeContext = typePieRef.value.getContext('2d')
  if (typeContext) {
    typePie = new Chart(typeContext, {
      type: 'pie',
      data: {
        labels: byType.labels,
        datasets: [{
          data: byType.values, backgroundColor: [
            'rgba(244, 67, 54, 0.9)', 'rgba(33, 150, 243, 0.9)', 'rgba(3, 169, 244, 0.9)', 'rgba(255, 152, 0, 0.9)'
          ]
        }]
      },
      options: { plugins: { legend: { labels: { color: '#fff' } } }, maintainAspectRatio: false }
    })
  }
}

function updateCharts() {
  if (!barChart || !deptPie || !typePie) return

  const { added, cancelled } = getMonthlyData()
  // Bar
  if (barChart.data.datasets[0] && barChart.data.datasets[1]) {
    barChart.data.datasets[0].data = added
    barChart.data.datasets[1].data = cancelled
    barChart.update()
  }

  // Dept pie
  const byDept = mapCountBy('department_id', departments)
  if (deptPie.data.datasets[0]) {
    deptPie.data.labels = byDept.labels
    deptPie.data.datasets[0].data = byDept.values
    deptPie.update()
  }

  // Type pie
  const byType = mapCountBy('category_id', categories)
  if (typePie.data.datasets[0]) {
    typePie.data.labels = byType.labels
    typePie.data.datasets[0].data = byType.values
    typePie.update()
  }
}

// KPI watcher
watch(filtered, () => {
  kpis.total = filtered.value.length
  kpis.active = filtered.value.filter(d => d.is_active).length
  kpis.inactive = kpis.total - kpis.active
  updateCharts()
}, { immediate: true })

// Rebuild charts when any filter that affects base changes structure
watch(
  () => [filters.year, filters.department, filters.category, filters.status, filters.query, filters.month],
  () => updateCharts()
)

onMounted(() => {
  // เปิดโหมดมืดให้เหมือนภาพตัวอย่าง
  // Note: Quasar dark mode should be handled through Quasar's dark plugin
  buildCharts()
})

onBeforeUnmount(() => {
  barChart?.destroy()
  deptPie?.destroy()
  typePie?.destroy()
})
</script>

<style scoped>
.q-page {
  min-height: 100vh;
}

.text-weight-medium {
  font-weight: 600;
}
</style>
