import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import EssentialLink from 'src/components/EssentialLink.vue'

describe('EssentialLink Component', () => {
  const defaultProps = {
    title: 'Test Title',
    caption: 'Test Caption',
    link: 'https://example.com',
    icon: 'home'
  }

  it('should render with required props', () => {
    const wrapper = mount(EssentialLink, {
      props: defaultProps
    })

    expect(wrapper.exists()).toBe(true)
    expect(wrapper.text()).toContain('Test Title')
    expect(wrapper.text()).toContain('Test Caption')
  })

  it('should render title correctly', () => {
    const wrapper = mount(EssentialLink, {
      props: defaultProps
    })

    expect(wrapper.text()).toContain(defaultProps.title)
  })

  it('should render caption correctly', () => {
    const wrapper = mount(EssentialLink, {
      props: defaultProps
    })

    expect(wrapper.text()).toContain(defaultProps.caption)
  })

  it('should handle different props', () => {
    const customProps = {
      title: 'Custom Title',
      caption: 'Custom Caption',
      link: 'https://custom.com',
      icon: 'settings'
    }

    const wrapper = mount(EssentialLink, {
      props: customProps
    })

    expect(wrapper.text()).toContain('Custom Title')
    expect(wrapper.text()).toContain('Custom Caption')
  })

  it('should render without caption', () => {
    const propsWithoutCaption = {
      title: 'Title Only',
      link: 'https://example.com',
      icon: 'home'
    }

    const wrapper = mount(EssentialLink, {
      props: propsWithoutCaption
    })

    expect(wrapper.text()).toContain('Title Only')
    expect(wrapper.exists()).toBe(true)
  })

  it('should handle empty props gracefully', () => {
    const emptyProps = {
      title: '',
      caption: '',
      link: '',
      icon: ''
    }

    const wrapper = mount(EssentialLink, {
      props: emptyProps
    })

    expect(wrapper.exists()).toBe(true)
  })

  it('should validate component structure', () => {
    const wrapper = mount(EssentialLink, {
      props: defaultProps
    })

    // Component should exist and be a Vue component
    expect(wrapper.vm).toBeDefined()
    expect(wrapper.element).toBeDefined()
  })
})
