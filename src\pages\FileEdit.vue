<template>
  <q-page-container>
    <q-page class="q-pa-none no-scroll-page">
      <div class="outer-wrapper">
        <div class="full-page-row">
          <!-- ซ้าย -->
          <div class="left-panel">
            <div class="left-layout">

              <!-- 🔵 Top: Header + Buttons -->
              <div class="top-section">
                <div class="header-container">
                  <div class="title-section">
                    <div class="title-icon-wrapper">
                      <q-icon name="edit_document" size="36px" class="title-icon" />
                    </div>
                    <div class="title-text">
                      <h3 class="main-title">แก้ไขเอกสาร</h3>
                      <p class="subtitle">จัดการและแก้ไขเอกสารของคุณ</p>
                    </div>
                  </div>

                  <div class="action-buttons">
                    <q-btn label="สแกนไฟล์"
                      :color="documentStore.file ? (activeButton === 'scan' ? 'positive' : 'primary') : 'primary'"
                      :class="['modern-btn', 'scan-btn', { 'active-btn': activeButton === 'scan' }]"
                      :loading="isLoading" :disable="true" @click="scanFile" icon="document_scanner" no-caps unelevated>
                      <template v-slot:loading>
                        <q-spinner-dots color="white" size="20px" />
                      </template>
                      <q-tooltip v-if="!documentStore.file" class="bg-negative">
                        กรุณาเลือกไฟล์ก่อนสแกน
                      </q-tooltip>
                    </q-btn>

                    <q-btn label="พิสูจน์ตัวอักษร" :color="activeButton === 'proofread' ? 'positive' : 'secondary'"
                      :class="['modern-btn', 'proofread-btn', { 'active-btn': activeButton === 'proofread' }]"
                      @click="toggleClicked" icon="spellcheck" no-caps unelevated />

                    <q-btn label="แก้ไขข้อมูล" :color="activeButton === 'edit' ? 'positive' : 'accent'"
                      :class="['modern-btn', 'edit-btn', { 'active-btn': activeButton === 'edit' }]"
                      @click="showMetadata" icon="edit_document" no-caps unelevated />
                  </div>
                </div>
              </div>

              <!-- ⚪ Center: Upload Box -->
              <div class="center-section">
                <div class="upload-container">
                  <div :class="['modern-upload-box', { 'has-file': documentStore.filePreview }]">
                    <div class="upload-area" @dragover.prevent="handleDragOver" @dragleave.prevent="handleDragLeave"
                      @drop="handleDrop" :class="{ 'drag-active': isDragActive }"
                      @click="!documentStore.filePreview && uploadfile()">

                      <input type="file" ref="fileInputUi" @change="onFileChange" style="display: none" />

                      <!-- Empty State - No File -->
                      <div v-if="!documentStore.filePreview" class="empty-state">
                        <div class="upload-icon-container">
                          <q-icon name="cloud_upload" class="upload-main-icon" />
                          <div class="upload-pulse"></div>
                        </div>

                        <div class="upload-content">
                          <h4 class="upload-title">ลากไฟล์มาวางที่นี่</h4>
                          <p class="upload-subtitle">หรือคลิกเพื่อเลือกไฟล์จากเครื่องของคุณ</p>

                          <div class="file-types-info">
                            <div class="supported-types">
                              <div class="file-type-badge pdf-badge">
                                <q-icon name="picture_as_pdf" />
                                <span>PDF</span>
                              </div>
                              <div class="file-type-badge docx-badge">
                                <q-icon name="description" />
                                <span>DOCX</span>
                              </div>
                            </div>
                            <p class="size-limit">ขนาดไฟล์สูงสุด: <strong>10MB</strong></p>
                          </div>
                        </div>

                        <div class="upload-action">
                          <q-btn label="เลือกไฟล์" color="primary" class="select-file-btn" @click.stop="uploadfile()"
                            icon="folder_open" no-caps unelevated size="lg" />
                        </div>
                      </div>

                      <!-- File Preview State -->
                      <div v-else class="file-preview-container">
                        <div class="file-preview-header">
                          <div class="file-info">
                            <q-icon :name="isPDF ? 'picture_as_pdf' : 'description'" class="file-icon" />
                            <div class="file-details">
                              <p class="file-name">{{ documentStore.file?.name }}</p>
                              <p class="file-size">{{ formatFileSize(documentStore.file?.size) }}</p>
                            </div>
                          </div>

                          <!-- File Viewing Controls -->
                          <div class="file-controls">
                            <q-btn icon="close" flat round class="remove-file-btn" @click="removeFile" size="sm">
                              <q-tooltip>ลบไฟล์</q-tooltip>
                            </q-btn>
                          </div>
                        </div>

                        <div class="file-preview-content" :class="{ 'fullscreen-preview': isFullscreen }">
                          <div class="document-viewer-container" :style="{ transform: `scale(${zoomLevel})` }">
                            <iframe v-if="isPDF" :src="documentStore.filePreview" class="file-preview-iframe" />
                            <div v-if="isDoc" ref="docxContainer" class="docx-preview">
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                </div>
              </div>



            </div>
          </div>
          <FileWarningDialog v-model="dialogOrPopupStore.showFilewarningDialog"></FileWarningDialog>
          <!-- ขวา -->
          <div class="right-panel">

            <div class="right-content">
              <div class="content-section extract-section">
                <GifWaiting v-if="dialogOrPopupStore.showCatCoffeeGifWaiting" class="loading-overlay" />

                <div v-if="!dialogOrPopupStore.showCatCoffeeGifWaiting" class="extract-content">
                  <!-- Enhanced Page Info Bar -->
                  <div class="page-info-bar">
                    <div class="page-info-left">
                      <div class="page-counter">
                        <div class="page-icon-wrapper">
                          <q-icon name="description" class="page-icon" />
                        </div>
                        <div class="page-details">
                          <div class="page-number">หน้าที่ {{ documentStore.ocrResults[currentPageIndex]?.page || 0 }}
                          </div>
                          <div class="page-total">จาก {{ documentStore.ocrResults.length }} หน้า</div>
                        </div>
                      </div>
                    </div>

                    <div class="page-info-center">
                      <!-- Enhanced Page Navigation -->
                      <div class="page-navigation">
                        <q-btn icon="first_page" @click="goToFirstPage" :disable="currentPageIndex === 0"
                          class="nav-btn first-btn" flat round size="sm">
                          <q-tooltip>หน้าแรก</q-tooltip>
                        </q-btn>

                        <q-btn icon="chevron_left" @click="goToPreviousPage" :disable="currentPageIndex === 0"
                          class="nav-btn prev-btn" flat round size="sm">
                          <q-tooltip>หน้าก่อนหน้า</q-tooltip>
                        </q-btn>

                        <div class="page-input-wrapper">
                          <q-input v-model.number="currentPageDisplay" @keyup.enter="goToPage" @blur="goToPage"
                            class="page-input" dense outlined :min="1" :max="documentStore.ocrResults.length"
                            type="number" />
                        </div>

                        <q-btn icon="chevron_right" @click="goToNextPage"
                          :disable="currentPageIndex >= documentStore.ocrResults.length - 1" class="nav-btn next-btn"
                          flat round size="sm">
                          <q-tooltip>หน้าถัดไป</q-tooltip>
                        </q-btn>

                        <q-btn icon="last_page" @click="goToLastPage"
                          :disable="currentPageIndex >= documentStore.ocrResults.length - 1" class="nav-btn last-btn"
                          flat round size="sm">
                          <q-tooltip>หน้าสุดท้าย</q-tooltip>
                        </q-btn>
                      </div>
                    </div>

                    <div class="page-info-right">
                      <!-- Help & Status -->
                      <q-btn icon="help_outline" flat round size="sm" class="help-btn">
                        <q-tooltip class="bg-info text-white" style="font-size: 14px; max-width: 300px;">
                          <div class="tooltip-content">
                            <div class="tooltip-title">💡 คำแนะนำการใช้งาน</div>
                            <div class="tooltip-item">• ใช้ปุ่มลูกศรเพื่อเปลี่ยนหน้า</div>
                            <div class="tooltip-item">• พิมพ์หมายเลขหน้าเพื่อไปยังหน้าที่ต้องการ</div>
                            <div class="tooltip-item">• แก้ไขข้อความได้โดยตรงในตัวแก้ไข</div>
                          </div>
                        </q-tooltip>
                      </q-btn>

                      <q-btn icon="warning" color="orange" flat round size="sm" class="warning-btn">
                        <q-tooltip class="bg-orange text-white" style="font-size: 13px; max-width: 350px;">
                          <div class="tooltip-content">
                            <div class="tooltip-title">⚠️ ข้อควรระวัง</div>
                            <div class="tooltip-item">• การกด Undo ซ้ำ ๆ อาจทำให้เนื้อหาซ้ำ</div>
                            <div class="tooltip-item">• การปรับขนาดตัวอักษรอาจไม่แบ่งหน้าอัตโนมัติ</div>
                            <div class="tooltip-item">• แนะนำให้รีเฟรชหน้าหากพบปัญหา</div>
                          </div>
                        </q-tooltip>
                      </q-btn>
                    </div>
                  </div>

                  <div class="editor-container">
                    <div ref="editorRef" class="text-editor"></div>
                  </div>
                </div>
              </div>
              <div class="content-section words-section">
                <!-- Empty State -->
                <div v-if="!documentStore.showTable && !showMeta" class="empty-state-words">
                  <div class="empty-content">
                    <q-icon name="spellcheck" size="48px" class="empty-icon" />
                    <h6 class="empty-title">พร้อมตรวจสอบคำผิด</h6>
                    <p class="empty-subtitle">กดปุ่ม "พิสูจน์ตัวอักษร" เพื่อเริ่มการตรวจสอบ</p>
                  </div>
                </div>

                <!-- Words Table -->
                <div v-if="documentStore.showTable" class="words-table-container">
                  <div class="table-header">
                    <div class="legend">
                      <div class="legend-item">
                        <div class="legend-color error-color"></div>
                        <span>คำที่มีโอกาสผิด</span>
                      </div>
                    </div>
                  </div>

                  <div class="table-wrapper">
                    <q-table :rows="documentStore.wrongWords" :columns="columns" row-key="word" dense flat
                      class="words-table" :pagination="{ rowsPerPage: 0 }" virtual-scroll
                      style="max-height: calc(100vh - 400px);">
                      <template v-slot:body-cell-word="props">
                        <q-td :props="props" class="word-cell">
                          <div class="word-item">
                            <div :class="['word-indicator', props.row.suggest ? 'error-color' : 'error-word']"></div>
                            <span class="word-text">{{ props.row.word }}</span>
                          </div>
                        </q-td>
                      </template>
                      <template v-slot:body-cell-suggest="props">
                        <q-td :props="props" class="suggest-cell">
                          <span class="suggest-text">{{ props.row.suggest || '-' }}</span>
                        </q-td>
                      </template>
                    </q-table>
                  </div>
                </div>

                <!-- Metadata Section -->
                <div v-if="showMeta && !documentStore.showTable" class="metadata-container">
                  <MetaDataUpload />
                </div>
              </div>
            </div>
          </div>

        </div>

        <!-- dialog ปุ่มกด สรุปเนื้อหา -->
        <SummryDialog v-model="dialogOrPopupStore.showDialogsummry" :initial-summary="summaryText"
          :initSummaryText="summaryText" @summarize-request="summarizeWithBot"
          @update:summary="(val) => (summaryText = val)" />
        <!-- dialog ปุ่มกด ยกเลิก -->
        <CancelUploadDialog v-model="dialogOrPopupStore.showCancelDialog" />

        <!-- dialog ปุ่มกด ยืนยัน -->
        <AcceptUploadDialog v-model="dialogOrPopupStore.showConfirmDialog" />
      </div>
    </q-page>
  </q-page-container>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, ref, watch } from 'vue'
import { useDocumentstore } from 'src/stores/document'
import { useDepartmentstore } from 'src/stores/department'
import { useCategorystore } from 'src/stores/category'
// import documentService from 'src/services/document'
import CancelUploadDialog from 'src/components/CancelUploadDialog.vue'
import AcceptUploadDialog from 'src/components/AcceptUploadDialog.vue'
import MetaDataUpload from 'src/components/MetaDataUpload.vue'
import GifWaiting from 'src/components/GifWaiting.vue'
import { useDialogOrPopupstore } from 'src/stores/dialogOrPopup'
import { useAccessPermissionstore } from 'src/stores/accessPermmission'
import FileWarningDialog from 'src/components/FileWarningDialog.vue'


const isLoading = ref(false)
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const accessPermissionStore = useAccessPermissionstore()
const documentStore = useDocumentstore()
const departmentStore = useDepartmentstore()
const categoryStore = useCategorystore()
const dialogOrPopupStore = useDialogOrPopupstore()

const activeButton = ref<null | string>(null)
const showMeta = ref(false)

// Drag and drop functionality
const isDragActive = ref(false)
const fileInputUi = ref<HTMLInputElement>()
const docxContainer = ref<HTMLElement | null>(null)

// File type flags
const isPDF = ref(false)
const isDoc = ref(false)
const isFullscreen = ref(false)
const zoomLevel = ref(1)

// Page navigation
const currentPageDisplay = ref(1)



onMounted(async () => {
  if (departmentStore.departments.length === 0) {
    await departmentStore.getDepartments()
  }
  if (categoryStore.categories.length === 0) {
    await categoryStore.getCategories()
  }

  // Only load document if we have a valid document to edit
  if (documentStore.editeddocument && documentStore.editeddocument.doc_id && documentStore.editeddocument.doc_id > 0) {
    documentStore.tranferDocument = documentStore.editeddocument
    console.log("documentStore.editeddocument FileEdit: ", documentStore.editeddocument)

    try {
      // Load the PDF document and set up file preview
      await documentStore.getPdfDocument(documentStore.editeddocument.doc_id)

      // Set up file information for display
      if (documentStore.filePreview) {
        // Set file type flags based on document name or assume PDF
        const fileName = documentStore.editeddocument.doc_name || 'document.pdf'
        isPDF.value = fileName.toLowerCase().endsWith('.pdf') || !fileName.includes('.')
        isDoc.value = fileName.toLowerCase().endsWith('.docx') || fileName.toLowerCase().endsWith('.doc')

        // Create a mock file object for display purposes
        documentStore.file = {
          name: fileName,
          size: 0, // We don't have the actual file size from the server
          type: isPDF.value ? 'application/pdf' : 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        } as File
      }

      await documentStore.getContextByDocId(documentStore.editeddocument.doc_id)
      await documentStore.getAccessPermissionByDocId(documentStore.editeddocument.doc_id)
      //await accessPermissionStore.getAccessPermission(documentStore.editeddocument.doc_id)
      documentStore.ocrResults = documentStore.ocrResults.map(item => ({
        page: item.page,
        content: item.content.replace(/\\n/g, '\n').replace(/\n/g, '<br>')
      }))
      documentStore.editeddocument.created_date = convertToISO(documentStore.editeddocument.created_date)
      summaryText.value = normalizeNewlines(documentStore.editeddocument.summary)
    } catch (error) {
      console.error('Error loading document:', error)
      // Reset to empty state if there's an error loading the document
      documentStore.file = undefined
      documentStore.filePreview = ''
    }
  } else {
    // No document to edit - show empty state for new file upload
    console.log("No document to edit - showing empty upload state")
    documentStore.file = undefined
    documentStore.filePreview = ''
    documentStore.ocrResults = []
  }
})

function normalizeNewlines(text: string) {
  if (!text) return ''

  text = text.replace(/\\r\\n|\\r|\\n/g, '\n')  // handle escape cases
  text = text.replace(/\r\n|\r/g, '\n')        // handle raw text cases
  return text
}

function convertToISO(buddhistDate: string): string {
  const [day, month, yearBE] = buddhistDate.split('/').map(Number);
  if (!day || !month || !yearBE) return buddhistDate
  const yearAD = yearBE - 543;
  return `${yearAD}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
}

// เช็คว่าไฟล์ที่เลือกเป็นประเภท image หรือไม่
//const isImage = ref(false)
// isPDF and isDoc are already declared above

// ฟังก์ชันที่ทำงานเมื่อเลือกไฟล์

const onFileChange = (event: Event) => {
  const input = event.target as HTMLInputElement
  console.log('ไฟล์ผ่าน onFileCharge compute function', input.files?.[0])
  //isImage.value = false
  isPDF.value = false
  isDoc.value = false
  const maxSizeMB = 10
  const maxSizeBytes = maxSizeMB * 1024 * 1024

  if (input) {
    const selectedFile = input.files?.[0] // ดึงไฟล์แรกจาก FileList

    if (selectedFile) {
      documentStore.file = selectedFile as unknown as File

      // สร้าง URL สำหรับการแสดงตัวอย่าง
      documentStore.filePreview = URL.createObjectURL(selectedFile)

      const isSupportedType =
        selectedFile.type === 'application/pdf' ||
        selectedFile.name.endsWith('.docx') ||
        selectedFile.name.endsWith('.doc')

      if (!isSupportedType) {
        window.alert(`กรุณาอัปโหลดเฉพาะไฟล์ .doc, .docx หรือ .pdf เท่านั้น`)
        documentStore.resetFile()
      } else if (selectedFile.size > maxSizeBytes) {
        dialogOrPopupStore.showFilewarningDialog = true
        documentStore.resetFile()
      }
      // เช็คประเภทของไฟล์
      //isImage.value = selectedFile.type.startsWith('image/')
      isPDF.value = selectedFile.type === 'application/pdf'
      isDoc.value = selectedFile.name.endsWith('.docx') || selectedFile.name.endsWith('.doc')

      if (documentStore.file instanceof Blob) {
        // สมมติชื่อไฟล์
        const filename = selectedFile.name // ตั้งชื่อไฟล์ตามต้องการ
        const fileType = selectedFile.type // ตั้งประเภทไฟล์ตามต้องการ

        // แปลง Blob เป็น File
        const fileToAppend = new File([documentStore.file], filename, { type: fileType })
        //console.log('create form: ', fileToAppend)
        // เพิ่มไฟล์ลงใน FormData
        documentStore.pdfForm.append('file', fileToAppend)

        console.log('File appended to FormData:', documentStore.pdfForm)
        for (const [key, value] of documentStore.pdfForm.entries()) {
          console.log(key, value)
        }
      } else {
        //console.error('file.value is not a Blob:', file.value)
      }
      //console.log('pdf in store: ', document.pdfForm)
    }
  }
}



const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  isDragActive.value = false

  const files = event.dataTransfer?.files
  if (files && files.length) {
    const selectedFile = files[0]

    // สร้าง event ปลอมให้ตรงกับรูปแบบของ onFileChange
    const fakeEvent = {
      target: { files: [selectedFile] },
    } as unknown as Event // แปลงเป็น Event โดยใช้ unknown ก่อน

    // เรียก onFileChange
    onFileChange(fakeEvent)
  }
}

// Drag and drop handlers
const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
  isDragActive.value = true
}

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault()
  isDragActive.value = false
}

// File upload function
const uploadfile = () => {
  fileInputUi.value?.click()
}

// Remove file function
const removeFile = () => {
  documentStore.file = undefined
  documentStore.filePreview = ''
  if (fileInputUi.value) {
    fileInputUi.value.value = ''
  }
  isPDF.value = false
  isDoc.value = false
}

// Format file size function
const formatFileSize = (bytes?: number): string => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const columns: {
  name: string
  label: string
  field: string
  align: 'left' | 'right' | 'center'
}[] = [
    { name: 'word', label: 'คำผิด', align: 'left', field: 'word' },
    { name: 'suggest', label: 'ควรแก้เป็น', align: 'left', field: 'suggest' },
  ]

const summaryText = ref<string>('')
watch(summaryText, (newVal) => {
  documentStore.editeddocument.summary = newVal
  summaryText.value = newVal
})
const summarizeWithBot = async () => {
  if (!documentStore.ocrResults.length || !editorInstance || !currentPage.value) return

  const currentContent = editorInstance.getContents(false)
  currentPage.value.content = currentContent

  const payload: { page: number; content: string }[] = documentStore.ocrResults.map((page, idx) => ({
    page: page.page ?? idx + 1,
    content: convertHtmlToPlainText(page.content),
  }))

  try {
    const result = await documentStore.createdSummary(payload)
    summaryText.value = result.summary
  } catch (error) {
    console.error('Error creating summary:', error)
  }
}


const toggleClicked = async () => {
  console.log("documentStore.ocrResults in พิสูจน์: ", documentStore.ocrResults)
  currentPageIndex.value = 0
  refreshEditor()
  if (!documentStore.ocrResults.length || !editorInstance || !currentPage.value) return

  // บันทึกเนื้อหาหน้าปัจจุบันก่อน
  const currentContent = editorInstance.getContents(false)
  currentPage.value.content = currentContent

  const payload: { page: number; content: string }[] = documentStore.ocrResults.map((page, idx) => ({
    page: page.page ?? idx + 1,
    content: page.content,
  }))

  try {
    const results = await documentStore.spellCheck(payload)

    documentStore.ocrResults = results.map(r => ({
      page: r.page,
      content: highlightWrongWords(r.content)
    }))

    documentStore.wrongWords = results.flatMap(r =>
      r.wordlist.map(w => ({ page: r.page, word: w.word, suggest: w.suggest }))
    )

    documentStore.showTable = true
    activeButton.value = 'proofread'
  } catch (err) {
    console.error('Spellcheck batch failed:', err)
  }
}

const showMetadata = () => {
  if (!documentStore.ocrResults || documentStore.ocrResults.length === 0) return
  showMeta.value = true
  documentStore.showTable = false
  activeButton.value = 'edit'
}

const scanFile = async () => {
  if (!documentStore.file) return //
  activeButton.value = 'scan'
  isLoading.value = true
  try {
    if (!documentStore.file) return
    // await new Promise((resolve) => setTimeout(resolve, 2000))
    //ถ้า error แก้ไขเป็นใช้ documentService แต่จะไม่มี gif แสดง
    const response = await documentStore.saveFile(documentStore.pdfForm)

    isLoading.value = false


    if (response) {
      documentStore.ocrResults = response
      console.log("documentStore.ocrResults.value in function: ", documentStore.ocrResults)
      documentStore.pdfForm = new FormData()
    }
    console.log("response.ocr_result: ", response)
    console.log("documentStore.documentStore.ocrResults: ", documentStore.ocrResults)
    //toggleClicked()
  } catch (error) {
    console.error('Failed to upload file:', error)
    //use for test
    // documentStore.wrongWords = [
    //   { word: 'มหาวิทยาลัยบูรพา', suggest: 'มหาวิทยาลัยบูรพา' },     // คำถูกแต่ใช้สำหรับเทส
    //   { word: 'คณะวิทยาการสารสนเทศ', suggest: 'คณะวิทยาศาสตร์' },
    //   { word: 'บริการวิชาการ', suggest: 'บริการเชิงวิชาการ' },
    //   { word: '๒๕๖๒', suggest: '2562' },
    //   { word: 'อธิการบดี', suggest: 'ผู้บริหารสูงสุด' },
    // ]
  }
}

//Sun editor
import SunEditor from 'suneditor'
import type Editor from 'suneditor/src/lib/core'
import 'suneditor/dist/css/suneditor.min.css'
import SummryDialog from 'src/components/SummryDialog.vue'
import {
  font,
  paragraphStyle,
  blockquote,
  fontColor,
  hiliteColor,
  textStyle,
  list,
  align,
  horizontalRule,
  link,
  lineHeight,
} from 'suneditor/src/plugins'
import fontSize from 'suneditor/src/plugins/submenu/fontSize'
import formatBlock from 'suneditor/src/plugins/submenu/formatBlock'
import math from 'suneditor/src/plugins/dialog/math'
import 'suneditor/dist/css/suneditor.min.css'
import katex from 'katex'
import 'katex/dist/katex.min.css'


const currentPageIndex = ref(0)
const editorRef = ref<HTMLElement | null>(null)
let editorInstance: Editor | null = null
const currentPage = computed(() => documentStore.ocrResults[currentPageIndex.value])

// Watch for currentPageIndex changes to update display
watch(currentPageIndex, (newIndex) => {
  currentPageDisplay.value = newIndex + 1
})
const plugins = [
  font,
  fontSize,
  formatBlock,
  paragraphStyle,
  blockquote,
  fontColor,
  hiliteColor,
  textStyle,
  list,
  align,
  horizontalRule,
  lineHeight,
  link,
  math
]
interface PageContent {
  page: number
  content: string
}


watch(
  () => documentStore.ocrResults,
  async (newVal) => {
    await nextTick()

    if (!editorRef.value) return

    // ถ้ามี editor เดิมให้ destroy ก่อน
    if (editorInstance) {
      editorInstance.destroy()
      editorInstance = null
    }



    const initialContent =
      newVal.length > 0
        ? convertPlainTextToHtml(newVal[0]?.content || '')
        : '<p>เพื่อความสะดวกของท่าน กรุณากดปุ่มสแกนไฟล์</p>'

    editorInstance = SunEditor.create(editorRef.value, {
      plugins: plugins,
      katex: katex,
      height: '65vh',
      width: '100%',
      buttonList: [
        ['undo', 'redo'],
        ['font', 'fontSize', 'formatBlock'],
        ['bold', 'underline', 'italic', 'strike', 'subscript', 'superscript'],
        ['paragraphStyle', 'blockquote'],
        ['fontColor', 'hiliteColor', 'textStyle'],
        ['removeFormat'],
        ['list', 'align', 'horizontalRule', 'lineHeight'],
        ['link', 'math']
      ],
      font: [
        'Cordia New', 'Sarabun', 'TH Sarabun New', 'Angsana New'
      ],
      defaultTag: 'div',
      value: initialContent,
    })

    editorInstance.onChange = (contents: string): void => {
      if (!currentPage.value || !editorInstance) return
      currentPage.value.content = contents

      const wysiwyg = editorInstance.core.context.element.wysiwyg
      if (wysiwyg.scrollHeight > 900) {
        void (async () => {
          await splitPage()
        })()
      }
    }

    editorInstance.core.context.element.wysiwyg.addEventListener(
      'keydown',
      handleKeyDown as EventListener,
    )
  },
  { immediate: true },
)


//const test = ref<{ page: number, content: string }[]>([])

function highlightWrongWords(text: string): string {
  // eslint-disable-next-line no-useless-escape
  const regex = /(\s*)\+\-(.+?)\-\+(\s*)/g
  return text.replace(regex, (_, leading, word, trailing) => {
    const safeLeading = leading !== ' ' ? '' : ' '
    const safeTrailing = trailing !== ' ' ? '' : ' '
    return `<span style="color: red; font-weight: bold; display: inline;">${safeLeading}${word}${safeTrailing}</span>`
  })
}


function convertHtmlToPlainText(html: string): string {
  let result = html.replace(/&nbsp;/g, ' ')
  result = result.replace(/<br\s*\/?>(\n)?/gi, '\n')
  result = result.replace(/<p[^>]*>(.*?)<\/p>/gi, '$1\n')
  result = result.replace(/<div[^>]*>|<\/div>/gi, '')
  result = result.replace(/<[^>]+>/g, '')
  return result.trim()
}

function refreshEditor() {
  if (!editorInstance) return
  const page = documentStore.ocrResults[currentPageIndex.value]
  editorInstance.setContents(page?.content || '')
}

function handleKeyDown(e: KeyboardEvent) {
  if (!editorInstance) return

  // ✅ ตรวจว่ากด Backspace ที่ต้นหน้า
  if (e.key === 'Backspace' && isCursorNearTop() && currentPageIndex.value > 0) {
    e.preventDefault()

    const currentContent = editorInstance.getContents(false)
    const previousPage = documentStore.ocrResults[currentPageIndex.value - 1]

    // รวมข้อความเข้าหน้าก่อน
    if (!previousPage) return
    previousPage.content += currentContent

    // ลบหน้าปัจจุบัน
    documentStore.ocrResults.splice(currentPageIndex.value, 1)

    // ย้าย index กลับไปยังหน้าเดิม
    currentPageIndex.value--

    updatePageNumbers()
    refreshEditor()
  }

  // ✅ ตรวจว่าเกินขอบเขตความสูง → แยกหน้า
  if ((e.key === ' ' || e.key === 'Enter') && isEditorOverflowing()) {
    e.preventDefault()

    const fullHtml = editorInstance.getContents(false)
    const fullText = convertHtmlToPlainText(fullHtml)

    const mid = Math.floor(fullText.length * 0.6) // แบ่งคร่าว ๆ
    const left = fullText.slice(0, mid)
    const right = fullText.slice(mid)

    // อัปเดตหน้าเดิมด้วย left
    if (currentPage.value) {
      currentPage.value.content = convertPlainTextToHtml(left)
    }

    // เพิ่มหน้าใหม่ด้วย right
    documentStore.ocrResults.splice(currentPageIndex.value + 1, 0, {
      page: 0,
      content: convertPlainTextToHtml(right),
    })

    updatePageNumbers()
    currentPageIndex.value++
    refreshEditor()
  }
}

function convertPlainTextToHtml(plainText: string): string {
  if (plainText == '') return 'เพื่อความสะดวกของท่าน กรุณากดปุ่มสแกนไฟล์'
  return plainText
    .replace(/  +/g, (spaces) => '&nbsp;'.repeat(spaces.length))
    .split('\n')
    .map((line) => (line.trim() === '' ? '<br>' : `<p>${line}</p>`))
    .join('')
}

function updatePageNumbers() {
  documentStore.ocrResults.forEach((p, i) => (p.page = i + 1))
}

function isEditorOverflowing(): boolean {
  if (!editorInstance) return false
  const wysiwyg = editorInstance.core.context.element.wysiwyg as HTMLElement
  return wysiwyg.scrollHeight > 800
}

function isCursorNearTop(): boolean {
  const sel = window.getSelection()
  if (!sel || sel.rangeCount === 0) return false
  const range = sel.getRangeAt(0)
  const node = range.startContainer
  const el = node instanceof Text ? node.parentElement : node
  if (!(el instanceof HTMLElement)) return false
  return el.offsetTop < 20
}

async function splitPage() {
  if (!editorInstance) return
  const html = editorInstance.getContents(false)
  const plainText = convertHtmlToPlainText(html)
  const splitIndex = Math.floor(plainText.length * 0.6) // split 60%
  const left = plainText.slice(0, splitIndex)
  const right = plainText.slice(splitIndex)

  // อัปเดตหน้าเดิมด้วยข้อความใหม่
  if (!currentPage.value) return
  currentPage.value.content = convertPlainTextToHtml(left)

  // แทรกหน้าใหม่พร้อมเนื้อหา
  const newPage: PageContent = {
    page: 0, // จะอัปเดตเลขทีหลัง
    content: convertPlainTextToHtml(right),
  }
  documentStore.ocrResults.splice(currentPageIndex.value + 1, 0, newPage)

  // เปลี่ยนไปหน้าใหม่และโหลดเนื้อหา
  currentPageIndex.value++
  updatePageNumbers()
  await nextTick(() => {
    editorInstance?.setContents(newPage.content)
  })
}

function goToPreviousPage() {
  if (currentPageIndex.value <= 0 || !editorInstance) return
  if (currentPage.value) {
    currentPage.value.content = editorInstance.getContents(false)
  }
  currentPageIndex.value--
  refreshEditor()
}

function goToNextPage() {
  if (currentPageIndex.value >= documentStore.ocrResults.length - 1 || !editorInstance) return
  if (currentPage.value) {
    currentPage.value.content = editorInstance.getContents(false)
  }
  currentPageIndex.value++
  refreshEditor()
}

function goToFirstPage() {
  if (currentPageIndex.value <= 0 || !editorInstance) return
  if (currentPage.value) {
    currentPage.value.content = editorInstance.getContents(false)
  }
  currentPageIndex.value = 0
  refreshEditor()
}

function goToLastPage() {
  if (currentPageIndex.value >= documentStore.ocrResults.length - 1 || !editorInstance) return
  if (currentPage.value) {
    currentPage.value.content = editorInstance.getContents(false)
  }
  currentPageIndex.value = documentStore.ocrResults.length - 1
  refreshEditor()
}

function goToPage() {
  const targetPage = currentPageDisplay.value - 1
  if (targetPage < 0 || targetPage >= documentStore.ocrResults.length || !editorInstance) {
    currentPageDisplay.value = currentPageIndex.value + 1
    return
  }
  if (currentPage.value) {
    currentPage.value.content = editorInstance.getContents(false)
  }
  currentPageIndex.value = targetPage
  refreshEditor()
}

</script>
<style scoped>
.no-scroll-page {
  height: 80vh;
  overflow: hidden;
}

.outer-wrapper {
  background: white;
  padding: 16px;
  height: 100%;
  box-sizing: border-box;
}

.full-page-row {
  display: flex;
  height: 100%;
  overflow: hidden;
  gap: 16px;
  /* ✅ เพิ่มระยะห่างตรงกลาง */
}

.left-panel {
  background-image: url('/src/assets/img/Upload_bg.png');
  border-radius: 20px;
  background-size: cover;
  background-position: center;
  display: flex;
  justify-content: center;
  align-items: stretch;
  width: 40%;
  max-width: 640px;
  min-height: 0;
  overflow: hidden;
}

.left-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
  gap: 12px;
  min-height: 0;
  overflow: hidden;
  border-radius: 20px;
}

/* ===== HEADER SECTION ===== */
.top-section {
  padding: 0;
  margin-bottom: 8px;
  flex-shrink: 0;
}

.header-container {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.title-section {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.title-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #032887 0%, #1976d2 100%);
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(3, 40, 135, 0.3);
}

.title-icon {
  color: white;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  font-size: 32px;
}

.title-text {
  flex: 1;
}

.main-title {
  margin: 0;
  font-size: 22px;
  font-weight: 700;
  color: #1a1a1a;
  line-height: 1.2;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.subtitle {
  margin: 2px 0 0 0;
  font-size: 14px;
  color: #666;
  font-weight: 400;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: flex-end;
}

.modern-btn {
  min-width: 140px;
  height: 44px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
  text-transform: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.modern-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.modern-btn.active-btn {
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.3);
}

.modern-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.modern-btn:hover::before {
  left: 100%;
}

/* Old styles removed - using FileUpload2 design */


/* ===== UPLOAD SECTION ===== */
.center-section {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  padding: 0 8px;
  min-height: 0;
  overflow: hidden;
}

.upload-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 0;
}

.modern-upload-box {
  width: 95%;
  height: 100%;
  max-height: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modern-upload-box.has-file {
  width: 100%;
  height: 100%;
}

.upload-area {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.upload-area.drag-active {
  background: linear-gradient(135deg, rgba(3, 40, 135, 0.1) 0%, rgba(25, 118, 210, 0.1) 100%);
  border: 2px dashed #032887;
  transform: scale(1.02);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px;
  text-align: center;
}

.upload-icon-container {
  position: relative;
  margin-bottom: 24px;
}

.upload-main-icon {
  font-size: 72px;
  color: #032887;
  filter: drop-shadow(0 4px 8px rgba(3, 40, 135, 0.2));
  animation: float 3s ease-in-out infinite;
}

.upload-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100px;
  height: 100px;
  border: 2px solid rgba(3, 40, 135, 0.3);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 1;
  }

  100% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
  }
}

/* Upload Content Styles */
.upload-content {
  margin-bottom: 32px;
}

.upload-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 700;
  color: #1a1a1a;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.upload-subtitle {
  margin: 0 0 24px 0;
  font-size: 16px;
  color: #666;
  font-weight: 500;
}

.file-types-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.supported-types {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.file-type-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
}

.pdf-badge {
  background: linear-gradient(135deg, #e53935 0%, #d32f2f 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(229, 57, 53, 0.3);
}

.docx-badge {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
}

.file-type-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.size-limit {
  margin: 0;
  font-size: 14px;
  color: #999;
  font-weight: 500;
}

.upload-action {
  margin-top: 16px;
}

.select-file-btn {
  min-width: 180px;
  height: 48px;
  border-radius: 24px;
  font-weight: 600;
  font-size: 16px;
  box-shadow: 0 4px 16px rgba(3, 40, 135, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.select-file-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(3, 40, 135, 0.4);
}

/* ===== FILE PREVIEW SECTION ===== */
.file-preview-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 16px;
  overflow: hidden;
}

.file-preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  flex-shrink: 0;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.file-icon {
  font-size: 32px;
  color: #032887;
}

.file-details {
  flex: 1;
}

.file-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  word-break: break-all;
}

.file-size {
  margin: 0;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.file-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.remove-file-btn {
  color: #666;
  transition: all 0.2s ease;
}

.remove-file-btn:hover {
  color: #e53935;
  background-color: rgba(229, 57, 53, 0.1);
}

.file-preview-content {
  flex: 1;
  position: relative;
  overflow: hidden;
  background: #f5f5f5;
}

.document-viewer-container {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
  transform-origin: center center;
}

.file-preview-iframe {
  width: 100%;
  height: 100%;
  border: none;
  display: block;
  background: white;
}

.docx-preview {
  width: 100%;
  height: 100%;
  overflow: auto;
  padding: 20px;
  background: white;
  box-sizing: border-box;
}

.fullscreen-preview {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: white;
}

/* ===== BOTTOM SECTION ===== */
.bottom-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60px;
  padding: 16px;
}

.upload-actions {
  text-align: center;
}

.process-btn {
  min-width: 180px;
  height: 48px;
  border-radius: 24px;
  font-weight: 600;
  font-size: 16px;
  box-shadow: 0 4px 16px rgba(3, 40, 135, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 12px;
}

.process-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(3, 40, 135, 0.4);
}

.file-support-info {
  margin: 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* พวกฝั่งขวา */
.right-panel {
  flex: 1;
  /* ✅ ขยายเต็มที่เท่าที่เหลือ */
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.right-header {
  display: flex;
  height: 65px;
  border: none;
  /* ✅ เอา border ทั่วไปออก */
  font-weight: bold;
  text-align: center;
  gap: 15px;
  /* ✅ เพิ่มระยะห่างเท่า margin ด้านล่าง */
  padding: 5px;
  /* ✅ padding ให้ content ไม่ชน */
  margin-top: 5px;
}


.half-title {
  flex: 1;
  background: white;
  border: 1px solid #555;
  box-sizing: border-box;
  margin: 5px;
  display: flex;
  justify-content: center;
  /* จัดกลางแนวนอน */
  align-items: center;
  /* จัดกลางแนวตั้ง */
  padding: 5px;
  font-weight: bold;
  text-align: center;
}

.half-title:first-child {
  flex: 2;
  /* กล่องแสดงผล Extract กว้างกว่า */
}

.half-title:last-child {
  flex: 1;
  /* กล่องคำผิดแคบกว่า */
}

.right-content {
  display: flex;
  flex: 1;
  gap: 15px;
  border: none;
  /* ✅ เอา border ทั่วไปออก */
  text-align: center;
  /* ✅ เพิ่มระยะห่างเท่า margin ด้านล่าง */
  padding: 5px;
  /* ✅ padding ให้ content ไม่ชน */
  margin-top: 5px;
}



.half-box {
  /* width: 50%; <- เอาออก */
  background: white;
  flex-direction: row;
  justify-content: center;
  /* จัดกลางแนวนอน */
  align-items: center;
  /* จัดกลางแนวตั้ง */
  flex: 1;
  margin: 5px;
  padding: 5px;
}

.half-box:first-child {
  flex: 2;
}

.half-box:last-child {
  flex: 1;
}

.page {
  position: relative;
  width: 100%;
  max-width: 794px;
  /* A4 = 210mm ≈ 794px ที่ความละเอียด 96dpi */
  aspect-ratio: 1 / 1.414;
  /* ✅ ทำให้สูงยาวตาม A4 */
  background-color: white;
  border: 1px solid #ccc;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.15);
  box-sizing: border-box;
  padding: 24px;

  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  margin: 0 auto;
  /* ✅ center horizontally */
}



.scroll-hidden {
  overflow-y: auto;
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE 10+ */
}

.scroll-hidden::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari */
}

::v-deep(.sun-editor) {
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
}

::v-deep(.sun-editor .se-wrapper) {
  white-space: pre-wrap !important;
  word-break: break-word !important;
}

::v-deep(.sun-editor .se-wrapper-wysiwyg) {
  white-space: pre-wrap !important;
  /* 🟢 รักษา \n และ space */
  word-break: break-word;
  max-width: 100%;
  width: 100%;
  box-sizing: border-box;
}



::v-deep(.sun-editor .se-wrapper-wysiwyg p) {
  margin: 0 !important;
  padding: 0 !important;
}

.subtle-hover-btn {
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.subtle-hover-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.25);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .right-content {
    flex-direction: column;
    gap: 16px;
  }

  .modern-btn {
    min-width: 120px;
    font-size: 13px;
  }

  .main-title {
    font-size: 20px;
  }

  .upload-main-icon {
    font-size: 64px;
  }

  .upload-title {
    font-size: 22px;
  }
}

@media (max-width: 768px) {
  .full-page-row {
    flex-direction: column;
    gap: 12px;
  }

  .left-panel {
    width: 100%;
    max-width: none;
    min-height: 400px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 8px;
  }

  .modern-btn {
    min-width: 100%;
    height: 40px;
  }

  .upload-main-icon {
    font-size: 56px;
  }

  .upload-title {
    font-size: 20px;
  }

  .upload-subtitle {
    font-size: 14px;
  }

  .supported-types {
    flex-direction: column;
    gap: 12px;
  }

  .file-type-badge {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .left-layout {
    padding: 12px;
    gap: 8px;
  }

  .header-container {
    padding: 8px;
  }

  .title-section {
    margin-bottom: 12px;
  }

  .main-title {
    font-size: 18px;
  }

  .subtitle {
    font-size: 13px;
  }

  .upload-main-icon {
    font-size: 48px;
  }

  .upload-title {
    font-size: 18px;
  }

  .empty-state {
    padding: 20px;
  }

  .select-file-btn {
    min-width: 160px;
    height: 44px;
    font-size: 15px;
  }
}

/* ===== NEW FILEUPLOAD2 STYLES ===== */
.content-section {
  background: white;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  min-height: 0;
  transition: all 0.3s ease;
}

.extract-section {
  flex: 2;
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
    linear-gradient(135deg, #032887, #1565c0) border-box;
}

.words-section {
  flex: 1;
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
    linear-gradient(135deg, #e91e63, #ad1457) border-box;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.extract-content {
  display: flex;
  flex-direction: column;
  height: 500px;
  padding: 20px;
}

/* Page Info Bar */
.page-info-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16px 16px 0 0;
  border-bottom: 2px solid #dee2e6;
  margin: -20px -20px 20px -20px;
}

.page-info-left {
  display: flex;
  align-items: center;
}

.page-counter {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #032887 0%, #1565c0 100%);
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(3, 40, 135, 0.3);
}

.page-icon {
  color: white;
  font-size: 20px;
}

.page-details {
  display: flex;
  flex-direction: column;
}

.page-number {
  font-size: 16px;
  font-weight: 700;
  color: #1a1a1a;
  line-height: 1.2;
}

.page-total {
  font-size: 13px;
  color: #666;
  font-weight: 500;
}

.page-info-center {
  display: flex;
  align-items: center;
}

.page-navigation {
  display: flex;
  align-items: center;
  gap: 8px;
  background: white;
  padding: 8px 12px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.nav-btn {
  color: #666;
  transition: all 0.3s ease;
}

.nav-btn:hover:not(:disabled) {
  color: #032887;
  background-color: rgba(3, 40, 135, 0.1);
  transform: scale(1.1);
}

.nav-btn:disabled {
  opacity: 0.4;
}

.page-input-wrapper {
  margin: 0 8px;
}

.page-input {
  width: 60px;
  text-align: center;
}

.page-input .q-field__control {
  height: 32px;
  border-radius: 8px;
}

.page-info-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.help-btn,
.warning-btn {
  color: #666;
  transition: all 0.3s ease;
}

.help-btn:hover {
  color: #2196f3;
  background-color: rgba(33, 150, 243, 0.1);
}

.warning-btn:hover {
  background-color: rgba(255, 152, 0, 0.1);
}

.editor-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.text-editor {
  flex: 1;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #e0e0e0;
}

/* Words Section Styles */
.empty-state-words {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px 20px;
  text-align: center;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.empty-icon {
  color: #e91e63;
  opacity: 0.7;
  animation: pulse 2s infinite;
}

.empty-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
}

.empty-subtitle {
  margin: 0;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.words-table-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px;
}

.table-header {
  margin-bottom: 16px;
}

.legend {
  display: flex;
  gap: 20px;
  justify-content: center;
  align-items: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.error-color {
  background-color: #e91e63;
}

.suggestion-color {
  background-color: #2196f3;
}

.table-wrapper {
  flex: 1;
  overflow: hidden;
}

.words-table {
  border-radius: 12px;
  overflow: hidden;
}

.word-cell,
.suggest-cell {
  padding: 8px 12px;
}

.word-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.word-indicator {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  flex-shrink: 0;
}

.error-word {
  background-color: #e91e63;
}

.word-text {
  font-weight: 500;
  color: #1a1a1a;
}

.suggest-text {
  color: #666;
  font-style: italic;
}

.metadata-container {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

@keyframes pulse {

  0%,
  100% {
    opacity: 0.7;
  }

  50% {
    opacity: 1;
  }
}

/* ===== READONLY MODE STYLES ===== */
.upload-area-readonly {
  cursor: default !important;
  pointer-events: none;
}

.upload-area-readonly .empty-state {
  opacity: 0.8;
}

.info-message {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: linear-gradient(135deg, rgba(3, 40, 135, 0.1) 0%, rgba(3, 40, 135, 0.05) 100%);
  border-radius: 12px;
  border: 1px solid rgba(3, 40, 135, 0.2);
  font-size: 14px;
  font-weight: 500;
  color: #1a1a1a;
}

.edit-mode-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 24px;
  background: linear-gradient(135deg, rgba(3, 40, 135, 0.1) 0%, rgba(3, 40, 135, 0.05) 100%);
  border-radius: 16px;
  border: 1px solid rgba(3, 40, 135, 0.2);
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  text-align: center;
  justify-content: center;
}
</style>
