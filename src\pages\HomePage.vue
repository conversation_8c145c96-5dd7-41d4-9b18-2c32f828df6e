<template>
  <q-page-container>
    <!-- Carousel บน -->
    <q-card flat bordered class="q-mb-sm q-mb-sm full-card">
      <q-carousel v-model="slide1" height="auto" animated arrows navigation control-color="primary"
        class="rounded-borders" autoplay :interval="6000" infinite>
        <q-carousel-slide v-for="(img, index) in images1" :key="index" :name="index">
          <img :src="img.src" class="carousel-image" />
        </q-carousel-slide>
      </q-carousel>
    </q-card>

    <!-- Carousel ล่าง -->
    <q-card flat bordered class="q-mb-sm q-mb-sm full-card">
      <q-carousel v-model="slide2" height="auto" animated arrows navigation control-color="primary"
        class="rounded-borders" autoplay :interval="8000" infinite>
        <q-carousel-slide v-for="(img, index) in images2" :key="index" :name="index">
          <img :src="img.src" class="carousel-image" />
        </q-carousel-slide>
      </q-carousel>
    </q-card>
    <q-btn round :icon="dialogOrPopupStore.showChatPage ? 'close' : 'chat'" color="primary" size="lg"
      class="fixed-bottom-right q-ma-md shadow-10 transition-icon" @click="toggleChat" />
  </q-page-container>
  <footer class="custom-footer">
    <q-chip class="chip" color="yellow" text-color="black" style="font-size: 12px">
      ติดต่อเรา
    </q-chip>
    <br />
    <div>
      <span style="margin-right: 65px">
        กองบริหารการศึกษา สำนักงานอธิการบดี มหาวิทยาลัยบูรพา (อาคาร ภปร) ชั้น 7
      </span>
      <span style="margin-right: 80px">
        📣 Facebook Fanpage: กองบริหารการศึกษา มหาวิทยาลัยบูรพา
      </span>
      <br />
      <span style="margin-right: 117px">
        169 ถนนลงหาดบางแสน ตำบลแสนสุข อำเภอเมือง จังหวัดชลบุรี 20131
      </span>
      <span style="margin-right: 296px">📣 Facebook: BUU Quality</span>
      <br />
      <span style="margin-right: 333px">✉ e-mail: <EMAIL></span>
      <span style="margin-right: 222px">📣 Facebook: BUU General Education</span>
    </div>
  </footer>

  <ChatBot ref="chatRef" v-model="dialogOrPopupStore.showChatPage"></ChatBot>

</template>

<script setup lang="ts">
import { useDialogOrPopupstore } from 'src/stores/dialogOrPopup'
import { ref } from 'vue'
import ChatBot from './ChatBot.vue'

const dialogOrPopupStore = useDialogOrPopupstore()
const chatRef = ref()
const slide1 = ref(0)
const slide2 = ref(0)

const toggleChat = () => {
  dialogOrPopupStore.showChatPage = !dialogOrPopupStore.showChatPage
}

const images1 = [
  { src: 'https://service.buu.ac.th/wp-content/uploads/2024/09/AUN-1-2048x564.png' },
  // { src: 'https://service.buu.ac.th/wp-content/uploads/2025/06/1-6-68-banner-2048x565.png' }
]

const images2 = [
  { src: 'https://service.buu.ac.th/wp-content/uploads/2022/11/FAQ-2048x442.png' },
  { src: 'https://service.buu.ac.th/wp-content/uploads/2023/02/Banner-BUU-Philo-2048x440.png' }
]
</script>

<style scoped>
.no-padding {
  padding: 0;
}

/* ให้ QCard เต็มความกว้างของหน้าจอ */
.full-card {
  margin-left: calc(-50.5vw + 50%);
  border-radius: 0;
}

.rounded-borders {
  border-radius: 8px;
  overflow: hidden;
}

.carousel-image {
  width: 99.1vw;
  height: auto;
  max-height: 600px;
  object-fit: contain;
  display: block;
  margin: auto;
}

.custom-footer {
  background-color: #032887;
  color: white;
  padding: 0.5rem;
  text-align: center;
  font-size: 15px;
  margin-top: 2rem;
}

.transition-icon {
  transition: transform 0.3s ease, background-color 0.3s ease;
}

.transition-icon:hover {
  transform: scale(1.1);
}

.transition-icon:active {
  transform: rotate(180deg);
}
</style>
