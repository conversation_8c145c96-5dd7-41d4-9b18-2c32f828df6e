import { describe, it, expect } from 'vitest'
import type { AccessPermission } from 'src/types/AccessPermission'
import type { Role } from 'src/types/Role'

describe('AccessPermission Type', () => {
  const mockRoles: Role[] = [
    { role_id: 1, role_name: 'Admin' },
    { role_id: 2, role_name: 'User' },
  ]

  const mockAccessPermission: AccessPermission = {
    premission_id: 1,
    doc_id: 1,
    roles: mockRoles,
  }

  it('should create a valid AccessPermission object', () => {
    expect(mockAccessPermission).toBeDefined()
    expect(mockAccessPermission.premission_id).toBe(1)
    expect(mockAccessPermission.doc_id).toBe(1)
    expect(mockAccessPermission.roles).toHaveLength(2)
  })

  it('should validate required properties', () => {
    const requiredProperties = ['premission_id', 'doc_id', 'roles']

    requiredProperties.forEach((prop) => {
      expect(mockAccessPermission).toHaveProperty(prop)
    })
  })

  it('should validate property types', () => {
    expect(typeof mockAccessPermission.premission_id).toBe('number')
    expect(typeof mockAccessPermission.doc_id).toBe('number')
    expect(Array.isArray(mockAccessPermission.roles)).toBe(true)
  })

  it('should validate roles array structure', () => {
    mockAccessPermission.roles.forEach((role) => {
      expect(role).toHaveProperty('role_id')
      expect(role).toHaveProperty('role_name')
      expect(typeof role.role_id).toBe('number')
      expect(typeof role.role_name).toBe('string')
    })
  })

  it('should handle empty roles array', () => {
    const emptyAccessPermission: AccessPermission = {
      premission_id: 0,
      doc_id: 0,
      roles: [],
    }

    expect(emptyAccessPermission).toBeDefined()
    expect(emptyAccessPermission.roles).toHaveLength(0)
    expect(Array.isArray(emptyAccessPermission.roles)).toBe(true)
  })

  it('should handle single role in array', () => {
    const singleRolePermission: AccessPermission = {
      premission_id: 2,
      doc_id: 2,
      roles: [{ role_id: 1, role_name: 'Student' }],
    }

    expect(singleRolePermission.roles).toHaveLength(1)
    expect(singleRolePermission.roles[0]?.role_name).toBe('Student')
  })

  it('should handle multiple roles in array', () => {
    const multipleRolesPermission: AccessPermission = {
      premission_id: 3,
      doc_id: 3,
      roles: [
        { role_id: 1, role_name: 'Admin' },
        { role_id: 2, role_name: 'User' },
        { role_id: 3, role_name: 'Manager' },
      ],
    }

    expect(multipleRolesPermission.roles).toHaveLength(3)
    expect(multipleRolesPermission.roles.map((r) => r.role_name)).toEqual([
      'Admin',
      'User',
      'Manager',
    ])
  })
})
