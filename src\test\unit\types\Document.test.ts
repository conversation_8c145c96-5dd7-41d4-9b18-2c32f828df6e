import { describe, it, expect } from 'vitest'
import type {
  Document,
  OcrResult,
  Spellcheck_results,
  WordList,
  PreviousResponse,
} from 'src/types/Document'
import type { Category } from 'src/types/Category'
import type { Department } from 'src/types/Department'

describe('Document Type', () => {
  const mockCategory: Category = {
    category_id: 1,
    category_name: 'ประกาศ',
  }

  const mockDepartment: Department = {
    department_id: 1,
    department_name: 'คณะวิศวกรรมศาสตร์',
  }

  const mockDocument: Document = {
    doc_id: 1,
    doc_name: 'Test Document',
    summary: 'This is a test document summary',
    is_public: 'Y',
    created_date: '2024-01-01',
    previous: 0,
    department: mockDepartment,
    category: mockCategory,
  }

  it('should create a valid Document object', () => {
    expect(mockDocument).toBeDefined()
    expect(mockDocument.doc_id).toBe(1)
    expect(mockDocument.doc_name).toBe('Test Document')
    expect(mockDocument.summary).toBe('This is a test document summary')
    expect(mockDocument.is_public).toBe('Y')
    expect(mockDocument.created_date).toBe('2024-01-01')
    expect(mockDocument.previous).toBe(0)
  })

  it('should have valid nested objects', () => {
    expect(mockDocument.department).toBeDefined()
    expect(mockDocument.category).toBeDefined()
    expect(mockDocument.department.department_id).toBe(1)
    expect(mockDocument.category.category_id).toBe(1)
  })

  it('should validate all required properties', () => {
    const requiredProperties = [
      'doc_id',
      'doc_name',
      'summary',
      'is_public',
      'created_date',
      'previous',
      'department',
      'category',
    ]

    requiredProperties.forEach((prop) => {
      expect(mockDocument).toHaveProperty(prop)
    })
  })
})

describe('OcrResult Type', () => {
  const mockOcrResult: OcrResult = {
    page: 1,
    content: 'This is OCR extracted content',
  }

  it('should create a valid OcrResult object', () => {
    expect(mockOcrResult).toBeDefined()
    expect(mockOcrResult.page).toBe(1)
    expect(mockOcrResult.content).toBe('This is OCR extracted content')
  })

  it('should validate property types', () => {
    expect(typeof mockOcrResult.page).toBe('number')
    expect(typeof mockOcrResult.content).toBe('string')
  })
})

describe('WordList Type', () => {
  const mockWordList: WordList = {
    word: 'misspelled',
    suggest: 'misspelled',
  }

  it('should create a valid WordList object', () => {
    expect(mockWordList).toBeDefined()
    expect(mockWordList.word).toBe('misspelled')
    expect(mockWordList.suggest).toBe('misspelled')
  })
})

describe('Spellcheck_results Type', () => {
  const mockWordList: WordList[] = [
    { word: 'teh', suggest: 'the' },
    { word: 'recieve', suggest: 'receive' },
  ]

  const mockSpellcheckResults: Spellcheck_results = {
    page: 1,
    content: 'This is teh content to recieve',
    wordlist: mockWordList,
  }

  it('should create a valid Spellcheck_results object', () => {
    expect(mockSpellcheckResults).toBeDefined()
    expect(mockSpellcheckResults.page).toBe(1)
    expect(mockSpellcheckResults.content).toBe('This is teh content to recieve')
    expect(mockSpellcheckResults.wordlist).toHaveLength(2)
  })

  it('should validate wordlist array', () => {
    expect(Array.isArray(mockSpellcheckResults.wordlist)).toBe(true)
    expect(mockSpellcheckResults.wordlist).toHaveLength(2)
    expect(mockSpellcheckResults.wordlist[0]?.word).toBe('teh')
    expect(mockSpellcheckResults.wordlist[0]?.suggest).toBe('the')
  })
})

describe('PreviousResponse Type', () => {
  const mockPreviousResponse: PreviousResponse = {
    previous: 5,
  }

  it('should create a valid PreviousResponse object', () => {
    expect(mockPreviousResponse).toBeDefined()
    expect(mockPreviousResponse.previous).toBe(5)
    expect(typeof mockPreviousResponse.previous).toBe('number')
  })
})
