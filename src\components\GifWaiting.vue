<script setup lang="ts">
// ✅ import gif ทั้งหมด โดยตั้งชื่อไม่ซ้ำกัน
import gif02 from '@/assets/gif_video/02.gif'
import gif03 from '@/assets/gif_video/03.gif'
import gif04 from '@/assets/gif_video/04.gif'
import gif05 from '@/assets/gif_video/05.gif'

// ✅ รวมไฟล์ gif เป็น array
const gifList = [gif02, gif03, gif04, gif05]

// ✅ สุ่ม index
const randomIndex = Math.floor(Math.random() * gifList.length)
const gifUrl = gifList[randomIndex] ?? ''
</script>

<template>
  <div class="gif-wrapper">
    <img :src="gifUrl" alt="Loading..." class="transparent-gif" />
  </div>
</template>

<style scoped>
.gif-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(217, 217, 217, 0.9);
  border-radius: 4px;
}

.transparent-gif {
  max-width: 90%;
  max-height: 90%;
}
</style>
