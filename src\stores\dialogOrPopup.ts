import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useDialogOrPopupstore = defineStore('dialogOrPopup', () => {
  const showConfirmDialog = ref(false)
  const showCancelDialog = ref(false)
  const showCatCoffeeGifWaiting = ref(false)
  const showFilewarningDialog = ref(false)
  const showDialogsummry = ref(false)
  const showCreateOrUpdate = ref(false)
  const showChatPage = ref(false)
  const showUserProfiledialog =ref(false)
  const isLoadingDialog = ref(false)
  const loadingDialogtitle = ref('')
  const showManyUsersDialog = ref(false)

  function setLoadingShowAndRename(isShow:boolean,title:string){
    isLoadingDialog.value = isShow
    loadingDialogtitle.value = title
  }
  return {
    showConfirmDialog,
    showCancelDialog,
    showCatCoffeeGifWaiting,
    showFilewarningDialog,
    showDialogsummry,
    showCreateOrUpdate,
    showChatPage,
    showUserProfiledialog,
    isLoadingDialog,
    loadingDialogtitle,
    setLoadingShowAndRename,
    showManyUsersDialog
  }
})
