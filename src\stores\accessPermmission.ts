import { defineStore } from 'pinia'
import type { AccessPermission } from 'src/types/AccessPermission'
import { ref } from 'vue'
import accessPermission from 'src/services/accessPermission'
import { useDocumentstore } from './document'
export const useAccessPermissionstore = defineStore('accessPermission', () => {
  const accessPermissions = ref<AccessPermission[]>()
  const documentStore = useDocumentstore()
  const initialAccessPermission: AccessPermission = {
    premission_id: 0,
    doc_id: 0,
    roles: [],
  }

  const editedAccessPermission = ref<AccessPermission>(
    JSON.parse(JSON.stringify(initialAccessPermission)),
  )

  async function getAccessPermission(id: number) {
    //oadingStore.doLoad()
    const res = await accessPermission.getAccessPermission(id)
    editedAccessPermission.value = res.data
    //loadingStore.finish()
  }

  async function getAccessPermissions() {
    try {
      //loadingStore.doLoad()
      const res = await accessPermission.getAccessPermissiones()
      accessPermissions.value = res.data
      //loadingStore.finish()
    } catch (e) {
      console.error('Error fetching user:', e)
      //loadingStore.finish()
    }
  }

  async function getAccessPermissionsByUser(role_id: number, department_id: number) {
    try {
      //loadingStore.doLoad()
      const res = await accessPermission.QueryPermissions(role_id, department_id)
      documentStore.documents = res.data
      //loadingStore.finish()
    } catch (e) {
      console.error('Error fetching user:', e)
      //loadingStore.finish()
    }
  }

  async function saveAccessPermission() {
    try {
      if (editedAccessPermission.value.premission_id > 0) {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const res = await accessPermission.createAccessPermByDocIdandRoleId(
          editedAccessPermission.value,
        )
      } else {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const res = await accessPermission.updateAccessPermByDocIdandRoleId(
          editedAccessPermission.value,
        )
      }
    } catch (e) {
      console.error('Error fetching user:', e)
    }
  }

  async function saveAccessPermissions(ap: AccessPermission) {
    try {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const res = await accessPermission.createAccessPermsByDocIdandRoleId(ap)
    } catch (e) {
      console.error('Error fetching user:', e)
    }
  }

  return {
    accessPermission,
    accessPermissions,
    initialAccessPermission,
    editedAccessPermission,
    getAccessPermission,
    getAccessPermissions,
    getAccessPermissionsByUser,
    saveAccessPermission,
    saveAccessPermissions,
  }
})
