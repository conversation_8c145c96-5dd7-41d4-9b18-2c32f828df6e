import type { Category } from './Category'
import type { Department } from './Department'

type Document = {
  doc_id: number
  doc_name: string
  summary: string
  is_public: string
  created_date: string
  previous: number
  department: Department
  category: Category
}
export interface OcrResult {
  page: number
  content: string
}

export interface Spellcheck_results {
  page: number
  content: string
  wordlist: WordList[]
}

export interface WordList {
  word: string
  suggest: string
}

export interface PreviousResponse {
  previous: number
}
export type { Document }
