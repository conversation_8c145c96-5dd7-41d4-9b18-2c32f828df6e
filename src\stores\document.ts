import { defineStore } from 'pinia'
import type { Document, PreviousResponse, OcrResult, Spellcheck_results } from 'src/types/Document'
import document from 'src/services/document'
import { ref } from 'vue'
import { useDialogOrPopupstore } from './dialogOrPopup'
import { useAccessPermissionstore } from './accessPermmission'

export const useDocumentstore = defineStore('document', () => {
  const dialogOrPopup = useDialogOrPopupstore()
  const accessPermmissionStore = useAccessPermissionstore()
  const documents = ref<Document[]>([])
  const initialdocument: Document = {
    doc_id: 0,
    doc_name: '',
    summary: '',
    is_public: '',
    created_date: '',
    previous: 0,
    department: {
      department_id: 0,
      department_name: '',
      //divistion_name: '', ERD does not have divistion_name
    },
    category: {
      category_id: 0,
      category_name: '',
    },
  }
  const editeddocument = ref<Document>(JSON.parse(JSON.stringify(initialdocument)))
  const tranferDocument = ref<Document>(JSON.parse(JSON.stringify(initialdocument)))
  const pdfForm = ref<FormData>(new FormData())

  const filePreview = ref<string>('')
  const fileInput = ref<HTMLInputElement | null>(null)
  const file = ref<File | undefined>(undefined)

  const showTable = ref(false)
  interface WrongWord {
    page: number
    word: string
    suggest: string
  }
  const wrongWords = ref<WrongWord[]>([])
  const ocrResults = ref<{ page: number; content: string }[]>([]) // เก็บผลการ Extract

  async function getDocument(id: number) {
    //oadingStore.doLoad()
    const res = await document.getDocument(id)
    editeddocument.value = res.data
    //loadingStore.finish()
  }

  async function getDocuments() {
    try {
      //loadingStore.doLoad()
      const res = await document.getDocuments()
      documents.value = res.data
      //loadingStore.finish()
    } catch (e) {
      console.error('Error fetching user:', e)
      //loadingStore.finish()
    }
  }

  async function saveDoc(data: FormData) {
    try {
      dialogOrPopup.showCreateOrUpdate = true
      if (editeddocument.value.doc_id < 1) {
        console.log('saveDoc')
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const res = await document.saveDoc(data)
      } else {
        //full update
        console.log('updateDoc')
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const res = await document.updateDoc(editeddocument.value, data)
      }
      //for test loading gif
      // await new Promise((resolve) => setTimeout(resolve, 5000))
      dialogOrPopup.showCreateOrUpdate = false
    } catch (e) {
      dialogOrPopup.showCreateOrUpdate = false
      console.error('Error fetching user:', e)
    }
  }

  async function deleteDoc(doc: Document, data: FormData) {
    try {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const res = await document.updateDoc(doc, data)
      await getDocuments()
    } catch (e) {
      console.error('Error fetching user:', e)
    }
  }

  async function saveFile(file: FormData): Promise<OcrResult[]> {
    try {
      dialogOrPopup.showCatCoffeeGifWaiting = true
      console.log('access save file')
      const res = await document.saveFile(file)
      dialogOrPopup.showCatCoffeeGifWaiting = false
      return res
    } catch (e) {
      console.error('Error uploading file:', e)
      dialogOrPopup.showCatCoffeeGifWaiting = false
      throw e
    }
  }

  async function spellCheck(ocrResults: OcrResult[]): Promise<Spellcheck_results[]> {
    try {
      dialogOrPopup.showCatCoffeeGifWaiting = true
      // เรียก service ด้วย batch ทีเดียว
      const res = await document.spellCheck(ocrResults)
      dialogOrPopup.showCatCoffeeGifWaiting = false
      return res
    } catch (e) {
      console.error('Error batch spellcheck:', e)
      dialogOrPopup.showCatCoffeeGifWaiting = false
      throw e
    }
  }

  async function checkPrevious(doc_name: string): Promise<PreviousResponse> {
    try {
      const res = await document.checkPrevious(doc_name)
      return res
    } catch (e) {
      console.error('Error uploading file:', e)
      throw e
    }
  }

  async function findDocId(doc: Document) {
    try {
      const res = await document.findDocId(doc)
      return res
    } catch (e) {
      console.error('Error uploading file:', e)
      throw e
    }
  }

  const createdSummary = async (text: OcrResult[]) => {
    try {
      const res = await document.createdSummary(text)
      return res
    } catch (e) {
      console.error('Error uploading file:', e)
      throw e
    }
  }

  function resetFile() {
    filePreview.value = '' // รีเซ็ตตัวอย่างไฟล์
    file.value = undefined // รีเซ็ตไฟล์ที่เลือก

    // ✅ รีเซ็ตค่า input file เพื่อให้สามารถเลือกไฟล์เดิมได้
    if (fileInput.value) {
      fileInput.value.value = ''
    }
    showTable.value = false
    ocrResults.value = []
    wrongWords.value = []
    pdfForm.value = new FormData()
    editeddocument.value = JSON.parse(JSON.stringify(initialdocument))
    tranferDocument.value = JSON.parse(JSON.stringify(initialdocument))
  }

  async function getPdfDocument(doc_id: number) {
    try {
      const res = await document.getPdfDocument(doc_id)
      filePreview.value = res
    } catch (e) {
      console.error('Error uploading file:', e)
      throw e
    }
  }

  async function getContextByDocId(doc_id: number) {
    try {
      const res = await document.getFullDocument(doc_id)
      ocrResults.value = res.data
    } catch (e) {
      console.error('Error uploading file:', e)
      throw e
    }
  }

  async function getAccessPermissionByDocId(doc_id: number) {
    try {
      const res = await document.getDocument(doc_id)
      console.log('access (res.data): ', res.data)
      accessPermmissionStore.editedAccessPermission.doc_id = res.data.doc_id
      accessPermmissionStore.editedAccessPermission.roles = res.data.access_permission
      console.log(
        'accessPermmissionStore.editedAccessPermission: ',
        accessPermmissionStore.editedAccessPermission,
      )
    } catch (e) {
      console.error('Error uploading file:', e)
      throw e
    }
  }

  async function contentSearch(
    query: string | null,
    is_public: string | null,
    access_role: string | null,
    department_id: number | null,
    category_id: number | null,
    page: number | null,
    page_size: number | null,
  ) {
    try {
      const res = await document.contentSearch(
        query,
        is_public,
        access_role,
        department_id,
        category_id,
        page,
        page_size,
      )
      return res
    } catch (e) {
      console.log(e)
    }
  }

  return {
    documents,
    initialdocument,
    editeddocument,
    tranferDocument,
    pdfForm,
    filePreview,
    file,
    fileInput,
    showTable,
    ocrResults,
    wrongWords,
    getDocument,
    getDocuments,
    saveDoc,
    deleteDoc,
    saveFile,
    spellCheck,
    checkPrevious,
    findDocId,
    resetFile,
    createdSummary,
    getPdfDocument,
    getContextByDocId,
    getAccessPermissionByDocId,
    contentSearch,
  }
})
