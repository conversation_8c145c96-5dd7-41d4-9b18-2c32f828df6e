# Test Suite Documentation

This directory contains comprehensive unit tests for the Quasar Vue.js application.

## Test Structure

```
src/test/
├── setup.ts                 # Global test setup and configuration
├── test-utils.ts            # Test utilities and helper functions
├── README.md               # This documentation
└── unit/
    ├── types/              # Type definition tests
    │   ├── User.test.ts
    │   ├── Document.test.ts
    │   ├── Category.test.ts
    │   ├── Department.test.ts
    │   ├── Role.test.ts
    │   └── AccessPermission.test.ts
    ├── services/           # Service layer tests
    │   ├── user.test.ts
    │   ├── document.test.ts
    │   ├── category.test.ts
    │   ├── department.test.ts
    │   ├── role.test.ts
    │   └── accessPermission.test.ts
    ├── stores/             # Pinia store tests
    │   ├── user.test.ts
    │   ├── document.test.ts
    │   ├── category.test.ts
    │   ├── department.test.ts
    │   ├── role.test.ts
    │   └── accessPermission.test.ts
    └── components/         # Vue component tests
        ├── EssentialLink.test.ts
        ├── GifWaiting.test.ts
        └── SidebarMenu.test.ts
```

## Running Tests

### Basic Commands

```bash
# Run all tests
npm run test:run

# Run tests in watch mode
npm run test

# Run tests with coverage
npm run test:coverage

# Run tests with UI
npm run test:ui
```

### Using the Test Script

```bash
# Run all tests
node scripts/test.js run

# Run tests in watch mode
node scripts/test.js watch

# Run with coverage
node scripts/test.js coverage

# Run specific test categories
node scripts/test.js types
node scripts/test.js services
node scripts/test.js stores
node scripts/test.js components

# Run specific test file
node scripts/test.js file src/test/unit/types/User.test.ts
```

## Test Categories

### 1. Type Tests (`src/test/unit/types/`)

Tests for TypeScript type definitions and interfaces:
- **User.test.ts**: Tests User type structure and validation
- **Document.test.ts**: Tests Document, OcrResult, Spellcheck_results, and related types
- **Category.test.ts**: Tests Category type structure
- **Department.test.ts**: Tests Department type structure
- **Role.test.ts**: Tests Role type structure
- **AccessPermission.test.ts**: Tests AccessPermission type structure

### 2. Service Tests (`src/test/unit/services/`)

Tests for API service functions with mocked axios calls:
- **user.test.ts**: Tests user CRUD operations
- **document.test.ts**: Tests document operations, file upload, OCR, spell check
- **category.test.ts**: Tests category retrieval operations
- **department.test.ts**: Tests department retrieval operations
- **role.test.ts**: Tests role retrieval operations
- **accessPermission.test.ts**: Tests access permission CRUD operations

### 3. Store Tests (`src/test/unit/stores/`)

Tests for Pinia stores with proper state management testing:
- **user.test.ts**: Tests user store state and actions
- **document.test.ts**: Tests document store with complex state management
- **category.test.ts**: Tests category store state and actions
- **department.test.ts**: Tests department store state and actions
- **role.test.ts**: Tests role store state and actions
- **accessPermission.test.ts**: Tests access permission store state and actions

### 4. Component Tests (`src/test/unit/components/`)

Tests for Vue components:
- **EssentialLink.test.ts**: Tests the EssentialLink component props and rendering
- **GifWaiting.test.ts**: Tests the GifWaiting component basic functionality
- **SidebarMenu.test.ts**: Tests the SidebarMenu component interactions and events

## Test Utilities

### `test-utils.ts`

Provides helper functions for testing:

```typescript
// Setup Pinia for store testing
setupTestPinia()

// Mock data factories
createMockUser(overrides)
createMockDocument(overrides)
createMockCategory(overrides)
createMockDepartment(overrides)
createMockRole(overrides)
createMockAccessPermission(overrides)

// Axios response helpers
createAxiosResponse(data, status)
createAxiosError(message, status)
```

### `setup.ts`

Global test configuration including:
- Axios mocking
- Quasar component stubs
- Vue Router mocking
- Global test utilities

## Testing Framework

- **Vitest**: Fast unit test framework
- **Vue Test Utils**: Vue component testing utilities
- **Happy DOM**: Lightweight DOM implementation for testing
- **Coverage**: v8 coverage provider

## Best Practices

1. **Isolation**: Each test is isolated with proper setup/teardown
2. **Mocking**: External dependencies are properly mocked
3. **Coverage**: Comprehensive test coverage for critical functionality
4. **Readability**: Clear test descriptions and organized structure
5. **Maintainability**: Reusable test utilities and consistent patterns

## Coverage Reports

Coverage reports are generated in the `coverage/` directory:
- **HTML Report**: `coverage/index.html`
- **JSON Report**: `coverage/coverage.json`
- **Text Report**: Console output

## Adding New Tests

When adding new tests:

1. Follow the existing directory structure
2. Use the test utilities from `test-utils.ts`
3. Mock external dependencies appropriately
4. Write descriptive test names and organize with `describe` blocks
5. Test both success and error scenarios
6. Maintain good test coverage

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure proper path aliases are configured in `vitest.config.ts`
2. **Component Tests**: Make sure Quasar components are properly stubbed
3. **Store Tests**: Always set up Pinia before testing stores
4. **Async Tests**: Use proper async/await patterns for asynchronous operations

### Debug Mode

Run tests with debug information:
```bash
DEBUG=1 npm run test
```

For more detailed debugging, use the UI mode:
```bash
npm run test:ui
```
