import type { User } from 'src/types/User'
import axios from 'src/boot/axios'

function getUser(id: number) {
  return axios.get(`/users/${id}`)
}

function createUser(userData: Partial<User>) {
  return axios.post('/users', userData)
}

function updateUser(id: number, formData: FormData) {
  return axios.put(`/users/${id}`, formData)
}

function deleteUser(id: number) {
  return axios.delete(`/users/${id}`)
}

function getUsers() {
  return axios.get(`/users`)
}
function createUsersByCSV(formData: FormData){
  return axios.post(`/users/upload_users`, formData)
}

export default {
  getUser,
  createUser,
  updateUser,
  deleteUser,
  getUsers,
  createUsersByCSV,
}
