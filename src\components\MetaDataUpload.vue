<template>
  <div class="metadata-form-container">
    <!-- Form Header -->
    <div class="form-header">
      <div class="header-content">
        <q-icon name="description" size="20px" class="header-icon" />
        <div class="header-text">
          <div class="form-title">ข้อมูลเอกสาร</div>
          <div class="form-subtitle">กรอกข้อมูลเมตาดาต้าของเอกสาร</div>
        </div>
      </div>
    </div>

    <!-- Form Fields -->
    <div class="form-fields">
      <!-- Document Name -->
      <div class="field-group">
        <label class="field-label">
          <q-icon name="title" size="14px" />
          ชื่อเอกสาร
        </label>
        <q-input v-model="documentStore.tranferDocument.doc_name" outlined dense class="modern-input"
          placeholder="กรอกชื่อเอกสาร" />
      </div>

      <!-- Document Category -->
      <div class="field-group">
        <label class="field-label">
          <q-icon name="category" size="14px" />
          ประเภทเอกสาร
        </label>
        <q-select v-model="documentStore.tranferDocument.category.category_name"
          :options="categoryStore.categories.map(c => ({ label: c.category_name, value: c.category_name }))"
          option-label="label" option-value="value" emit-value outlined dense class="modern-input"
          placeholder="เลือกประเภทเอกสาร" />
      </div>

      <!-- Department -->
      <div class="field-group">
        <label class="field-label">
          <q-icon name="business" size="14px" />
          หน่วยงาน
        </label>
        <q-select v-model="documentStore.tranferDocument.department.department_name"
          :options="departmentStore.departments.map(d => ({ label: d.department_name, value: d.department_name }))"
          option-label="label" option-value="value" emit-value outlined dense class="modern-input"
          placeholder="เลือกหน่วยงาน" />
      </div>

      <!-- Publish Date -->
      <div class="field-group">
        <label class="field-label">
          <q-icon name="event" size="14px" />
          วันที่ประกาศ
        </label>
        <q-input v-model="documentStore.tranferDocument.created_date" type="date" outlined dense class="modern-input" />
      </div>

      <!-- Access Permissions -->
      <div class="field-group">
        <label class="field-label">
          <q-icon name="security" size="14px" />
          ผู้มีสิทธิเข้าถึง
        </label>
        <q-select v-model="accessPermissionStore.editedAccessPermission.roles" :options="roleStore.roles"
          option-label="role_name" multiple outlined dense use-chips class="modern-input"
          placeholder="เลือกผู้มีสิทธิเข้าถึง" />
      </div>
    </div>


    <!-- Action Buttons -->
    <div class="form-actions">
      <q-btn class="summary-btn" label="การสรุปเนื้อหา" no-caps @click="showDialogsummry" icon="summarize" />

      <div class="action-buttons">
        <q-btn color="grey-6" class="action-btn cancel-btn" label="ยกเลิก" no-caps @click="showDialogcancel"
          icon="close" flat />
        <q-btn color="primary" class="action-btn confirm-btn" label="ยืนยัน" no-caps @click="showDialogconfirm"
          icon="check" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';

import { useDocumentstore } from 'src/stores/document'
import { useDialogOrPopupstore } from 'src/stores/dialogOrPopup'
import { useRolestore } from 'src/stores/role';
import { useAccessPermissionstore } from 'src/stores/accessPermmission';
import { useCategorystore } from 'src/stores/category';
import { useDepartmentstore } from 'src/stores/department';

onMounted(async () => {
  await categoryStore.getCategories()
  await departmentStore.getDepartments()
  await roleStore.getRoles()
})

const roleStore = useRolestore()
const dialogOrPopupStore = useDialogOrPopupstore()
const documentStore = useDocumentstore()
const accessPermissionStore = useAccessPermissionstore()
const categoryStore = useCategorystore()
const departmentStore = useDepartmentstore()


//const selectedTags = ref<string[]>([])
const showDialogcancel = () => {
  dialogOrPopupStore.showCancelDialog = true
}

const showDialogconfirm = () => {
  dialogOrPopupStore.showConfirmDialog = true
}

const showDialogsummry = () => {
  console.log("before set: ", dialogOrPopupStore.showDialogsummry)
  dialogOrPopupStore.showDialogsummry = true
  console.log("after set: ", dialogOrPopupStore.showDialogsummry)
}
</script>

<style scoped>
/* ===== MAIN CONTAINER ===== */
.metadata-form-container {
  max-width: 100%;
  margin: 0 auto;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(3, 40, 135, 0.1);
}

/* ===== FORM HEADER ===== */
.form-header {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 2px solid rgba(3, 40, 135, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  color: #032887;
  background: rgba(3, 40, 135, 0.1);
  padding: 8px;
  border-radius: 8px;
}

.header-text {
  flex: 1;
}

.form-title {
  font-size: 18px;
  font-weight: 700;
  color: #032887;
  margin: 0;
}

.form-subtitle {
  font-size: 13px;
  color: #64748b;
  margin: 2px 0 0 0;
}

/* ===== FORM FIELDS ===== */
.form-fields {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.field-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.field-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
}

.field-label .q-icon {
  color: #032887;
}

/* ===== MODERN INPUTS ===== */
.modern-input {
  border-radius: 8px;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.9);
}

.modern-input :deep(.q-field__control) {
  border-radius: 8px;
  background: transparent;
  min-height: 40px;
}

.modern-input :deep(.q-field__native) {
  color: #374151;
  font-size: 14px;
  padding: 8px 12px;
}

.modern-input :deep(.q-field__label) {
  color: #6b7280;
  font-size: 13px;
}

.modern-input:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(3, 40, 135, 0.1);
}

.modern-input :deep(.q-field--focused .q-field__control) {
  box-shadow: 0 0 0 2px rgba(3, 40, 135, 0.2);
}

/* ===== FORM ACTIONS ===== */
.form-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 20px;
  padding-top: 16px;
}

.summary-btn {
  background: linear-gradient(135deg, #032887, #1976d2);
  color: white;
  border-radius: 8px;
  padding: 10px 16px;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.2s ease;
  width: 100%;
}

.summary-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(3, 40, 135, 0.3);
}

.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: space-between;
}

.action-btn {
  border-radius: 8px;
  padding: 8px 20px;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.2s ease;
  flex: 1;
  min-height: 36px;
}

.cancel-btn {
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
}

.cancel-btn:hover {
  background: rgba(107, 114, 128, 0.2);
  transform: translateY(-1px);
}

.confirm-btn {
  background: #032887;
  color: white;
}

.confirm-btn:hover {
  background: #1976d2;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(3, 40, 135, 0.3);
}
</style>
