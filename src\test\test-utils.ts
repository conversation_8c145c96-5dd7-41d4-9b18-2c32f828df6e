import { create<PERSON><PERSON>, setActive<PERSON><PERSON> } from 'pinia'
import { config } from '@vue/test-utils'
import { Quasar } from 'quasar'
import { vi } from 'vitest'

// Test utilities for common test setup
export function setupTest<PERSON><PERSON>() {
  const pinia = createPinia()
  setActivePinia(pinia)
  return pinia
}

// Mock router for components that use vue-router
export const mockRouter = {
  push: vi.fn(),
  replace: vi.fn(),
  go: vi.fn(),
  back: vi.fn(),
  forward: vi.fn(),
}

export const mockRoute = {
  params: {},
  query: {},
  path: '/',
  name: 'home',
}

// Common test data factories
export const createMockUser = (overrides = {}) => ({
  user_id: 1,
  username: 'testuser',
  password: 'password123',
  full_name: 'Test User',
  role: {
    role_id: 1,
    role_name: 'Admin',
  },
  department: {
    department_id: 1,
    department_name: 'IT Department',
  },
  ...overrides,
})

export const createMockDocument = (overrides = {}) => ({
  doc_id: 1,
  doc_name: 'Test Document',
  summary: 'Test summary',
  is_public: 'Y',
  created_date: '2024-01-01',
  previous: 0,
  department: {
    department_id: 1,
    department_name: 'IT Department',
  },
  category: {
    category_id: 1,
    category_name: 'ประกาศ',
  },
  ...overrides,
})

export const createMockCategory = (overrides = {}) => ({
  category_id: 1,
  category_name: 'ประกาศ',
  ...overrides,
})

export const createMockDepartment = (overrides = {}) => ({
  department_id: 1,
  department_name: 'คณะวิศวกรรมศาสตร์',
  ...overrides,
})

export const createMockRole = (overrides = {}) => ({
  role_id: 1,
  role_name: 'Admin',
  ...overrides,
})

export const createMockAccessPermission = (overrides = {}) => ({
  premission_id: 1,
  doc_id: 1,
  roles: [
    { role_id: 1, role_name: 'Admin' },
    { role_id: 2, role_name: 'User' },
  ],
  ...overrides,
})

// Helper function to create axios mock responses
export const createAxiosResponse = (data: any, status = 200) =>
  ({
    data,
    status,
    statusText: 'OK',
    headers: {},
    config: {
      headers: {},
      method: 'get',
      url: '',
    },
  }) as any

// Helper function to create axios error
export const createAxiosError = (message: string, status = 500) => {
  const error = new Error(message)
  ;(error as any).response = {
    status,
    statusText: 'Internal Server Error',
    data: { message },
  }
  return error
}

// Global test configuration
export function setupGlobalTestConfig() {
  // Configure Vue Test Utils globally
  config.global.plugins = [Quasar]

  // Global stubs for Quasar components
  config.global.stubs = {
    QPage: true,
    QPageContainer: true,
    QCard: true,
    QCardSection: true,
    QBtn: true,
    QInput: true,
    QSelect: true,
    QTable: true,
    QDialog: true,
    QIcon: true,
    QSpace: true,
    QSeparator: true,
    QSpinner: true,
    QLinearProgress: true,
    RouterLink: true,
    RouterView: true,
  }

  // Global mocks
  config.global.mocks = {
    $router: mockRouter,
    $route: mockRoute,
  }
}

// Call setup function
setupGlobalTestConfig()
