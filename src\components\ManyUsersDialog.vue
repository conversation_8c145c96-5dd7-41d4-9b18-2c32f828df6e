<template>
  <q-dialog v-model="dialogOrPopupStore.showManyUsersDialog" :maximized="maximized" persistent transition-show="scale"
    transition-hide="scale">
    <q-card class="premium-dialog column no-wrap" :style="cardStyle">
      <!-- ===== Header ===== -->
      <q-card-section class="dialog-header row items-start">
        <div class="icon-container">
          <div class="success-icon-wrapper">
            <q-icon name="group_add" class="main-icon" />
          </div>
        </div>

        <div class="header-text">
          <h2 class="dialog-title">ADD MANY USER</h2>
          <p class="dialog-subtitle">
            อัปโหลดและตรวจสอบผู้ใช้เป็นชุดจากไฟล์ CSV / XLSX / JSON
          </p>
        </div>

        <div class="header-actions row items-center q-ml-auto">
          <q-btn flat round dense :icon="maximized ? 'fullscreen_exit' : 'fullscreen'" @click="maximized = !maximized"
            class="q-mr-xs" />
          <q-btn icon="close" flat round dense @click="close" />
        </div>
      </q-card-section>

      <q-separator />

      <!-- ===== Body ===== -->
      <q-card-section class="dialog-body q-gutter-md">
        <!-- Upload -->
        <q-file v-model="file" label="ลากไฟล์ CSV / XLSX / JSON มาวางที่นี่ หรือคลิกอัปโหลด" outlined dense
          accept=".csv,.xlsx,.json" @update:model-value="onFilePicked">
          <template #prepend><q-icon name="cloud_upload" /></template>
          <template #append>
            <q-btn v-if="file" flat dense icon="clear" @click="clearFile" />
          </template>
        </q-file>

        <!-- Schema status -->
        <div class="row items-center q-gutter-sm">
          <div class="text-body2">
            <strong>คอลัมน์บังคับ:</strong>
            <q-badge v-for="h in REQUIRED_HEADERS" :key="h" :label="h"
              :color="missingHeaders.has(h) ? 'negative' : 'positive'" class="q-ml-xs" />
          </div>
          <div v-if="unknownHeaders.length" class="text-caption text-grey-7">
            (มีคอลัมน์ส่วนเกิน: {{ unknownHeaders.join(', ') }})
          </div>
        </div>

        <!-- Errors / loaded info -->
        <div v-if="errorMsg" class="text-negative text-body2">⚠️ {{ errorMsg }}</div>
        <div v-else-if="rows.length" class="text-body2">
          โหลดข้อมูล: {{ rows.length.toLocaleString() }} แถว, {{ currentHeaders.length }} คอลัมน์
        </div>

        <!-- Preview + Edit table -->
        <q-table v-if="rows.length && missingHeaders.size === 0" :rows="rows" :columns="columns" row-key="__row_id" flat
          bordered :pagination="pagination" :rows-per-page-options="[10, 25, 50, 100]" :filter="filter"
          no-data-label="ไม่พบข้อมูลในไฟล์" virtual-scroll :virtual-scroll-item-size="56"
          :virtual-scroll-sticky-size-start="56" :wrap-cells="true" table-header-class="sticky-th" style="height: 520px"
          class="q-mt-sm" :selection="editMode ? 'multiple' : 'none'" v-model:selected="selected">
          <template #top-right>
            <div class="row items-center q-gutter-sm">
              <q-toggle v-model="editMode" color="primary" label="โหมดแก้ไข" />
              <q-toggle v-model="showPasswords" color="deep-orange" label="แสดงรหัสผ่าน" />
              <q-input dense debounce="300" v-model="filter" placeholder="ค้นหา...">
                <template #prepend><q-icon name="search" /></template>
              </q-input>
              <q-separator vertical spaced v-if="editMode" />
              <q-btn v-if="editMode" dense flat icon="delete" color="negative" label="ลบที่เลือก"
                :disable="selected.length === 0" @click="onDeleteSelected()" />
            </div>
          </template>

          <!-- username -->
          <template #body-cell-username="props">
            <q-td :props="props" :class="dupCellClass('username', props.row)">
              <template v-if="editMode">
                <q-input dense borderless :model-value="props.value || ''"
                  @update:model-value="(v) => onEditCell(props.row, 'username', v == null ? '' : String(v))" />
              </template>
              <template v-else>{{ props.value }}</template>
            </q-td>
          </template>

          email
          <template #body-cell-email="props">
            <q-td :props="props" :class="dupCellClass('email', props.row)">
              <template v-if="editMode">
                <q-input dense borderless type="email" :model-value="props.value || ''"
                  @update:model-value="(v) => onEditCell(props.row, 'email', v == null ? '' : String(v))" />
              </template>
              <template v-else>{{ props.value }}</template>
            </q-td>
          </template>

          <!-- full_name -->
          <template #body-cell-full_name="props">
            <q-td :props="props" :class="dupCellClass('full_name', props.row)">
              <template v-if="editMode">
                <q-input dense borderless :model-value="props.value || ''"
                  @update:model-value="(v) => onEditCell(props.row, 'full_name', v == null ? '' : String(v))" />
              </template>
              <template v-else>{{ props.value }}</template>
            </q-td>
          </template>

          <!-- password -->
          <template #body-cell-password="props">
            <q-td :props="props">
              <template v-if="editMode">
                <q-input dense borderless :type="showPasswords ? 'text' : 'password'" :model-value="props.value || ''"
                  @update:model-value="(v) => onEditCell(props.row, 'password', v == null ? '' : String(v))"
                  autocomplete="new-password">
                  <template #append><q-icon name="key" size="16px" /></template>
                </q-input>
              </template>
              <template v-else>
                <span v-if="showPasswords">{{ props.value }}</span>
                <span v-else class="text-grey-6">{{ props.value ? '••••••' : '' }}</span>
              </template>
            </q-td>
          </template>

          <!-- department_name (select) -->
          <template #body-cell-department_name="props">
            <q-td :props="props">
              <template v-if="editMode">
                <q-select dense borderless :model-value="props.row.department_name || ''" :options="departments"
                  use-input fill-input input-debounce="0"
                  @new-value="val => onEditCell(props.row, 'department_name', typeof val === 'string' ? val : '')"
                  @update:model-value="v => onEditCell(props.row, 'department_name', typeof v === 'string' ? v : '')">
                  <template #prepend><q-icon name="apartment" size="16px" /></template>
                </q-select>
              </template>
              <template v-else>{{ props.value }}</template>
            </q-td>
          </template>

          <!-- role_name (select) -->
          <template #body-cell-role_name="props">
            <q-td :props="props">
              <template v-if="editMode">
                <q-select dense borderless :model-value="props.row.role_name || ''" :options="roles" use-input
                  fill-input input-debounce="0"
                  @new-value="val => onEditCell(props.row, 'role_name', typeof val === 'string' ? val : '')"
                  @update:model-value="v => onEditCell(props.row, 'role_name', typeof v === 'string' ? v : '')">
                  <template #prepend><q-icon name="badge" size="16px" /></template>
                </q-select>
              </template>
              <template v-else>{{ props.value }}</template>
            </q-td>
          </template>

          <!-- generic editable fallback -->
          <template #body-cell="props">
            <q-td v-if="isEditable(props.col.name)" :props="props">
              <q-input v-if="editMode" dense borderless :model-value="props.value || ''"
                @update:model-value="(v) => onEditCell(props.row, props.col.name as RequiredHeader, v == null ? '' : String(v))" />
              <template v-else>{{ props.value }}</template>
            </q-td>
            <q-td v-else :props="props">{{ props.value }}</q-td>
          </template>

          <!-- Actions (delete inline; show only in editMode) -->
          <template #body-cell-actions="props">
            <q-td :props="props" style="width: 90px; text-align:center;">
              <q-btn v-if="editMode" round dense flat icon="delete" color="negative"
                @click.stop="onDeleteOne(props.row.__row_id)" :aria-label="`ลบแถวที่ ${props.row.__row_id}`" />
            </q-td>
          </template>
        </q-table>

        <div class="text-caption text-grey-7">
          * รองรับไฟล์ .csv, .xlsx, .json (ต้องมี Header: {{ REQUIRED_HEADERS.join(', ') }})<br />
          * ชื่อ <b>department_name</b> และ <b>role_name</b> ต้องตรงกับข้อมูลในระบบ<br />
          * ไฮไลต์สีเหลือง มีข้อมูลซ้ำกันในไฟล์ และ ไฮไลต์สีแดง มีข้อมูลซ้ำกับในระบบ
        </div>
      </q-card-section>

      <q-separator />

      <!-- ===== Footer / Actions ===== -->
      <q-card-actions align="right" class="dialog-actions sticky-footer">
        <q-btn class="secondary-btn" label="CANCEL" flat @click="close" />
        <q-btn class="primary-btn" label="DOWNLOAD JSON" color="secondary" text-color="white" :disable="!rows.length"
          @click="downloadJson" />
        <q-btn class="primary-btn success-btn" label="UPLOAD"
          :disable="!rows.length || missingHeaders.size > 0 || isUploading || hasDupMarks" :loading="isUploading"
          @click="handleUpload" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { useDialogOrPopupstore } from 'src/stores/dialogOrPopup';
import { computed, defineEmits, ref, watch } from 'vue';
import Papa, { type ParseResult } from 'papaparse';
import * as XLSX from 'xlsx';
import type { QTableColumn } from 'quasar';
import { Notify } from 'quasar';
import axios from 'axios';

const emit = defineEmits<{ (e: 'uploaded'): void }>();
const dialogOrPopupStore = useDialogOrPopupstore();

/** ==== CONFIG ==== */
const API =
  (import.meta as unknown as { env?: { VITE_API_BASE?: string } }).env?.VITE_API_BASE ??
  'http://localhost:8080';

/** ==== fullscreen & width ==== */
const maximized = ref(false);
const cardStyle = computed(() =>
  maximized.value
    ? 'width: 100vw; max-width: 100vw; height: 100vh; max-height: 100vh;'
    : 'width: min(1400px, 96vw); max-width: 96vw; max-height: 90vh;'
);

/** ==== TYPES ==== */
const REQUIRED_HEADERS = ['username', 'password', 'full_name', 'department_name', 'role_name', 'email'] as const;
type RequiredHeader = typeof REQUIRED_HEADERS[number];

type CsvRow = Record<string, string> &
  Partial<Record<RequiredHeader, string>> & { __row_id?: number };
type UserColumn = QTableColumn<CsvRow>;

type ExportRow = {
  username: string; password: string; full_name: string;
  department_name: string; role_name: string; email: string;
};

interface DepartmentRef { id?: number; department_name?: string; name?: string; }
interface RoleRef { id?: number; role_name?: string; name?: string; }
interface UserApi { username?: string | null; email?: string | null; full_name?: string | null; }
type UnknownObject = Record<string, unknown>;

/** ==== STATE ==== */
const editMode = ref<boolean>(false);
const showPasswords = ref<boolean>(false);

const file = ref<File | null>(null);
const rows = ref<CsvRow[]>([]);
const columns = ref<UserColumn[]>([]);
const filter = ref<string>('');
const errorMsg = ref<string>('');
const pagination = ref({ page: 1, rowsPerPage: 25 });
const isUploading = ref<boolean>(false);

const selected = ref<CsvRow[]>([]);
const lastDeleted = ref<CsvRow[] | null>(null);

// refs for validation
const departments = ref<string[]>([]);
const roles = ref<string[]>([]);

// existing sets (avoid duplicates)
const existingUsernames = ref<Set<string>>(new Set());
const existingEmails = ref<Set<string>>(new Set());
const existingFullNames = ref<Set<string>>(new Set());

const dupInFile = {
  username: ref<Set<string>>(new Set()),
  email: ref<Set<string>>(new Set()),
  full_name: ref<Set<string>>(new Set()),
};
const dupInDB = {
  username: ref<Set<string>>(new Set()),
  email: ref<Set<string>>(new Set()),
  full_name: ref<Set<string>>(new Set()),
};

const norm = (s?: string) => (s ?? '').trim().toLowerCase();

/** ==== VALIDATION ==== */
const currentHeaders = ref<string[]>([]);

const missingHeaders = computed<Set<RequiredHeader>>(() => {
  const present = new Set(currentHeaders.value.map(h => h.toLowerCase().trim()));
  const missing = new Set<RequiredHeader>();
  REQUIRED_HEADERS.forEach(h => { if (!present.has(h)) missing.add(h); });
  return missing;
});
const unknownHeaders = computed<string[]>(() => {
  const must = new Set<string>(REQUIRED_HEADERS as unknown as string[]);
  return currentHeaders.value.filter(h => !must.has(h));
});
const hasDupMarks = computed<boolean>(() =>
  dupInFile.username.value.size > 0 ||
  dupInFile.email.value.size > 0 ||
  dupInFile.full_name.value.size > 0 ||
  dupInDB.username.value.size > 0 ||
  dupInDB.email.value.size > 0 ||
  dupInDB.full_name.value.size > 0
);

/** ==== HELPERS ==== */
function prettifyLabel(h: string) {
  return h.replace(/_/g, ' ').replace(/\b\w/g, c => c.toUpperCase());
}

function buildColumnsFromHeaders(headers: string[]): UserColumn[] {
  const numberCol: UserColumn = {
    name: 'no',
    label: 'No.',
    field: (row: CsvRow) => String(row.__row_id ?? ''),
    align: 'center',
    sortable: false,
    style: 'width:60px'
  };
  const dataCols: UserColumn[] = headers.map<UserColumn>((h) => ({
    name: h,
    label: prettifyLabel(h),
    field: (row: CsvRow) => row[h] ?? '',
    align: 'left' as const,
    sortable: true
  }));
  const actionCol: UserColumn = {
    name: 'actions',
    label: 'Actions',
    field: () => '',
    align: 'center',
    sortable: false,
    style: 'width:90px'
  };
  return [numberCol, ...dataCols, actionCol];
}

function clearFile(): void {
  file.value = null;
  rows.value = [];
  columns.value = [];
  errorMsg.value = '';
  currentHeaders.value = [];
  filter.value = '';
  pagination.value = { page: 1, rowsPerPage: 25 };
  selected.value = [];

  dupInFile.username.value.clear();
  dupInFile.email.value.clear();
  dupInFile.full_name.value.clear();
  dupInDB.username.value.clear();
  dupInDB.email.value.clear();
  dupInDB.full_name.value.clear();
}

function close(): void {
  clearFile();
  dialogOrPopupStore.showManyUsersDialog = false;
}

/** ==== REF DATA ==== */
function pickList<T>(input: unknown): T[] {
  if (Array.isArray(input)) return input as T[];
  if (input && typeof input === 'object') {
    const obj = input as UnknownObject;
    const inner = obj['data'];
    if (Array.isArray(inner)) return inner as T[];
  }
  return [];
}

async function preloadRefData() {
  try {
    const [deptRes, roleRes, usersRes] = await Promise.all([
      axios.get<unknown>(`${API}/iverytried/departments`),
      axios.get<unknown>(`${API}/iverytried/roles`),
      axios.get<unknown>(`${API}/iverytried/users`),
    ]);

    const deptList = pickList<DepartmentRef>(deptRes.data);
    const roleList = pickList<RoleRef>(roleRes.data);
    const users = pickList<UserApi>(usersRes.data);

    departments.value = deptList.map(d => (d.department_name ?? d.name ?? '').trim()).filter(Boolean);
    roles.value = roleList.map(r => (r.role_name ?? r.name ?? '').trim()).filter(Boolean);

    existingUsernames.value = new Set(users.map(u => norm(u.username ?? '')).filter(Boolean));
    existingEmails.value = new Set(users.map(u => norm(u.email ?? '')).filter(Boolean));
    existingFullNames.value = new Set(users.map(u => norm(u.full_name ?? '')).filter(Boolean));
  } catch {
    Notify.create({ type: 'warning', message: 'โหลดรายการอ้างอิงไม่สำเร็จ — จะไม่ตรวจชื่อซ้ำ/สะกด' });
  }
}

watch(() => dialogOrPopupStore.showManyUsersDialog, (open) => { if (open) void preloadRefData(); });

/** ==== PARSE ==== */
async function onFilePicked(f: File | null): Promise<void> {
  errorMsg.value = '';
  rows.value = [];
  columns.value = [];
  currentHeaders.value = [];
  if (!f) return;
  const ext = f.name.split('.').pop()?.toLowerCase();
  if (ext === 'csv') {
    Papa.parse<CsvRow>(f, {
      header: true,
      skipEmptyLines: true,
      transformHeader: (h: string): string => h.trim().toLowerCase(),
      complete: (result: ParseResult<CsvRow>): void => { processParsedData(result.data.filter(Boolean)); },
      error: (err) => { errorMsg.value = `เกิดข้อผิดพลาดในการอ่าน CSV: ${err.message}`; }
    });
  } else if (ext === 'xlsx') {
    try {
      const buf = await f.arrayBuffer();
      const workbook = XLSX.read(buf, { type: 'array' });
      const sheetName = workbook.SheetNames?.[0];
      if (!sheetName) { errorMsg.value = 'ไฟล์ XLSX ไม่มี sheet'; return; }
      const sheet = workbook.Sheets[sheetName];
      if (!sheet) { errorMsg.value = 'ไม่พบข้อมูลใน sheet'; return; }
      const json = XLSX.utils.sheet_to_json<CsvRow>(sheet, { defval: '' });
      processParsedData(json);
    } catch { errorMsg.value = 'อ่านไฟล์ XLSX ไม่สำเร็จ'; }
  } else if (ext === 'json') {
    try {
      const text = await readFileAsText(f);
      const json = JSON.parse(text) as CsvRow[];
      processParsedData(json);
    } catch { errorMsg.value = 'ไฟล์ JSON ไม่ถูกต้อง'; }
  } else {
    errorMsg.value = 'ไฟล์ที่รองรับ: .csv, .xlsx, .json';
  }
}

// แปลงไฟล์รุ่นเก่า: department -> department_name, role -> role_name
function normalizeLegacyColumns(data: CsvRow[]): CsvRow[] {
  let touched = false;
  for (const r of data) {
    if (!r['department_name'] && (r as Record<string, string>)['department']) {
      r['department_name'] = String((r as Record<string, string>)['department']);
      delete (r as Record<string, string>)['department'];
      touched = true;
    }
    if (!r['role_name'] && (r as Record<string, string>)['role']) {
      r['role_name'] = String((r as Record<string, string>)['role']);
      delete (r as Record<string, string>)['role'];
      touched = true;
    }
  }
  if (touched) {
    Notify.create({ type: 'info', message: 'แปลงคอลัมน์อัตโนมัติ', caption: 'department → department_name, role → role_name' });
  }
  return data;
}

function processParsedData(data: CsvRow[]): void {
  const clean = normalizeLegacyColumns(data.filter(Boolean));
  if (clean.length === 0) {
    errorMsg.value = 'ไม่พบข้อมูลในไฟล์';
    rows.value = []; columns.value = []; currentHeaders.value = []; return;
  }
  const headerSet = new Set<string>();
  for (const r of clean) for (const k of Object.keys(r)) headerSet.add(k);
  const headers = Array.from(headerSet);
  const orderedHeaders: string[] = [
    ...REQUIRED_HEADERS.filter(h => headers.includes(h)),
    ...headers.filter(h => !REQUIRED_HEADERS.includes(h as RequiredHeader))
  ];
  clean.forEach((r, idx) => { r.__row_id = idx + 1; });
  rows.value = clean;
  currentHeaders.value = orderedHeaders;
  columns.value = buildColumnsFromHeaders(orderedHeaders);
  errorMsg.value = missingHeaders.value.size > 0
    ? `ขาดคอลัมน์บังคับ: ${Array.from(missingHeaders.value).join(', ')}`
    : '';
  recomputeDuplicateMarks();
}

function readFileAsText(f: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = () => reject(new Error(reader.error?.message || 'Unknown file read error'));
    reader.readAsText(f);
  });
}

/** ==== EXPORT ==== */
function downloadJson(): void {
  if (!rows.value.length) return;
  const originalName = file.value?.name || 'users.csv';
  const baseName = originalName.replace(/\.[^/.]+$/, '');
  const fileName = `${baseName}.json`;
  const exportRows = rows.value.map(row =>
    Object.fromEntries(Object.entries(row).filter(([k]) => k !== '__row_id'))
  );
  const blob = new Blob([JSON.stringify(exportRows, null, 2)], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url; link.download = fileName; link.click();
  URL.revokeObjectURL(url);
}

function toRowsForCsvExport(): ExportRow[] {
  return rows.value.map((r: CsvRow) => ({
    username: String(r.username ?? ''),
    password: String(r.password ?? ''),
    full_name: String(r.full_name ?? ''),
    department_name: String(r.department_name ?? (r as Record<string, string>)['department'] ?? ''),
    role_name: String(r.role_name ?? (r as Record<string, string>)['role'] ?? ''),
    email: String(r.email ?? ''),
  }));
}

function buildCsvText(): string {
  const data = toRowsForCsvExport();
  return Papa.unparse(data, {
    header: true,
    delimiter: ',',
    newline: '\n',
    columns: REQUIRED_HEADERS as unknown as string[]
  });
}

/** ==== EDIT HANDLERS ==== */
function isEditable(colName: string): colName is RequiredHeader {
  return (REQUIRED_HEADERS as readonly string[]).includes(colName as RequiredHeader);
}

function onEditCell(row: CsvRow, field: RequiredHeader, value: string) {
  (row as Record<string, string>)[field] = value ?? '';
}

/** ==== DUP MARK ==== */
function recomputeDuplicateMarks() {
  const count = (arr: string[]) => {
    const m = new Map<string, number>();
    for (const v of arr) if (v) m.set(v, (m.get(v) ?? 0) + 1);
    return m;
  };

  const usernames = rows.value.map(r => norm(r.username));
  const emails = rows.value.map(r => norm(r.email));
  const fullnames = rows.value.map(r => norm(r.full_name));

  const uMap = count(usernames);
  const eMap = count(emails);
  const fMap = count(fullnames);

  dupInFile.username.value = new Set([...uMap.entries()].filter(([, c]) => c > 1).map(([k]) => k));
  dupInFile.email.value = new Set([...eMap.entries()].filter(([, c]) => c > 1).map(([k]) => k));
  dupInFile.full_name.value = new Set([...fMap.entries()].filter(([, c]) => c > 1).map(([k]) => k));

  dupInDB.username.value = new Set(usernames.filter(v => v && existingUsernames.value.has(v)));
  dupInDB.email.value = new Set(emails.filter(v => v && existingEmails.value.has(v)));
  dupInDB.full_name.value = new Set(fullnames.filter(v => v && existingFullNames.value.has(v)));
}

watch([rows, existingUsernames, existingEmails, existingFullNames], () => { recomputeDuplicateMarks(); }, { deep: true });

function dupCellClass(field: 'username' | 'email' | 'full_name', row: CsvRow) {
  const v = norm(row[field]);
  if (!v) return '';
  const fileDup = dupInFile[field].value.has(v);
  const dbDup = dupInDB[field].value.has(v);
  return fileDup || dbDup ? (dbDup ? 'dup-cell db' : 'dup-cell file') : '';
}

function summarizeDup(): string {
  const show = (s: Set<string>, label: string) =>
    s.size ? `${label}: ${Array.from(s).slice(0, 5).join(', ')}${s.size > 5 ? ` …(+${s.size - 5})` : ''}` : '';
  return [
    show(dupInFile.username.value, 'username ซ้ำในไฟล์'),
    show(dupInFile.email.value, 'email ซ้ำในไฟล์'),
    show(dupInFile.full_name.value, 'ชื่อเต็มซ้ำในไฟล์'),
    show(dupInDB.username.value, 'username ชนของเดิมในระบบ'),
    show(dupInDB.email.value, 'email ชนของเดิมในระบบ'),
    show(dupInDB.full_name.value, 'ชื่อเต็มชนของเดิมในระบบ'),
  ].filter(Boolean).join(' | ');
}

/** ==== VALIDATE dept/role ==== */
function validateDeptRole(rowsToCheck: CsvRow[]) {
  const deptSet = new Set(departments.value.map(s => s.toLowerCase()));
  const roleSet = new Set(roles.value.map(s => s.toLowerCase()));
  const invalidDept = new Set<string>();
  const invalidRole = new Set<string>();

  for (const r of rowsToCheck) {
    const d = (r.department_name ?? (r as Record<string, string>)['department'] ?? '').trim();
    const rl = (r.role_name ?? (r as Record<string, string>)['role'] ?? '').trim();
    if (d && !deptSet.has(d.toLowerCase())) invalidDept.add(d);
    if (rl && !roleSet.has(rl.toLowerCase())) invalidRole.add(rl);
  }
  return { invalidDept, invalidRole };
}

/** ==== DELETE (inline, undoable) ==== */
function reindexRows() {
  rows.value.forEach((r, i) => { r.__row_id = i + 1; });
}

function onDeleteOne(id?: number) {
  if (!id) return;
  lastDeleted.value = rows.value.filter(r => (r.__row_id ?? -1) === id);
  rows.value = rows.value.filter(r => (r.__row_id ?? -1) !== id);
  reindexRows();
  selected.value = [];
  showUndo(`ลบแถวที่ ${id} แล้ว`);
}

function onDeleteSelected() {
  const ids = new Set<number>(selected.value.map(r => r.__row_id ?? -1));
  if (!ids.size) return;
  lastDeleted.value = rows.value.filter(r => ids.has(r.__row_id ?? -1));
  rows.value = rows.value.filter(r => !ids.has(r.__row_id ?? -1));
  reindexRows();
  selected.value = [];
  showUndo(`ลบ ${lastDeleted.value.length} แถวแล้ว`);
}

function showUndo(msg: string) {
  Notify.create({
    type: 'info',
    message: msg,
    timeout: 5000,
    actions: [
      {
        label: 'Undo',
        color: 'white',
        handler: () => {
          if (!lastDeleted.value?.length) return;
          // ใส่กลับแล้วเรียงลำดับใหม่
          rows.value = [...rows.value, ...lastDeleted.value]
            .sort((a, b) => (a.__row_id ?? 0) - (b.__row_id ?? 0));
          reindexRows();
          lastDeleted.value = null;
        }
      }
    ]
  });
}

/** ==== UPLOAD ==== */
async function handleUpload(): Promise<void> {
  if (rows.value.length === 0) return;

  if (missingHeaders.value.size > 0) {
    const msg = `ขาดคอลัมน์บังคับ: ${Array.from(missingHeaders.value).join(', ')}`;
    errorMsg.value = msg;
    Notify.create({ type: 'warning', message: 'ไม่สามารถอัปโหลดได้', caption: msg });
    return;
  }

  if (hasDupMarks.value) {
    Notify.create({
      type: 'negative',
      message: 'ยังมีข้อมูลซ้ำอยู่ — กรุณาแก้ไขให้ครบก่อนอัปโหลด',
      caption: summarizeDup()
    });
    return;
  }

  if (departments.value.length || roles.value.length) {
    const { invalidDept, invalidRole } = validateDeptRole(rows.value);
    if (invalidDept.size || invalidRole.size) {
      const msg = [
        invalidDept.size ? `แผนกไม่พบ: ${Array.from(invalidDept).join(', ')}` : '',
        invalidRole.size ? `บทบาทไม่พบ: ${Array.from(invalidRole).join(', ')}` : ''
      ].filter(Boolean).join(' | ');
      Notify.create({ type: 'warning', message: 'ตรวจพบข้อมูลไม่ตรงระบบ', caption: msg });
      return;
    }
  }

  try {
    isUploading.value = true;

    const csvText = buildCsvText();
    const originalName = file.value?.name || 'users.csv';
    const baseName = originalName.replace(/\.[^/.]+$/, '');
    const outName = `${baseName}.csv`;
    const blob = new Blob([csvText], { type: 'text/csv;charset=utf-8' });
    const csvFile = new File([blob], outName, { type: 'text/csv' });

    const formData = new FormData();
    formData.append('file', csvFile);

    await axios.post(`${API}/iverytried/users/upload_users`, formData);

    Notify.create({
      type: 'positive',
      message: 'อัปโหลดสำเร็จ 🎉',
      caption: `บันทึกข้อมูล ${rows.value.length.toLocaleString()} แถว`
    });
    emit('uploaded');
    close();
  } catch (e: unknown) {
    const err = e as { response?: { data?: unknown } };
    let msg = 'อัปโหลดไม่สำเร็จ ⚠️';
    const data = err.response?.data;

    if (typeof data === 'string') msg = data;
    else if (data && typeof data === 'object') {
      const obj = data as UnknownObject;
      const maybeMessage = obj['message'];
      if (typeof maybeMessage === 'string') msg = maybeMessage;
    }

    Notify.create({
      type: 'negative',
      message: msg,
      caption: 'หัวตารางที่ต้องมี: username, password, full_name, department_name, role_name, email'
    });
  } finally {
    isUploading.value = false;
  }
}
</script>

<style scoped>
/* ===== LAYOUT & SCROLL FIX ===== */
.premium-dialog {
  width: min(1400px, 96vw);
  max-width: 96vw;
  max-height: 90vh;

  border-radius: 20px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.12),
    0 8px 25px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.06);

  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

:global(.q-dialog__inner--minimized) .premium-dialog {
  max-height: 90vh;
}

:global(.q-dialog__inner--maximized) .premium-dialog {
  height: 100vh;
}

.dialog-header {
  flex: 0 0 auto;
}

.dialog-actions {
  flex: 0 0 auto;
}

.dialog-body {
  flex: 1 1 auto;
  min-height: 0;
  overflow: auto;
  padding: 0 24px 20px 24px;
}

:deep(.q-table) {
  border-radius: 14px;
  background: #fff;
  box-shadow: 0 1px 0 rgba(0, 0, 0, .04) inset;
  overflow-x: auto;
}

:deep(.q-table thead th) {
  white-space: nowrap;
}

:deep(.sticky-th) {
  position: sticky;
  top: 0;
  z-index: 2;
  background: #fff;
  box-shadow: 0 1px 0 rgba(0, 0, 0, .06);
}

/* ===== THEME: BLUE/PURPLE ===== */
.premium-dialog::before {
  content: '';
  position: absolute;
  inset: 0 0 auto 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6 0%, #6366f1 50%, #8b5cf6 100%);
}

/* Header */
.dialog-header {
  gap: 16px;
  padding: 20px 24px 16px 24px;
  background: rgba(255, 255, 255, 0.85);
}

.icon-container {
  flex-shrink: 0;
}

.success-icon-wrapper {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 20px rgba(99, 102, 241, 0.28);
}

.main-icon {
  font-size: 28px;
  color: #fff;
}

.header-text {
  padding-top: 2px;
}

.dialog-title {
  font-size: 20px;
  font-weight: 700;
  margin: 0 0 6px 0;
  line-height: 1.3;
  color: #4f46e5;
}

.dialog-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  font-weight: 500;
}

.header-actions .q-btn {
  --q-btn-padding: 6px;
}

/* field & badge */
:deep(.q-file) {
  border-radius: 12px;
  background: rgba(248, 250, 252, 0.6);
}

:deep(.q-field--outlined .q-field__control) {
  border-radius: 12px;
}

:deep(.q-badge) {
  border-radius: 10px;
  padding: 3px 8px;
}

/* ===== ACTIONS / BUTTONS ===== */
.dialog-actions {
  padding: 16px 24px 20px 24px;
  gap: 10px;
  justify-content: flex-end;
  background: rgba(248, 250, 252, 0.5);
  border-top: 1px solid rgba(0, 0, 0, 0.06);
}

.secondary-btn {
  padding: 10px 18px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
  color: #6b7280;
  background: transparent;
  transition: all .2s ease;
  min-width: 100px;
}

.secondary-btn:hover {
  background: rgba(107, 114, 128, 0.1);
  color: #4b5563;
}

.primary-btn {
  padding: 10px 20px;
  border-radius: 12px;
  font-weight: 700;
  font-size: 14px;
  border: none;
  min-width: 120px;
  transition: all .2s ease;
}

.success-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  color: #fff !important;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.35);
}

.success-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #6366f1 100%);
  box-shadow: 0 6px 16px rgba(79, 70, 229, 0.45);
  transform: translateY(-1px);
}

/* ===== DUP HIGHLIGHT ===== */
.dup-cell {
  font-weight: 600;
}

.dup-cell.file {
  background-color: rgba(245, 158, 11, 0.16);
  color: #7a5a00;
}

.dup-cell.db {
  background-color: rgba(239, 68, 68, 0.16);
  color: #b91c1c;
}

/* ===== DARK MODE ===== */
:root body.body--dark .premium-dialog {
  background: linear-gradient(135deg, #0b1024 0%, #0f1733 100%);
  border-color: rgba(255, 255, 255, 0.06);
}

:root body.body--dark .dialog-header,
:root body.body--dark .dialog-actions {
  background: rgba(15, 23, 42, 0.5);
}

:root body.body--dark .dialog-title {
  color: #8b5cf6;
}

:root body.body--dark .dialog-subtitle {
  color: #cbd5e1;
}

:root body.body--dark .q-table {
  background: rgba(2, 6, 23, 0.6);
}

:root body.body--dark .sticky-th {
  background: rgba(2, 6, 23, 0.8);
}
</style>
