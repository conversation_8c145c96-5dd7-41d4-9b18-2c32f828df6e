{"name": "quasar-project", "version": "0.0.1", "description": "A Quasar Project", "productName": "Quasar App", "author": "Tanipong Santibowonwong <<EMAIL>>", "type": "module", "private": true, "scripts": {"lint": "eslint -c ./eslint.config.js \"./src*/**/*.{ts,js,cjs,mjs,vue}\"", "format": "prettier --write \"**/*.{js,ts,vue,scss,html,md,json}\" --ignore-path .gitignore", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "dev": "quasar dev", "build": "quasar build", "postinstall": ""}, "dependencies": {"@quasar/extras": "^1.16.4", "axios": "^1.7.9", "chart.js": "^4.5.0", "docx-preview": "^0.3.5", "html2pdf.js": "^0.10.3", "jspdf": "^3.0.1", "katex": "^0.16.22", "mammoth": "^1.9.1", "papaparse": "^5.5.3", "pdfjs-dist": "^5.3.31", "pinia": "^3.0.1", "quasar": "^2.18.2", "suneditor": "^2.41.3", "vue": "^3.4.18", "vue-chartjs": "^5.3.2", "vue-router": "^4.5.1", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.14.0", "@quasar/app-vite": "^2.3.0", "@quasar/cli": "^2.5.0", "@types/node": "^20.5.9", "@types/papaparse": "^5.3.16", "@types/vue-router": "^2.0.0", "@vitest/coverage-v8": "^3.2.3", "@vitest/ui": "^3.2.3", "@vue/compiler-sfc": "^3.5.13", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.1.3", "@vue/test-utils": "^2.4.6", "autoprefixer": "^10.4.2", "eslint": "^9.14.0", "eslint-plugin-vue": "^9.30.0", "globals": "^15.12.0", "happy-dom": "^18.0.1", "jsdom": "^26.1.0", "prettier": "^3.3.3", "typescript": "~5.5.3", "vite-plugin-checker": "^0.8.0", "vitest": "^3.2.3", "vue-tsc": "^2.0.29"}, "engines": {"node": "^28 || ^26 || ^24 || ^22 || ^20 || ^18", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}