import { vi } from 'vitest'
import { config } from '@vue/test-utils'
import { Quasar } from 'quasar'

// Mock axios with proper mock functions
vi.mock('src/boot/axios', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    patch: vi.fn(),
  },
}))

// Configure Vue Test Utils to use Quasar
config.global.plugins = config.global.plugins || []
if (!config.global.plugins.includes(Quasar)) {
  config.global.plugins.push(Quasar)
}

// Mock Quasar components and directives that might be used in tests
config.global.stubs = {
  QPage: true,
  QPageContainer: true,
  QCard: true,
  QCardSection: true,
  QBtn: true,
  QInput: true,
  QSelect: true,
  QTable: true,
  QDialog: true,
  QIcon: true,
  QSpace: true,
  QSeparator: true,
  QSpinner: true,
  QLinearProgress: true,
}

// Mock router
vi.mock('vue-router', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    go: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
  }),
  useRoute: () => ({
    params: {},
    query: {},
    path: '/',
    name: 'home',
  }),
}))

// Global test utilities
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})
