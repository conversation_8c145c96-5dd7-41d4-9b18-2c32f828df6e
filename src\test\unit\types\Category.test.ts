import { describe, it, expect } from 'vitest'
import type { Category } from 'src/types/Category'

describe('Category Type', () => {
  const mockCategory: Category = {
    category_id: 1,
    category_name: 'ประกาศ'
  }

  it('should create a valid Category object', () => {
    expect(mockCategory).toBeDefined()
    expect(mockCategory.category_id).toBe(1)
    expect(mockCategory.category_name).toBe('ประกาศ')
  })

  it('should validate required properties', () => {
    const requiredProperties = ['category_id', 'category_name']
    
    requiredProperties.forEach(prop => {
      expect(mockCategory).toHaveProperty(prop)
    })
  })

  it('should validate property types', () => {
    expect(typeof mockCategory.category_id).toBe('number')
    expect(typeof mockCategory.category_name).toBe('string')
  })

  it('should handle empty category', () => {
    const emptyCategory: Category = {
      category_id: 0,
      category_name: ''
    }

    expect(emptyCategory).toBeDefined()
    expect(emptyCategory.category_id).toBe(0)
    expect(emptyCategory.category_name).toBe('')
  })

  it('should handle different category types', () => {
    const categories = [
      { category_id: 1, category_name: 'ประกาศ' },
      { category_id: 2, category_name: 'ระเบียบ' },
      { category_id: 3, category_name: 'ข้อบังคับ' },
      { category_id: 4, category_name: 'คำสั่ง' }
    ]

    categories.forEach(category => {
      expect(category).toHaveProperty('category_id')
      expect(category).toHaveProperty('category_name')
      expect(typeof category.category_id).toBe('number')
      expect(typeof category.category_name).toBe('string')
    })
  })
})
