import { describe, it, expect, vi, beforeEach } from 'vitest'
import departmentService from 'src/services/department'
import type { Department } from 'src/types/Department'
import { createAxiosResponse } from 'src/test/test-utils'

// Mock axios
vi.mock('src/boot/axios', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    patch: vi.fn(),
  },
}))

// Import the mocked axios
import axios from 'src/boot/axios'
const mockedAxios = axios as any

describe('Department Service', () => {
  const mockDepartment: Department = {
    department_id: 1,
    department_name: 'คณะวิศวกรรมศาสตร์',
  }

  const mockDepartments: Department[] = [
    mockDepartment,
    {
      department_id: 2,
      department_name: 'คณะวิทยาการสารสนเทศ',
    },
    {
      department_id: 3,
      department_name: 'คณะบริหารธุรกิจ',
    },
  ]

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getDepartment', () => {
    it('should fetch a department by id', async () => {
      const mockResponse = createAxiosResponse(mockDepartment)
      mockedAxios.get.mockResolvedValue(mockResponse)

      const result = await departmentService.getDepartment(1)

      expect(mockedAxios.get).toHaveBeenCalledWith('/departments/1')
      expect(result).toEqual(mockResponse)
    })

    it('should handle error when fetching department', async () => {
      const mockError = new Error('Department not found')
      mockedAxios.get.mockRejectedValue(mockError)

      await expect(departmentService.getDepartment(999)).rejects.toThrow('Department not found')
      expect(mockedAxios.get).toHaveBeenCalledWith('/departments/999')
    })

    it('should fetch department with different ids', async () => {
      const mockResponse = createAxiosResponse(mockDepartments[1])
      mockedAxios.get.mockResolvedValue(mockResponse)

      const result = await departmentService.getDepartment(2)

      expect(mockedAxios.get).toHaveBeenCalledWith('/departments/2')
      expect(result).toEqual(mockResponse)
    })
  })

  describe('getDepartments', () => {
    it('should fetch all departments', async () => {
      const mockResponse = createAxiosResponse(mockDepartments)
      mockedAxios.get.mockResolvedValue(mockResponse)

      const result = await departmentService.getDepartments()

      expect(mockedAxios.get).toHaveBeenCalledWith('/departments')
      expect(result).toEqual(mockResponse)
      expect(result.data).toHaveLength(3)
    })

    it('should handle error when fetching departments', async () => {
      const mockError = new Error('Failed to fetch departments')
      mockedAxios.get.mockRejectedValue(mockError)

      await expect(departmentService.getDepartments()).rejects.toThrow(
        'Failed to fetch departments',
      )
      expect(mockedAxios.get).toHaveBeenCalledWith('/departments')
    })

    it('should handle empty departments response', async () => {
      const mockResponse = createAxiosResponse([])
      mockedAxios.get.mockResolvedValue(mockResponse)

      const result = await departmentService.getDepartments()

      expect(mockedAxios.get).toHaveBeenCalledWith('/departments')
      expect(result.data).toHaveLength(0)
    })
  })

  describe('service structure', () => {
    it('should export correct methods', () => {
      expect(departmentService).toHaveProperty('getDepartment')
      expect(departmentService).toHaveProperty('getDepartments')
      expect(typeof departmentService.getDepartment).toBe('function')
      expect(typeof departmentService.getDepartments).toBe('function')
    })
  })
})
