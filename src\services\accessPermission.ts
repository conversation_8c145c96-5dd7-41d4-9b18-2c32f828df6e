import type { AccessPermission } from 'src/types/AccessPermission'
import axios from 'src/boot/axios'

function getAccessPermission(id: number) {
  return axios.get(`/access-permmission/${id}`)
}

function getAccessPermissiones() {
  return axios.get(`/access-permmissions`)
}

function createAccessPermByDocIdandRoleId(ap: AccessPermission) {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { premission_id, ...data } = ap
  return axios.post(`access-permmission`, data)
}

function createAccessPermsByDocIdandRoleId(ap: AccessPermission) {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { premission_id, ...data } = ap
  return axios.post(`access-permmission`, data)
}

function updateAccessPermByDocIdandRoleId(ap: AccessPermission) {
  const { premission_id, ...data } = ap
  return axios.post(`access-permmission/${premission_id}`, data)
}

function QueryPermissions(role_id: number, department_id: number) {
  return axios.post(
    '/documents/pirolity',
    { role_id, department_id }, // JSON body
    {
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
}



export default {
  getAccessPermission,
  getAccessPermissiones,
  createAccessPermByDocIdandRoleId,
  createAccessPermsByDocIdandRoleId,
  updateAccessPermByDocIdandRoleId,
  QueryPermissions,
}
