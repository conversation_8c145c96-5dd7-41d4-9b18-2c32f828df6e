import { describe, it, expect, vi, beforeEach } from 'vitest'
import { setActivePinia, createP<PERSON> } from 'pinia'
import { useCategorystore } from 'src/stores/category'
import categoryService from 'src/services/category'
import type { Category } from 'src/types/Category'
import { createAxiosResponse } from 'src/test/test-utils'

// Mock the category service
vi.mock('src/services/category')
const mockedCategoryService = vi.mocked(categoryService)

describe('Category Store', () => {
  let categoryStore: ReturnType<typeof useCategorystore>

  const mockCategory: Category = {
    category_id: 1,
    category_name: 'ประกาศ',
  }

  const mockCategories: Category[] = [
    mockCategory,
    { category_id: 2, category_name: 'ระเบียบ' },
    { category_id: 3, category_name: 'ข้อบังคับ' },
    { category_id: 4, category_name: 'คำสั่ง' },
  ]

  beforeEach(() => {
    setActivePinia(createPinia())
    categoryStore = useCategorystore()
    vi.clearAllMocks()
  })

  describe('initial state', () => {
    it('should have correct initial state', () => {
      expect(categoryStore.categories).toBeDefined()
      expect(Array.isArray(categoryStore.categories)).toBe(true)
      expect(categoryStore.categories).toHaveLength(0)

      expect(categoryStore.initialCategory).toBeDefined()
      expect(categoryStore.initialCategory.category_id).toBe(0)
      expect(categoryStore.initialCategory.category_name).toBe('')

      expect(categoryStore.editedCategory).toBeDefined()
      expect(categoryStore.editedCategory.category_id).toBe(0)
      expect(categoryStore.editedCategory.category_name).toBe('')
    })

    it('should validate initial category structure', () => {
      const { initialCategory } = categoryStore

      expect(typeof initialCategory.category_id).toBe('number')
      expect(typeof initialCategory.category_name).toBe('string')
    })

    it('should have editedCategory as deep copy of initialCategory', () => {
      expect(categoryStore.editedCategory).toEqual(categoryStore.initialCategory)
      expect(categoryStore.editedCategory).not.toBe(categoryStore.initialCategory)
    })
  })

  describe('getCategory action', () => {
    it('should fetch category successfully', async () => {
      const mockResponse = createAxiosResponse(mockCategory)
      mockedCategoryService.getCategory.mockResolvedValue(mockResponse)

      await categoryStore.getCategory(1)

      expect(mockedCategoryService.getCategory).toHaveBeenCalledWith(1)
      expect(mockedCategoryService.getCategory).toHaveBeenCalledTimes(1)
    })

    it('should handle different category IDs', async () => {
      const mockResponse = createAxiosResponse(mockCategories[1])
      mockedCategoryService.getCategory.mockResolvedValue(mockResponse)

      await categoryStore.getCategory(2)

      expect(mockedCategoryService.getCategory).toHaveBeenCalledWith(2)
    })

    it('should handle error when fetching category', async () => {
      const mockError = new Error('Category not found')
      mockedCategoryService.getCategory.mockRejectedValue(mockError)

      await expect(categoryStore.getCategory(999)).rejects.toThrow('Category not found')
      expect(mockedCategoryService.getCategory).toHaveBeenCalledWith(999)
    })
  })

  describe('getCategories action', () => {
    it('should fetch all categories successfully', async () => {
      const mockResponse = createAxiosResponse(mockCategories)
      mockedCategoryService.getCategories.mockResolvedValue(mockResponse)

      await categoryStore.getCategories()

      expect(mockedCategoryService.getCategories).toHaveBeenCalledTimes(1)
      expect(categoryStore.categories).toEqual(mockCategories)
    })

    it('should handle error when fetching categories', async () => {
      const mockError = new Error('Failed to fetch categories')
      mockedCategoryService.getCategories.mockRejectedValue(mockError)

      // Mock console.error to avoid error output in tests
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      await categoryStore.getCategories()

      expect(mockedCategoryService.getCategories).toHaveBeenCalledTimes(1)
      expect(consoleSpy).toHaveBeenCalledWith('Error fetching user:', mockError)

      consoleSpy.mockRestore()
    })

    it('should handle empty categories response', async () => {
      const mockResponse = createAxiosResponse([])
      mockedCategoryService.getCategories.mockResolvedValue(mockResponse)

      await categoryStore.getCategories()

      expect(categoryStore.categories).toEqual([])
      expect(categoryStore.categories).toHaveLength(0)
    })
  })

  describe('store structure', () => {
    it('should export correct properties and methods', () => {
      expect(categoryStore).toHaveProperty('categories')
      expect(categoryStore).toHaveProperty('initialCategory')
      expect(categoryStore).toHaveProperty('editedCategory')
      expect(categoryStore).toHaveProperty('getCategory')
      expect(categoryStore).toHaveProperty('getCategories')

      expect(typeof categoryStore.getCategory).toBe('function')
      expect(typeof categoryStore.getCategories).toBe('function')
    })

    it('should have reactive state', () => {
      expect(categoryStore.categories).toBeDefined()
      expect(categoryStore.initialCategory).toBeDefined()
      expect(categoryStore.editedCategory).toBeDefined()
    })
  })

  describe('state mutations', () => {
    it('should update categories when getCategories is called', async () => {
      const mockResponse = createAxiosResponse(mockCategories)
      mockedCategoryService.getCategories.mockResolvedValue(mockResponse)

      expect(categoryStore.categories).toHaveLength(0)

      await categoryStore.getCategories()

      expect(categoryStore.categories).toHaveLength(4)
      expect(categoryStore.categories[0]?.category_name).toBe('ประกาศ')
      expect(categoryStore.categories[1]?.category_name).toBe('ระเบียบ')
    })

    it('should update editedCategory when getCategory is called', async () => {
      const mockResponse = createAxiosResponse(mockCategory)
      mockedCategoryService.getCategory.mockResolvedValue(mockResponse)

      expect(categoryStore.editedCategory.category_id).toBe(0)

      await categoryStore.getCategory(1)

      expect(categoryStore.editedCategory).toEqual(mockCategory)
    })
  })

  describe('data validation', () => {
    it('should maintain correct data types', async () => {
      const mockResponse = createAxiosResponse(mockCategories)
      mockedCategoryService.getCategories.mockResolvedValue(mockResponse)

      await categoryStore.getCategories()

      categoryStore.categories.forEach((category) => {
        expect(typeof category.category_id).toBe('number')
        expect(typeof category.category_name).toBe('string')
      })
    })
  })
})
