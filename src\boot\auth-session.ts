// src/boot/auth-session.ts
import { boot } from 'quasar/wrappers'
import { useUserStore } from 'src/stores/user'
import api from './axios'

export default boot(() => {
  const userStore = useUserStore()
  userStore.loadSession?.()

  const token =
    userStore.token ||
    sessionStorage.getItem('token') ||
    localStorage.getItem('access_token') ||
    ''

  if (token) {
    api.defaults.headers.common['Authorization'] = `Bearer ${token}`
    localStorage.setItem('access_token', token)
  }
})
