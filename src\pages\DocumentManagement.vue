<template>
  <q-page-container>
    <q-page class="q-pt-md background-image  ">
      <q-card-section class=" text-white ">
        <div class="text-h5 row items-center">
          <div class="col-auto">
            <div class="text-h4">การจัดการเอกสาร</div>
          </div>
          <!-- <q-icon name="fa-folder-settings" size="35px" class="text-white" /> -->
          <q-space />
          <div class="row q-gutter-md">
            <q-select class="bg-white text-black dropdown" v-model="selectedType" :options="docTypes" label="ประเภท"
              placeholder="เลือกประเภท" outlined dense @update:model-value="resetIfDefault" />

            <q-select class="bg-white text-black dropdown" v-model="selectedDept" :options="departments"
              label="หน่วยงาน" placeholder="เลือกหน่วยงาน" outlined dense @update:model-value="resetDept" />
            <q-select class="bg-white text-black dropdown" v-model="computedStatus" :options="status"
              option-label="label" option-value="value" emit-value map-options label="สถานะ" placeholder="เลือกสถานะ"
              outlined dense @update:model-value="resetStatus" />

            <div>

              <q-input outlined dense v-model="search" placeholder="ค้นหา"
                class="custom-search bg-white text-black searcher border-none"
                @keyup.enter="contentSearch(search, selectedstatus, access_role, selectedDept, selectedType, page, page_size)">


                <!-- ปุ่ม toggle ค้นหาด้วยเนื้อหา -->
                  <q-btn flat round dense icon="find_in_page" :color="isSearchByContentSelected ? 'primary' : 'grey-6'"
                    @click="isSearchByContentSelected = !isSearchByContentSelected">
                    <q-tooltip class="text-subtitle2" :offset="[0, 8]">
                      เปิด/ปิด การค้นหาด้วยเนื้อหา
                    </q-tooltip>
                  </q-btn>

                <template v-slot:prepend>
                  <q-icon name="search" />
                </template>

                <template v-slot:append>
                  <!-- ปุ่ม dropdown แสดงจำนวน -->
                  <q-btn flat round dense :label="documentLimitLabel" :color="'primary'" :disable="!isSearchByContentSelected">
                    <q-menu>
                      <q-list dense style="min-width: 100px;">
                        <q-item v-for="option in [1, 2, 3, 5, 7]" :key="option" clickable v-close-popup
                          @click="page_size = option">
                          <q-item-section>{{ option }}</q-item-section>
                        </q-item>
                      </q-list>
                    </q-menu>

                    <q-tooltip class="text-subtitle2" :offset="[0, 8]">
                      เลือกจำนวนเอกสารในการค้นหาด้วยเนื้อหา
                    </q-tooltip>
                  </q-btn>
                </template>
              </q-input>




            </div>
            <q-btn color="info" label="ค้นหา"
              @click="contentSearch(search, selectedstatus, access_role, selectedDept, selectedType, page, page_size)" />
            <q-btn class="border-fill-btn" color="info" label="อัปโหลด" @click="handleUpload" />
          </div>
        </div>
      </q-card-section>

      <q-card-section style="max-height: 90vh; overflow: hidden">
        <div class="table-container">
          <q-table class="custom-table" :rows="filteredDocuments" :columns="columns" row-key="id" bordered dense flat
            virtual-scroll :rows-per-page-options="[0, 10, 20, 30, 50]" :pagination="{ rowsPerPage: 0 }">
            <template v-slot:body-cell-actions="props">
              <q-td :props="props" class="text-center">
                <q-btn flat dense round icon="edit" color="primary" size="md" @click="editDoc(props.row)" />
                <q-btn flat dense round icon="delete" color="negative" size="md" @click="deleteDoc(props.row)" />
              </q-td>
            </template>
          </q-table>
        </div>
      </q-card-section>

      <q-dialog v-model="showDeleteDialog">
        <q-card style="width: 280px; height: 280px; text-align: center">
          <q-card-section>
            <q-icon name="description" size="120px" />
            <p style="margin-top: 10px" class="text">ยกเลิกการใช้งาน</p>
          </q-card-section>
          <q-card-actions align="center" class="button-container">
            <q-btn label="ยืนยัน" color="green" @click="deleteDocComfirm" />
            <q-btn label="ยกเลิก" color="red" @click="showDeleteDialog = false" />
          </q-card-actions>
        </q-card>
      </q-dialog>
      <LoadingDialog/>
    </q-page>
  </q-page-container>

  <router-view />
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ref, computed, onMounted, watchEffect } from 'vue'
import documentService from 'src/services/document'
import { useDepartmentstore } from 'src/stores/department'
import type { Document } from 'src/types/Document'
import { useDocumentstore } from 'src/stores/document'
import { useCategorystore } from 'src/stores/category'
import { useDialogOrPopupstore } from 'src/stores/dialogOrPopup'
import LoadingDialog from 'src/components/LoadingDialog.vue'

const router = useRouter()
const showDeleteDialog = ref(false)
const documentStore = useDocumentstore()
const categoryStore = useCategorystore()
const dialogOrPopupstore = useDialogOrPopupstore()


const handleUpload = async () => {
  await router.push('/uploadDocManage')
}

const search = ref('')

const page = ref(1)
const page_size = ref(5)

//mock test
const access_role = ''

const documentLimitLabel = computed(() => {
  return String(page_size.value)
})


const selectedType = ref<string | null>(null)
const selectedDept = ref<string | null>(null)
const selectedstatus = ref<string | null>('default')

const docTypes = ['ประเภท', 'ประกาศ', 'ระเบียบ', 'ข้อบังคับ', 'คำสั่ง']
const departments = ref<string[]>([]) // เปลี่ยนเป็น ref เพื่อให้สามารถอัพเดทค่าได้

const isSearchByContentSelected = ref(false)
const status = [
  { label: 'สถานะ', value: 'default' },
  { label: 'เปิดการใช้งาน', value: 'Y' },
  { label: 'ปิดการใช้งาน', value: 'N' },
]

const departmentStore = useDepartmentstore() // ใช้ store ของแผนก

const columns = [
  { name: 'index', label: 'ลำดับ', field: 'index', align: 'left' as const },
  { name: 'doc_name', label: 'ชื่อเอกสาร', field: 'doc_name', align: 'left' as const },
  {
    name: 'type',
    label: 'ประเภทเอกสาร',
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    field: (row: any) => row.category?.category_name ?? '-',
    align: 'left' as const,
  },
  {
    name: 'department_name',
    label: 'หน่วยงาน',
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    field: (row: any) => row.department?.department_name ?? '-',
    align: 'left' as const,
  },
  { name: 'created_date', label: 'วันที่ประกาศ', field: 'created_date', align: 'left' as const },
  { name: 'actions', label: 'Action', field: 'actions', align: 'center' as const },
]

const filteredDocuments = computed(() => {
  let docs = [...documentStore.documents]

  if (selectedType.value && selectedType.value !== 'ประเภท') {
    docs = docs.filter((doc) => doc.category.category_name === selectedType.value)
  }

  if (selectedDept.value && selectedDept.value !== 'หน่วยงาน') {
    docs = docs.filter((doc) => doc.department.department_name === selectedDept.value)
  }

  if (selectedstatus.value && selectedstatus.value !== 'default') {
    docs = docs.filter((doc) => String(doc.is_public) === selectedstatus.value)
  }

  if (search.value && !isSearchByContentSelected.value) {
    docs = docs.filter((doc) => doc.doc_name.includes(search.value))
  }

  docs.forEach(doc => {
    if (typeof doc.created_date === "string" && doc.created_date.trim() !== "") {
      const isValidDateFormat = /^\d{2}\/\d{2}\/\d{4}$/.test(doc.created_date);

      if (!isValidDateFormat) {
        const rawDate = doc.created_date.split("T")[0]?.split(" ")[0];

        if (rawDate) {
          const [yearStr, monthStr, dayStr] = rawDate.split("-");

          if (yearStr && monthStr && dayStr) {
            const year = Number(yearStr);
            const month = Number(monthStr);
            const day = Number(dayStr);

            if (!isNaN(year) && !isNaN(month) && !isNaN(day)) {
              const buddhistYear = year + 543;
              doc.created_date = `${day.toString().padStart(2, "0")}/${month.toString().padStart(2, "0")}/${buddhistYear}`;
            }
          }
        }
      }
    }
  });



  // 🔽 เพิ่มการเรียงตามวันที่ล่าสุด
  docs.sort((a, b) => {
    const parseDate = (dateStr: string) => {
      const [day, month, year] = dateStr.split("/").map(Number)
      return new Date(year!, month! - 1, day) // month - 1 เพราะ JavaScript นับเดือนจาก 0-11
    }

    const dateA = parseDate(a.created_date)
    const dateB = parseDate(b.created_date)

    return dateB.getTime() - dateA.getTime()
  })


  return docs.map((doc, index) => ({
    ...doc,
    index: index + 1,
  }))
})

watchEffect(() => {
  console.log('📦 documentStore.documents:', documentStore.documents)
  console.log('📦 filteredDocuments:', filteredDocuments.value)
})

const resetIfDefault = (newValue: string) => {
  selectedType.value = newValue === 'ประเภท' ? null : newValue
}

const resetDept = (newValue: string) => {
  selectedDept.value = newValue === 'หน่วยงาน' ? null : newValue
}

const resetStatus = (newValue: string | null) => {
  selectedstatus.value = newValue === 'สถานะ' ? null : newValue
}

const computedStatus = computed({
  get: () => (selectedstatus.value === 'default' ? null : selectedstatus.value),
  set: (val) => {
    selectedstatus.value = val || 'default' // ✅ ถ้ากดล้างค่า ให้กลับไปเป็น "default"
  },
})

const fetchDocuments = async () => {
  try {
    const response = await documentService.getDocuments()
    documentStore.documents = response.data.map((doc: Document) => ({
      ...doc,
      type: doc.category.category_name,
      created_date: new Date(doc.created_date).toLocaleDateString('th-TH', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      }),
    }))
  } catch (error) {
    console.error('Failed to fetch documents:', error)
  }
}

const fetchDepartments = async () => {
  await departmentStore.getDepartments()
  departments.value = [
    'หน่วยงาน',
    ...departmentStore.departments.map((dept) => dept.department_name),
  ]
}

const editDoc = async (doc: Document) => {
  documentStore.editeddocument = doc
  console.log("documentStore.editeddocument: ", documentStore.editeddocument)
  await router.push('/editDocManage')
}
const deleteDoc = (doc: Document) => {
  documentStore.editeddocument = doc
  showDeleteDialog.value = true
}

const deleteDocComfirm = async () => {
  const formdata = new FormData
  formdata.append('is_public', 'N')
  await documentStore.deleteDoc(documentStore.editeddocument, formdata)
  await fetchDocuments()
  showDeleteDialog.value = false
}
onMounted(async () => {
  dialogOrPopupstore.setLoadingShowAndRename(true,'กำลังโหลดข้อมูล')
  //await accessPermissionStore.getAccessPermissions()
  await fetchDepartments()
  await categoryStore.getCategories()
  await fetchDocuments()
  dialogOrPopupstore.setLoadingShowAndRename(false,'')
})


async function contentSearch(
  query: string | null,
  is_public: string | null,
  access_role: string | null,
  department: string | null,
  category: string | null,
  page: number | null,
  page_size: number | null,
) {
  if (isSearchByContentSelected.value && query) {
    dialogOrPopupstore.setLoadingShowAndRename(true,'กำลังหาข้อมูล')
    try {
      console.log(`query=${query ?? ''}, is_public=${is_public ?? ''}, access_role=${access_role ?? ''}, department=${department ?? ''}, category=${category ?? ''}, page=${page ?? ''}, page_size=${page_size ?? ''}`);
      const dep_id = departmentStore.departments.find(dept => dept.department_name === department)?.department_id;
      const cat_id = categoryStore.categories.find(cat => cat.category_name === category)?.category_id;
      const res = await documentStore.contentSearch(
        query ?? '',
        is_public == 'default' ? '' : is_public,
        access_role ?? '',
        dep_id ?? 0,
        cat_id ?? 0,
        page ?? 1,
        page_size ?? 5,
      );
      if (res) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        documentStore.documents = res.map((doc: any) => {
          const departmentName = doc.department?.trim().toLowerCase() ?? '';
          const categoryName = doc.category?.trim().toLowerCase() ?? '';

          const department = departmentStore.departments.find(
            dep => dep.department_name.trim().toLowerCase() === departmentName
          );

          const category = categoryStore.categories.find(
            cat => cat.category_name.trim().toLowerCase() === categoryName
          );
          return {
            ...doc,
            department: {
              department_id: department?.department_id ?? 0,
              department_name: department?.department_name ?? '',
            },
            category: {
              category_id: category?.category_id ?? 0,
              category_name: category?.category_name ?? '',
            },
          };
        });
      }
      dialogOrPopupstore.setLoadingShowAndRename(false,'')
    } catch (e) {
      dialogOrPopupstore.setLoadingShowAndRename(false,'')
      console.log(e);
    }
  } else {
    await fetchDocuments();
    dialogOrPopupstore.setLoadingShowAndRename(false,'')
  }
}


</script>

<style scoped>
.background-image {
  position: relative;
  overflow: hidden;
}

.background-image::before {
  content: "";
  position: absolute;
  inset: 0;
  background-image: url('/src/assets/img/Document_BG.jpg');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center 16px;
  z-index: 0;
}


.background-image::after {
  content: "";
  position: absolute;
  inset: 0;
  background-color: rgba(17, 35, 173, 0.64);
  margin-top: 16px;
  z-index: 1;
}

.background-image>* {
  position: relative;
  z-index: 2;
  /* ✅ ข้อความอยู่บนสุด ไม่โดนจาง */

}

.q-btn {
  font-size: 16px;
}

.q-select,
.q-input {
  font-size: 14px;
  min-width: 200px;
}

.dropdown {
  width: 100px;
  border-radius: 3px !important;
}

::v-deep(.custom-table thead tr) {
  background-color: #CAE3FF !important;
  color: rgb(0, 0, 0) !important;
}

::v-deep(.custom-table thead th) {
  font-weight: bold;
  font-size: 15px;
}


.table-container {
  max-height: calc(100vh - 200px);
  /* ปรับให้พอดีกับขนาดจอ */
  overflow-y: auto;
  /* ให้ Scroll เฉพาะตัวนี้ */
}

.text {
  font-size: 18px;
}

.t {
  min-height: calc(100vh - 66px);
  overflow-y: auto;
}

.searcher {
  width: 240px;
  max-width: 250px;
  height: 40px;
  padding: 0 0px;
  box-sizing: border-box;
  border-radius: 3px !important;
  overflow: hidden;
}

.button-container {
  display: flex;
  justify-content: center;
  /* จัดให้อยู่ตรงกลาง */
  align-items: center;
  /* จัดให้อยู่แนวเดียวกัน */
  gap: 20px;
  /* ระยะห่างระหว่างปุ่ม */
  flex-wrap: nowrap;
  /* ❌ ป้องกันปุ่มตกบรรทัด */
}

.action-btn {
  min-width: 100px;
  /* ป้องกันปุ่มเล็กเกินไป */
  max-width: 150px;
  /* จำกัดขนาดปุ่ม */
  flex-shrink: 0;
  /* ❌ ป้องกันปุ่มหดตัว */
}

.border-fill-btn {
  border: none;
  background: white;
  color: #4eaceb;
  overflow: hidden;
  z-index: 1;
}

.border-fill-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #4eaceb;
  transform: scaleX(0);
  transform-origin: left;
  transition: transform .4s;
  z-index: -2;
}

.border-fill-btn:hover {
  color: #ffffff;
}

.border-fill-btn:hover::after {
  transform: scaleX(1);
}
</style>
