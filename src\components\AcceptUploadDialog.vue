<template>
  <q-dialog persistent transition-show="scale" transition-hide="scale">
    <q-card class="premium-dialog">
      <!-- Header Section -->
      <div class="dialog-header">
        <div class="icon-container">
          <div class="success-icon-wrapper">
            <q-icon name="check_circle" class="main-icon" />
          </div>
        </div>
        <div class="header-text">
          <h2 class="dialog-title">ยืนยันการอัปโหลด</h2>
          <p class="dialog-subtitle">คุณต้องการอัปโหลดเอกสารนี้หรือไม่?</p>
        </div>
      </div>

      <!-- Content Section -->
      <div class="dialog-body">
        <div class="confirmation-list">
          <div class="confirmation-item">
            <div class="confirmation-icon">
              <q-icon name="description" />
            </div>
            <span class="confirmation-text">เอกสารจะถูกบันทึกและเผยแพร่ในระบบ</span>
          </div>

          <div class="confirmation-item">
            <div class="confirmation-icon">
              <q-icon name="security" />
            </div>
            <span class="confirmation-text">สิทธิการเข้าถึงจะถูกกำหนดตามที่ระบุ</span>
          </div>
        </div>
      </div>

      <!-- Actions Section -->
      <div class="dialog-actions">
        <q-btn class="secondary-btn" label="ยกเลิก" no-caps flat @click="dialogOrPopupStore.showConfirmDialog = false"
          icon="close" />
        <q-btn class="primary-btn success-btn" label="ยืนยันอัปโหลด" no-caps @click="addDoc" icon="cloud_upload"
          :loading="isUploading" />
      </div>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { toRaw, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useDocumentstore } from 'src/stores/document'
import { useDepartmentstore } from 'src/stores/department';
import { useCategorystore } from 'src/stores/category';
import { useDialogOrPopupstore } from 'src/stores/dialogOrPopup';
import { useAccessPermissionstore } from 'src/stores/accessPermmission';

const documentStore = useDocumentstore()
const router = useRouter()
const departmentStore = useDepartmentstore()
const categoryStore = useCategorystore()
const dialogOrPopupStore = useDialogOrPopupstore()
const accessPermissionstore = useAccessPermissionstore()

// Loading state for upload button
const isUploading = ref(false)

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const toThaiDateString = (date: Date): string => {
  const day = String(date.getDate()).padStart(2, '0')
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const year = date.getFullYear() + 543 // แปลงเป็นปี พ.ศ.
  return `${day}/${month}/${year}`
}


async function tranferDocObj() {
  try {
    if (!documentStore.editeddocument.summary) {
      window.alert(`กรุณาดำเนินการสรุปเนื้อหา`)
      return
    }
    documentStore.editeddocument.doc_name = documentStore.tranferDocument.doc_name
    documentStore.editeddocument.is_public = 'Y'
    console.log("documentStore.tranferDocument.created_date: ", documentStore.tranferDocument.created_date)
    documentStore.editeddocument.created_date = documentStore.tranferDocument.created_date
    // const rawDate = new Date(documentStore.tranferDocument.created_date)
    // const formatted = toThaiDateString(rawDate)
    // documentStore.editeddocument.created_date = formatted

    const dept = toRaw(
      departmentStore.departments.find((d) => d.department_name === documentStore.tranferDocument.department.department_name),
    ) || { department_id: 1, department_name: 'คณะวิทยาการสารสนเทศ' }
    console.log("documentStore.tranferDocument.department: ", documentStore.tranferDocument.department)
    console.log("dept: ", dept)
    const cat = toRaw(
      categoryStore.categories.find((c) => c.category_name === documentStore.tranferDocument.category.category_name),
    ) || { category_id: 3, category_name: 'ประกาศ' }
    console.log("documentStore.tranferDocument.category: ", documentStore.tranferDocument.category)
    console.log("cat: ", cat)
    documentStore.editeddocument.category = { ...cat }
    documentStore.editeddocument.department = { ...dept }

    //ของที่ต้องทำหลัง save document
    //documentStore.editeddocument.previous
    // const previous_version = await documentStore.checkPrevious(documentStore.editeddocument.doc_name)
    // if (previous_version.previous == 0) {
    //   documentStore.editeddocument.previous = previous_version.previous
    // } else {
    //   documentStore.editeddocument.previous = previous_version.previous + 1
    // }

    //save access permission
    // accessPermissionstore.editedAccessPermission.doc_id = await documentStore.findDocId(documentStore.editeddocument)
    // await accessPermissionstore.saveAccessPermissions(accessPermissionstore.editedAccessPermission)

    //formdata
    const formData = new FormData();
    //ส่งไป update
    formData.append("is_public", documentStore.editeddocument.is_public);
    // ทำความสะอาด highlight ก่อน save
    const cleanOcrResults = documentStore.ocrResults.map(p => ({
      ...p,
      content: removeHighlightTags(p.content)
    }))
    formData.append("content", JSON.stringify(cleanOcrResults))
    const access_permission = accessPermissionstore.editedAccessPermission?.roles || [];
    formData.append("access_permission", JSON.stringify(access_permission));
    formData.append("created_date", documentStore.editeddocument.created_date);
    formData.append("doc_name", documentStore.editeddocument.doc_name);
    formData.append("summary", removeSurroundingQuotes(JSON.stringify(documentStore.editeddocument.summary)) || '');
    formData.append("department_id", documentStore.editeddocument.department?.department_id.toString() || '1');
    formData.append("category_id", documentStore.editeddocument.category?.category_id.toString() || '3');
    //ส่งไป create
    if (documentStore.editeddocument.doc_id < 1) {
      formData.append("previous", documentStore.editeddocument.previous?.toString() || '0');

      if (documentStore.file) {
        formData.append("pdf", documentStore.file);
      } else {
        console.log("No PDF file selected");
      }
    }


    for (const [key, value] of formData.entries()) {
      console.log(key, value);
    }
    const response = await documentStore.saveDoc(formData)
    console.log("Upload success:", response);

    //จบพวก document ต่อไป save AccessPermission
    resetAboutUpload()
    await router.push('/DocManage')
  } catch (e) {
    console.log("save error", e)
    throw e
  }
}
function removeHighlightTags(html: string): string {
  return html.replace(
    /<span\s+style="color:\s*red;\s*font-weight:\s*bold;\s*display:\s*inline;">(.*?)<\/span>/g,
    '$1'
  )
}


function removeSurroundingQuotes(text: string) {
  if (!text) return text

  // เช็คว่ามี " คร่อมอยู่รอบนอกจริง ๆ หรือไม่
  if (text.startsWith('"') && text.endsWith('"')) {
    return text.slice(1, -1)
  }

  return text
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
function convertThaiDate(thaiDate?: string): string {
  if (!thaiDate) return '';
  const [day, month, year] = thaiDate.split('/');
  if (!year || !month || !day) return '';
  const gregorianYear = parseInt(year) - 543;
  return `${gregorianYear}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
}



async function addDoc() {
  try {
    isUploading.value = true
    await tranferDocObj()
    dialogOrPopupStore.showConfirmDialog = false
  } catch (e) {
    console.log('error: ', e)
    dialogOrPopupStore.showConfirmDialog = false
  } finally {
    isUploading.value = false
  }
}

function resetAboutUpload() {
  documentStore.resetFile()
  documentStore.editeddocument = documentStore.initialdocument
  documentStore.tranferDocument = documentStore.initialdocument
  accessPermissionstore.editedAccessPermission = accessPermissionstore.initialAccessPermission
}
</script>

<style scoped>
/* ===== PREMIUM DIALOG STYLING ===== */
.premium-dialog {
  width: 420px;
  max-width: 90vw;
  border-radius: 20px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.12),
    0 8px 25px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.06);
  overflow: hidden;
  position: relative;
}

.premium-dialog::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #059669 0%, #10b981 50%, #34d399 100%);
}

/* ===== DIALOG HEADER ===== */
.dialog-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 24px 24px 20px 24px;
  background: rgba(255, 255, 255, 0.8);
}

.icon-container {
  flex-shrink: 0;
}

.success-icon-wrapper {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  background: linear-gradient(135deg, #059669 0%, #10b981 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 20px rgba(5, 150, 105, 0.25);
}

.main-icon {
  font-size: 28px;
  color: white;
}

.header-text {
  flex: 1;
  padding-top: 2px;
}

.dialog-title {
  font-size: 20px;
  font-weight: 700;
  color: #059669;
  margin: 0 0 6px 0;
  line-height: 1.3;
}

.dialog-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  font-weight: 500;
  line-height: 1.4;
}

/* ===== DIALOG BODY ===== */
.dialog-body {
  padding: 0 24px 20px 24px;
}

.confirmation-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.confirmation-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 14px 16px;
  background: rgba(220, 252, 231, 0.4);
  border-radius: 12px;
  border: 1px solid rgba(5, 150, 105, 0.1);
  transition: all 0.2s ease;
}

.confirmation-item:hover {
  background: rgba(220, 252, 231, 0.6);
  border-color: rgba(5, 150, 105, 0.2);
}

.confirmation-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: rgba(5, 150, 105, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.confirmation-icon .q-icon {
  font-size: 16px;
  color: #059669;
}

.confirmation-text {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
  line-height: 1.4;
}

/* ===== DIALOG ACTIONS ===== */
.dialog-actions {
  padding: 20px 24px 24px 24px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  background: rgba(248, 250, 252, 0.5);
  border-top: 1px solid rgba(0, 0, 0, 0.06);
}

.secondary-btn {
  padding: 10px 20px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
  color: #6b7280;
  background: transparent;
  border: none;
  transition: all 0.2s ease;
  min-width: 100px;
}

.secondary-btn:hover {
  background: rgba(107, 114, 128, 0.1);
  color: #4b5563;
}

.primary-btn {
  padding: 10px 20px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
  color: white;
  border: none;
  transition: all 0.2s ease;
  min-width: 120px;
}

.success-btn {
  background: linear-gradient(135deg, #059669 0%, #10b981 100%);
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
}

.success-btn:hover {
  background: linear-gradient(135deg, #047857 0%, #059669 100%);
  box-shadow: 0 6px 16px rgba(5, 150, 105, 0.4);
  transform: translateY(-1px);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 600px) {
  .premium-dialog {
    width: 95vw;
    margin: 16px;
  }

  .dialog-header {
    padding: 20px 20px 16px 20px;
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .dialog-body {
    padding: 0 20px 16px 20px;
  }

  .dialog-actions {
    padding: 16px 20px 20px 20px;
    flex-direction: column;
    gap: 8px;
  }

  .secondary-btn,
  .primary-btn {
    width: 100%;
    min-width: unset;
  }
}
</style>
