import { describe, it, expect, vi, beforeEach } from 'vitest'
import { setActive<PERSON><PERSON>, createPinia } from 'pinia'
import { useUserStore } from 'src/stores/user'
import userService from 'src/services/user'
import type { User } from 'src/types/User'
import { createAxiosResponse } from 'src/test/test-utils'

// Mock the user service
vi.mock('src/services/user')
const mockedUserService = vi.mocked(userService)

describe('User Store', () => {
  let userStore: ReturnType<typeof useUserStore>

  const mockUser: User = {
    user_id: 1,
    username: 'testuser',
    password: 'password123',
    full_name: 'Test User',
    role: {
      role_id: 1,
      role_name: 'Admin',
    },
    department: {
      department_id: 1,
      department_name: 'IT Department',
    },
  }

  beforeEach(() => {
    setActivePinia(createPinia())
    userStore = useUserStore()
    vi.clearAllMocks()
  })

  describe('initial state', () => {
    it('should have correct initial state', () => {
      expect(userStore.initialUser).toBeDefined()
      expect(userStore.initialUser.user_id).toBe(0)
      expect(userStore.initialUser.username).toBe('')
      expect(userStore.initialUser.password).toBe('')
      expect(userStore.initialUser.full_name).toBe('')
      expect(userStore.initialUser.role.role_id).toBe(0)
      expect(userStore.initialUser.role.role_name).toBe('')
      expect(userStore.initialUser.department.department_id).toBe(1)
      expect(userStore.initialUser.department.department_name).toBe('')
    })

    it('should have form with initial data', () => {
      expect(userStore.form).toBeDefined()
      expect(Array.isArray(userStore.form)).toBe(true)
      expect(userStore.form).toHaveLength(1)
      expect(userStore.form[0]?.username).toBe('Bank')
      expect(userStore.form[0]?.full_name).toBe('FIM Yai')
    })
  })

  describe('getUser action', () => {
    it('should fetch user successfully', async () => {
      const mockResponse = createAxiosResponse(mockUser)
      mockedUserService.getUser.mockResolvedValue(mockResponse)

      await userStore.getUser(1)

      expect(mockedUserService.getUser).toHaveBeenCalledWith(1)
      expect(mockedUserService.getUser).toHaveBeenCalledTimes(1)
    })

    it('should handle error when fetching user', async () => {
      const mockError = new Error('User not found')
      mockedUserService.getUser.mockRejectedValue(mockError)

      // Mock console.error to avoid error output in tests
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      await userStore.getUser(999)

      expect(mockedUserService.getUser).toHaveBeenCalledWith(999)
      expect(consoleSpy).toHaveBeenCalledWith('Error fetching user:', mockError)

      consoleSpy.mockRestore()
    })

    it('should call getUser with correct parameters', async () => {
      const mockResponse = createAxiosResponse(mockUser)
      mockedUserService.getUser.mockResolvedValue(mockResponse)

      await userStore.getUser(5)

      expect(mockedUserService.getUser).toHaveBeenCalledWith(5)
    })
  })

  describe('store structure', () => {
    it('should export correct properties and methods', () => {
      expect(userStore).toHaveProperty('initialUser')
      expect(userStore).toHaveProperty('form')
      expect(userStore).toHaveProperty('getUser')
      expect(typeof userStore.getUser).toBe('function')
    })

    it('should have reactive form data', () => {
      expect(userStore.form).toBeDefined()
      expect(userStore.form[0]).toHaveProperty('user_id')
      expect(userStore.form[0]).toHaveProperty('username')
      expect(userStore.form[0]).toHaveProperty('password')
      expect(userStore.form[0]).toHaveProperty('full_name')
      expect(userStore.form[0]).toHaveProperty('role')
      expect(userStore.form[0]).toHaveProperty('department')
    })
  })

  describe('user data validation', () => {
    it('should validate initial user structure', () => {
      const { initialUser } = userStore

      expect(typeof initialUser.user_id).toBe('number')
      expect(typeof initialUser.username).toBe('string')
      expect(typeof initialUser.password).toBe('string')
      expect(typeof initialUser.full_name).toBe('string')
      expect(typeof initialUser.role).toBe('object')
      expect(typeof initialUser.department).toBe('object')
    })

    it('should validate form data structure', () => {
      const firstUser = userStore.form[0]

      expect(firstUser).toBeDefined()
      expect(typeof firstUser?.user_id).toBe('number')
      expect(typeof firstUser?.username).toBe('string')
      expect(typeof firstUser?.password).toBe('string')
      expect(typeof firstUser?.full_name).toBe('string')
      expect(typeof firstUser?.role).toBe('object')
      expect(typeof firstUser?.department).toBe('object')
    })
  })

  describe('store reactivity', () => {
    it('should maintain reactivity when accessing properties', () => {
      const initialFormLength = userStore.form.length
      expect(initialFormLength).toBe(1)

      // The form should be reactive
      expect(userStore.form).toBeDefined()
      expect(userStore.initialUser).toBeDefined()
    })
  })
})
