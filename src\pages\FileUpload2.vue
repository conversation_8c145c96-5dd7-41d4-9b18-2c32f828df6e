<template>
  <q-page-container>
    <q-page class="q-pa-none no-scroll-page">
      <div class="outer-wrapper">
        <div class="full-page-row">
          <!-- ซ้าย -->
          <div class="left-panel">
            <div class="left-layout">
              <!-- 🔵 Top: Header + Buttons -->
              <div class="top-section">
                <div class="header-container">
                  <div class="title-section">
                    <div class="title-icon-wrapper">
                      <q-icon name="cloud_upload" size="36px" class="title-icon" />
                    </div>
                    <div class="title-text">
                      <h3 class="main-title">อัปโหลดเอกสาร</h3>
                      <p class="subtitle">เลือกไฟล์เพื่อเริ่มต้นการประมวลผล</p>
                    </div>
                  </div>

                  <div class="action-buttons">
                    <q-btn label="สแกนไฟล์"
                      :color="documentStore.file ? (activeButton === 'scan' ? 'positive' : 'primary') : 'primary'"
                      :class="['modern-btn', 'scan-btn', { 'active-btn': activeButton === 'scan' }]"
                      :loading="isLoading" :disable="!documentStore.file" @click="scanFile" icon="document_scanner"
                      no-caps unelevated>
                      <template v-slot:loading>
                        <q-spinner-dots color="white" size="20px" />
                      </template>
                      <q-tooltip v-if="!documentStore.file" class="bg-negative">
                        กรุณาเลือกไฟล์ก่อนสแกน
                      </q-tooltip>
                    </q-btn>

                    <q-btn label="พิสูจน์ตัวอักษร" :color="activeButton === 'proofread' ? 'positive' : 'secondary'"
                      :class="['modern-btn', 'proofread-btn', { 'active-btn': activeButton === 'proofread' }]"
                      @click="toggleClicked" icon="spellcheck" no-caps unelevated />

                    <q-btn label="แก้ไขข้อมูล" :color="activeButton === 'edit' ? 'positive' : 'accent'"
                      :class="['modern-btn', 'edit-btn', { 'active-btn': activeButton === 'edit' }]"
                      @click="showMetadata" icon="edit_document" no-caps unelevated />
                  </div>
                </div>
              </div>

              <!-- ⚪ Center: Upload Box -->
              <div class="center-section">
                <div class="upload-container">
                  <div :class="['modern-upload-box', { 'has-file': documentStore.filePreview }]">
                    <div class="upload-area" @dragover.prevent="handleDragOver" @dragleave.prevent="handleDragLeave"
                      @drop="handleDrop" :class="{ 'drag-active': isDragActive }"
                      @click="!documentStore.filePreview && uploadfile()">

                      <input type="file" ref="fileInputUi" @change="onFileChange" style="display: none" />

                      <!-- Empty State - No File -->
                      <div v-if="!documentStore.filePreview" class="empty-state">
                        <div class="upload-icon-container">
                          <q-icon name="cloud_upload" class="upload-main-icon" />
                          <div class="upload-pulse"></div>
                        </div>

                        <div class="upload-content">
                          <h4 class="upload-title">ลากไฟล์มาวางที่นี่</h4>
                          <p class="upload-subtitle">หรือคลิกเพื่อเลือกไฟล์จากเครื่องของคุณ</p>

                          <div class="file-types-info">
                            <div class="supported-types">
                              <div class="file-type-badge pdf-badge">
                                <q-icon name="picture_as_pdf" />
                                <span>PDF</span>
                              </div>
                              <div class="file-type-badge docx-badge">
                                <q-icon name="description" />
                                <span>DOCX</span>
                              </div>
                            </div>
                            <p class="size-limit">ขนาดไฟล์สูงสุด: <strong>10MB</strong></p>
                          </div>
                        </div>

                        <div class="upload-action">
                          <q-btn label="เลือกไฟล์" color="primary" class="select-file-btn" @click.stop="uploadfile()"
                            icon="folder_open" no-caps unelevated size="lg" />
                        </div>
                      </div>

                      <!-- File Preview State -->
                      <div v-else class="file-preview-container">
                        <div class="file-preview-header">
                          <div class="file-info">
                            <q-icon :name="isPDF ? 'picture_as_pdf' : 'description'" class="file-icon" />
                            <div class="file-details">
                              <p class="file-name">{{ documentStore.file?.name }}</p>
                              <p class="file-size">{{ formatFileSize(documentStore.file?.size) }}</p>
                            </div>
                          </div>

                          <!-- File Viewing Controls -->
                          <div class="file-controls">
                            <q-btn icon="close" flat round class="remove-file-btn" @click="removeFile" size="sm">
                              <q-tooltip>ลบไฟล์</q-tooltip>
                            </q-btn>
                          </div>
                        </div>

                        <div class="file-preview-content" :class="{ 'fullscreen-preview': isFullscreen }">
                          <div class="document-viewer-container" :style="{ transform: `scale(${zoomLevel})` }">
                            <iframe v-if="isPDF" :src="documentStore.filePreview" class="file-preview-iframe" />
                            <div v-if="isDoc" ref="docxContainer" class="docx-preview">
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                </div>
              </div>

              <!-- ⚫ Bottom: Action Section -->
              <div v-if="documentStore.filePreview" class="bottom-section">
                <div class="upload-actions">
                  <q-btn label="เลือกไฟล์ใหม่" color="primary" class="process-btn"
                    :disable="dialogOrPopupStore.showCatCoffeeGifWaiting" @click="uploadfile()" icon="folder_open"
                    no-caps unelevated size="lg" />
                  <p class="file-support-info">
                    รองรับไฟล์ <strong>DOC, DOCX และ PDF</strong> ขนาดไม่เกิน <strong>10MB</strong>
                  </p>
                </div>
              </div>
            </div>
          </div>

          <FileWarningDialog v-model="dialogOrPopupStore.showFilewarningDialog"></FileWarningDialog>
          <!-- ขวา -->
          <div class="right-panel">

            <div class="right-content">
              <div class="content-section extract-section">
                <GifWaiting v-if="dialogOrPopupStore.showCatCoffeeGifWaiting" class="loading-overlay" />

                <div v-if="!dialogOrPopupStore.showCatCoffeeGifWaiting" class="extract-content">
                  <!-- Enhanced Page Info Bar -->
                  <div class="page-info-bar">
                    <div class="page-info-left">
                      <div class="page-counter">
                        <div class="page-icon-wrapper">
                          <q-icon name="description" class="page-icon" />
                        </div>
                        <div class="page-details">
                          <div class="page-number">หน้าที่ {{ documentStore.ocrResults[currentPageIndex]?.page || 0 }}
                          </div>
                          <div class="page-total">จาก {{ documentStore.ocrResults.length }} หน้า</div>
                        </div>
                      </div>
                    </div>

                    <div class="page-info-center">
                      <!-- Enhanced Page Navigation -->
                      <div class="page-navigation">
                        <q-btn icon="first_page" @click="goToFirstPage" :disable="currentPageIndex === 0"
                          class="nav-btn first-btn" flat round size="sm">
                          <q-tooltip>หน้าแรก</q-tooltip>
                        </q-btn>

                        <q-btn icon="chevron_left" @click="goToPreviousPage" :disable="currentPageIndex === 0"
                          class="nav-btn prev-btn" flat round size="sm">
                          <q-tooltip>หน้าก่อนหน้า</q-tooltip>
                        </q-btn>

                        <div class="page-input-wrapper">
                          <q-input v-model.number="currentPageDisplay" @keyup.enter="goToPage" @blur="goToPage"
                            class="page-input" dense outlined :min="1" :max="documentStore.ocrResults.length"
                            type="number" />
                        </div>

                        <q-btn icon="chevron_right" @click="goToNextPage"
                          :disable="currentPageIndex >= documentStore.ocrResults.length - 1" class="nav-btn next-btn"
                          flat round size="sm">
                          <q-tooltip>หน้าถัดไป</q-tooltip>
                        </q-btn>

                        <q-btn icon="last_page" @click="goToLastPage"
                          :disable="currentPageIndex >= documentStore.ocrResults.length - 1" class="nav-btn last-btn"
                          flat round size="sm">
                          <q-tooltip>หน้าสุดท้าย</q-tooltip>
                        </q-btn>
                      </div>
                    </div>

                    <div class="page-info-right">
                      <!-- Help & Status -->
                      <q-btn icon="help_outline" flat round size="sm" class="help-btn">
                        <q-tooltip class="bg-info text-white" style="font-size: 14px; max-width: 300px;">
                          <div class="tooltip-content">
                            <div class="tooltip-title">💡 คำแนะนำการใช้งาน</div>
                            <div class="tooltip-item">• ใช้ปุ่มลูกศรเพื่อเปลี่ยนหน้า</div>
                            <div class="tooltip-item">• พิมพ์หมายเลขหน้าเพื่อไปยังหน้าที่ต้องการ</div>
                            <div class="tooltip-item">• แก้ไขข้อความได้โดยตรงในตัวแก้ไข</div>
                          </div>
                        </q-tooltip>
                      </q-btn>

                      <q-btn icon="warning" color="orange" flat round size="sm" class="warning-btn">
                        <q-tooltip class="bg-orange text-white" style="font-size: 13px; max-width: 350px;">
                          <div class="tooltip-content">
                            <div class="tooltip-title">⚠️ ข้อควรระวัง</div>
                            <div class="tooltip-item">• การกด Undo ซ้ำ ๆ อาจทำให้เนื้อหาซ้ำ</div>
                            <div class="tooltip-item">• การปรับขนาดตัวอักษรอาจไม่แบ่งหน้าอัตโนมัติ</div>
                            <div class="tooltip-item">• แนะนำให้รีเฟรชหน้าหากพบปัญหา</div>
                          </div>
                        </q-tooltip>
                      </q-btn>
                    </div>
                  </div>

                  <div class="editor-container">
                    <div ref="editorRef" class="text-editor"></div>
                  </div>
                </div>
              </div>
              <div class="content-section words-section">
                <!-- Empty State -->
                <div v-if="!documentStore.showTable && !showMeta" class="empty-state-words">
                  <div class="empty-content">
                    <q-icon name="spellcheck" size="48px" class="empty-icon" />
                    <h6 class="empty-title">พร้อมตรวจสอบคำผิด</h6>
                    <p class="empty-subtitle">กดปุ่ม "พิสูจน์ตัวอักษร" เพื่อเริ่มการตรวจสอบ</p>
                  </div>
                </div>

                <!-- Words Table -->
                <div v-if="documentStore.showTable" class="words-table-container">
                  <div class="table-header">
                    <div class="legend">
                      <div class="legend-item">
                        <div class="legend-color error-color"></div>
                        <span>คำที่มีโอกาสผิด</span>
                      </div>
                    </div>
                  </div>

                  <div class="table-wrapper">
                    <q-table :rows="documentStore.wrongWords" :columns="columns" row-key="word" dense flat
                      class="modern-table" :pagination="{ rowsPerPage: 0 }" virtual-scroll
                      :virtual-scroll-item-size="48">

                      <template v-slot:body-cell-word="props">
                        <q-td :props="props" class="word-cell">
                          <div class="word-item">
                            <div :class="['status-indicator', {
                              'error-color': props.row.shouldBe && props.row.shouldBe.trim() !== '',
                              'no-suggestion': !props.row.shouldBe || props.row.shouldBe.trim() === ''
                            }]"></div>
                            <span class="word-text">{{ props.row.word }}</span>
                          </div>
                        </q-td>
                      </template>

                      <template v-slot:body-cell-suggest="props">
                        <q-td :props="props" class="suggest-cell">
                          <span class="suggest-text">{{ props.row.suggest || '-' }}</span>
                        </q-td>
                      </template>
                    </q-table>
                  </div>
                </div>

                <!-- Metadata Section -->
                <div v-if="showMeta && !documentStore.showTable" class="metadata-container">
                  <MetaDataUpload />
                </div>

                <!-- dialog ปุ่มกด สรุปเนื้อหา -->
                <SummryDialog v-model="dialogOrPopupStore.showDialogsummry" :initial-summary="summaryText"
                  :initSummaryText="summaryText" @summarize-request="summarizeWithBot"
                  @update:summary="(val: any) => (summaryText = val)" />
                <!-- dialog ปุ่มกด ยกเลิก -->
                <CancelUploadDialog v-model="dialogOrPopupStore.showCancelDialog" />

                <!-- dialog ปุ่มกด ยืนยัน -->
                <AcceptUploadDialog v-model="dialogOrPopupStore.showConfirmDialog" />
              </div>

            </div>
          </div>
        </div>
      </div>
    </q-page>
  </q-page-container>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'
import { useDocumentstore } from 'src/stores/document'
import { useDepartmentstore } from 'src/stores/department'
import { useCategorystore } from 'src/stores/category'
// import documentService from 'src/services/document'
import CancelUploadDialog from 'src/components/CancelUploadDialog.vue'
import AcceptUploadDialog from 'src/components/AcceptUploadDialog.vue'
import MetaDataUpload from 'src/components/MetaDataUpload.vue'
import GifWaiting from 'src/components/GifWaiting.vue'
import { useDialogOrPopupstore } from 'src/stores/dialogOrPopup'
import FileWarningDialog from 'src/components/FileWarningDialog.vue'
import SummryDialog from 'src/components/SummryDialog.vue'
import * as pdfjsLib from 'pdfjs-dist/legacy/build/pdf.mjs'
import workerSrc from 'pdfjs-dist/legacy/build/pdf.worker.mjs?url'
import { renderAsync } from 'docx-preview'

// eslint-disable-next-line @typescript-eslint/no-explicit-any
declare const html2pdf: any

const isLoading = ref(false)
const isDragActive = ref(false)
const uploadProgress = ref(0)
const uploadMessage = ref('')
const zoomLevel = ref(1) // Re-enabled for file viewing
const isFullscreen = ref(false)

const documentStore = useDocumentstore()
const departmentStore = useDepartmentstore()
const categoryStore = useCategorystore()
const dialogOrPopupStore = useDialogOrPopupstore()

const activeButton = ref<null | string>(null)
const showMeta = ref(false)
const fileInputUi = ref<HTMLInputElement | null>(null)
documentStore.ocrResults = [{ page: 1, content: '' }]

// Computed properties for upload progress (kept for future use)
// const progressOffset = computed(() => {
//   const circumference = 2 * Math.PI * 54 // radius = 54
//   return circumference - (uploadProgress.value / 100) * circumference
// })

onMounted(async () => {
  documentStore.resetFile()
  if (departmentStore.departments.length === 0) {
    await departmentStore.getDepartments()
  }
  if (categoryStore.categories.length === 0) {
    await categoryStore.getCategories()
  }
  if (documentStore.ocrResults.length === 0) {
    documentStore.ocrResults = [{ page: 1, content: '' }]
  }

  // Add keyboard shortcuts for file viewing
  document.addEventListener('keydown', handleKeyboardShortcuts)

  // Add mouse wheel zoom support for file viewing
  document.addEventListener('wheel', handleMouseWheel, { passive: false })
})

// Cleanup event listeners
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyboardShortcuts)
  document.removeEventListener('wheel', handleMouseWheel)
})

// Keyboard shortcuts handler
const handleKeyboardShortcuts = (event: KeyboardEvent) => {
  if (!documentStore.filePreview) return

  // Zoom controls
  if (event.ctrlKey || event.metaKey) {
    switch (event.key) {
      case '=':
      case '+':
        event.preventDefault()
        zoomIn()
        break
      case '-':
        event.preventDefault()
        zoomOut()
        break
      case '0':
        event.preventDefault()
        fitToScreen()
        break
      case 'f':
      case 'F':
        event.preventDefault()
        toggleFullscreen()
        break
    }
  }

  // ESC to exit fullscreen or close document
  if (event.key === 'Escape') {
    if (isFullscreen.value) {
      toggleFullscreen()
    } else {
      removeFile()
    }
  }
}

// Mouse wheel zoom handler
const handleMouseWheel = (event: WheelEvent) => {
  if (!documentStore.filePreview) return

  if (event.ctrlKey || event.metaKey) {
    event.preventDefault()

    if (event.deltaY < 0) {
      zoomIn()
    } else {
      zoomOut()
    }
  }
}



// เช็คว่าไฟล์ที่เลือกเป็นประเภท image หรือไม่
//const isImage = ref(false)
const isPDF = ref(false)
const isDoc = ref(false)

const docxContainer = ref<HTMLElement>()

// ฟังก์ชันที่ทำงานเมื่อเลือกไฟล์
const onFileChange = async (event: Event) => {
  const input = event.target as HTMLInputElement
  console.log('ไฟล์ผ่าน onFileChange compute function', input.files?.[0])

  isPDF.value = false
  isDoc.value = false
  const maxSizeMB = 10
  const maxSizeBytes = maxSizeMB * 1024 * 1024

  if (input) {
    const selectedFile = input.files?.[0]

    if (selectedFile) {
      documentStore.file = selectedFile as unknown as File
      documentStore.filePreview = URL.createObjectURL(selectedFile)

      const isSupportedType =
        selectedFile.type === 'application/pdf' ||
        selectedFile.name.endsWith('.docx') ||
        selectedFile.name.endsWith('.doc')

      if (!isSupportedType) {
        window.alert(`กรุณาอัปโหลดเฉพาะไฟล์ .doc, .docx หรือ .pdf เท่านั้น`)
        documentStore.resetFile()
        return
      } else if (selectedFile.size > maxSizeBytes) {
        dialogOrPopupStore.showFilewarningDialog = true
        documentStore.resetFile()
        return
      }

      isPDF.value = selectedFile.type === 'application/pdf'
      isDoc.value = selectedFile.name.endsWith('.docx') || selectedFile.name.endsWith('.doc')

      // ✅ ถ้า PDF ทำงานเหมือน demo html ของคุณ
      if (isPDF.value == true) {
        pdfjsLib.GlobalWorkerOptions.workerSrc = workerSrc
        const arrayBuffer = await selectedFile.arrayBuffer()
        const pdf = await pdfjsLib.getDocument({ data: new Uint8Array(arrayBuffer) }).promise

        // ✅ เก็บเนื้อหาทั้งหมดแบบ array ของ { page, content }
        const pagesContent: { page: number, content: string }[] = []

        for (let i = 1; i <= pdf.numPages; i++) {
          const page = await pdf.getPage(i)
          const textContent = await page.getTextContent()

          interface LineGroup {
            y: number
            items: string[]
          }

          const lines: LineGroup[] = []

          textContent.items.forEach(item => {
            // ตรวจว่า item มี str และเป็น TextItem
            if ('str' in item && typeof item.str === 'string' && Array.isArray(item.transform)) {
              const y = item.transform[5] // Y position
              let found = false

              for (const line of lines) {
                if (Math.abs(line.y - y) < 2) {
                  line.items.push(item.str)
                  found = true
                  break
                }
              }

              if (!found) {
                lines.push({ y, items: [item.str] })
              }
            }
          })

          // เรียงบรรทัดจากบนลงล่าง
          lines.sort((a, b) => b.y - a.y)

          // รวมข้อความแต่ละบรรทัด
          const pageText = lines.map(line => line.items.join(' ')).join('<br>')

          pagesContent.push({
            page: i,
            content: pageText
          })
        }



        console.log('✅ Pages content:', pagesContent)
        documentStore.ocrResults = pagesContent
      } else if (isDoc.value == true) {
        await nextTick()
        if (docxContainer.value) {
          const arrayBuffer = await selectedFile.arrayBuffer();
          await renderAsync(arrayBuffer, docxContainer.value, undefined);
          const opt = {
            margin: 0.5,
            filename: "output.pdf",
            image: { type: "jpeg", quality: 1 },
            html2canvas: { scale: 2 },
            jsPDF: { unit: "in", format: "a4", orientation: "portrait" },
          };
          isDoc.value = false
          const pdfBlob = await html2pdf()
            .set(opt)
            .from(docxContainer.value)
            .outputPdf("blob");
          documentStore.filePreview = URL.createObjectURL(pdfBlob)
          isPDF.value = true
        } else {
          console.warn("docxContainer ยังไม่ได้ mount")
        }
      }


      if (documentStore.file instanceof Blob) {
        const filename = selectedFile.name
        const fileType = selectedFile.type
        const fileToAppend = new File([documentStore.file], filename, { type: fileType })

        documentStore.pdfForm.append('file', fileToAppend)

        console.log('File appended to FormData:', documentStore.pdfForm)
        for (const [key, value] of documentStore.pdfForm.entries()) {
          console.log(key, value)
        }
      }
    }
  }
}


function uploadfile() {
  if (fileInputUi.value) {
    documentStore.resetFile()
  }
  fileInputUi.value?.click()
  documentStore.fileInput = fileInputUi.value
  console.log("test doc: ", documentStore.fileInput)
  documentStore.ocrResults = [{ page: 1, content: '' }]

  // If there's already a file selected, start the upload process
  if (documentStore.file) {
    simulateUploadProgress()
  }
}

const handleDrop = async (event: DragEvent) => {
  event.preventDefault()
  isDragActive.value = false

  const files = event.dataTransfer?.files
  if (files && files.length) {
    const selectedFile = files[0]

    // สร้าง event ปลอมให้ตรงกับรูปแบบของ onFileChange
    const fakeEvent = {
      target: { files: [selectedFile] },
    } as unknown as Event // แปลงเป็น Event โดยใช้ unknown ก่อน

    // เรียก onFileChange
    await onFileChange(fakeEvent)
  }
}

const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
  isDragActive.value = true
}

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault()
  isDragActive.value = false
}

const removeFile = () => {
  documentStore.resetFile()
  if (fileInputUi.value) {
    fileInputUi.value.value = ''
  }
}

const formatFileSize = (bytes?: number): string => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Zoom and document control functions
const zoomIn = () => {
  if (zoomLevel.value < 3) {
    zoomLevel.value = Math.min(zoomLevel.value + 0.25, 3)
  }
}

const zoomOut = () => {
  if (zoomLevel.value > 0.5) {
    zoomLevel.value = Math.max(zoomLevel.value - 0.25, 0.5)
  }
}

const fitToScreen = () => {
  zoomLevel.value = 1
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
}

// const onDocumentLoad = () => {
//   console.log('Document loaded successfully')
// }

// Enhanced upload progress simulation
const simulateUploadProgress = () => {
  uploadProgress.value = 0
  uploadMessage.value = 'เริ่มต้นการอัปโหลด...'

  const interval = setInterval(() => {
    uploadProgress.value += Math.random() * 15

    if (uploadProgress.value >= 30 && uploadProgress.value < 60) {
      uploadMessage.value = 'กำลังประมวลผลไฟล์...'
    } else if (uploadProgress.value >= 60 && uploadProgress.value < 90) {
      uploadMessage.value = 'กำลังแยกข้อความ...'
    } else if (uploadProgress.value >= 90) {
      uploadMessage.value = 'เกือบเสร็จแล้ว...'
    }

    if (uploadProgress.value >= 100) {
      uploadProgress.value = 100
      uploadMessage.value = 'อัปโหลดเสร็จสิ้น!'
      clearInterval(interval)

      // Reset after a short delay
      setTimeout(() => {
        uploadProgress.value = 0
        uploadMessage.value = ''
      }, 1500)
    }
  }, 200)
}

const columns: {
  name: string
  label: string
  field: string
  align: 'left' | 'right' | 'center'
}[] = [
    { name: 'word', label: 'คำผิด', align: 'left', field: 'word' },
    { name: 'suggest', label: 'ควรแก้เป็น', align: 'left', field: 'suggest' },
  ]

const summaryText = ref<string>('')
const summarizeWithBot = async () => {
  if (!documentStore.ocrResults.length || !editorInstance || !currentPage.value) return

  const currentContent = editorInstance.getContents(false)
  currentPage.value.content = currentContent

  const payload: { page: number; content: string }[] = documentStore.ocrResults.map((page, idx) => ({
    page: page.page ?? idx + 1,
    content: convertHtmlToPlainText(page.content),
  }))

  try {
    const result = await documentStore.createdSummary(payload)
    summaryText.value = result.summary
  } catch (error) {
    console.error('Error creating summary:', error)
  }
}



const toggleClicked = async () => {
  console.log("documentStore.ocrResults in พิสูจน์: ", documentStore.ocrResults)
  currentPageIndex.value = 0
  refreshEditor()
  if (!documentStore.ocrResults.length || !editorInstance || !currentPage.value) return

  // บันทึกเนื้อหาหน้าปัจจุบันก่อน
  const currentContent = editorInstance.getContents(false)
  currentPage.value.content = currentContent

  const payload: { page: number; content: string }[] = documentStore.ocrResults.map((page, idx) => ({
    page: page.page ?? idx + 1,
    content: page.content,
  }))

  try {
    const results = await documentStore.spellCheck(payload)

    documentStore.ocrResults = results.map(r => ({
      page: r.page,
      content: highlightWrongWords(r.content)
    }))

    documentStore.wrongWords = results.flatMap(r =>
      r.wordlist.map(w => ({ page: r.page, word: w.word, suggest: w.suggest }))
    )

    documentStore.showTable = true
    activeButton.value = 'proofread'
  } catch (err) {
    console.error('Spellcheck batch failed:', err)
  }
}


const showMetadata = () => {
  if (!documentStore.ocrResults || documentStore.ocrResults.length === 0) return
  showMeta.value = true
  documentStore.showTable = false
  activeButton.value = 'edit'
}

const scanFile = async () => {
  if (!documentStore.file) return //
  activeButton.value = 'scan'
  isLoading.value = true
  try {
    if (!documentStore.file) return
    // await new Promise((resolve) => setTimeout(resolve, 2000))
    //ถ้า error แก้ไขเป็นใช้ documentService แต่จะไม่มี gif แสดง
    const response = await documentStore.saveFile(documentStore.pdfForm)

    isLoading.value = false


    if (response) {
      documentStore.ocrResults = response
      documentStore.pdfForm = new FormData()
    }
    console.log("response.ocr_result: ", response)
    console.log("documentStore.ocrResults: ", documentStore.ocrResults)
    //toggleClicked()
  } catch (error) {
    console.error('Failed to upload file:', error)
    //use for test
    // documentStore.wrongWords = [
    //   { word: 'มหาวิทยาลัยบูรพา', suggest: 'มหาวิทยาลัยบูรพา' },     // คำถูกแต่ใช้สำหรับเทส
    //   { word: 'คณะวิทยาการสารสนเทศ', suggest: 'คณะวิทยาศาสตร์' },
    //   { word: 'บริการวิชาการ', suggest: 'บริการเชิงวิชาการ' },
    //   { word: '๒๕๖๒', suggest: '2562' },
    //   { word: 'อธิการบดี', suggest: 'ผู้บริหารสูงสุด' },
    // ]
  }
}

//Sun editor
import SunEditor from 'suneditor'
import type Editor from 'suneditor/src/lib/core'
import {
  font,
  paragraphStyle,
  blockquote,
  fontColor,
  hiliteColor,
  textStyle,
  list,
  align,
  horizontalRule,
  link,
  lineHeight,
} from 'suneditor/src/plugins'
import fontSize from 'suneditor/src/plugins/submenu/fontSize'
import formatBlock from 'suneditor/src/plugins/submenu/formatBlock'
import math from 'suneditor/src/plugins/dialog/math'
import 'suneditor/dist/css/suneditor.min.css'
import katex from 'katex'
import 'katex/dist/katex.min.css'

const plugins = [
  font,
  fontSize,
  formatBlock,
  paragraphStyle,
  blockquote,
  fontColor,
  hiliteColor,
  textStyle,
  list,
  align,
  horizontalRule,
  lineHeight,
  link,
  math
]
const currentPageIndex = ref(0)
const currentPageDisplay = ref(1) // For the page input field
const editorRef = ref<HTMLElement | null>(null)
let editorInstance: Editor | null = null
const currentPage = computed(() => documentStore.ocrResults[currentPageIndex.value])

interface PageContent {
  page: number
  content: string
}


watch(
  () => documentStore.ocrResults,
  async (newVal) => {
    await nextTick()

    if (!editorRef.value) return

    // ถ้ามี editor เดิมให้ destroy ก่อน
    if (editorInstance) {
      editorInstance.destroy()
      editorInstance = null
    }



    const initialContent =
      newVal.length > 0
        ? convertPlainTextToHtml(newVal[0]?.content || '')
        : '<p>เพื่อความสะดวกของท่าน กรุณากดปุ่มสแกนไฟล์</p>'

    editorInstance = SunEditor.create(editorRef.value, {
      plugins: plugins,
      katex: katex,
      height: '65vh',
      width: '100%',
      buttonList: [
        ['undo', 'redo'],
        ['font', 'fontSize', 'formatBlock'],
        ['bold', 'underline', 'italic', 'strike', 'subscript', 'superscript'],
        ['paragraphStyle', 'blockquote'],
        ['fontColor', 'hiliteColor', 'textStyle'],
        ['removeFormat'],
        ['list', 'align', 'horizontalRule', 'lineHeight'],
        ['link', 'math']
      ],
      font: [
        'Cordia New', 'Sarabun', 'TH Sarabun New', 'Angsana New'
      ],
      defaultTag: 'div',
      value: initialContent,
    })

    editorInstance.onChange = (contents: string): void => {
      if (!currentPage.value || !editorInstance) return
      currentPage.value.content = contents

      const wysiwyg = editorInstance.core.context.element.wysiwyg
      if (wysiwyg.scrollHeight > 900) {
        void (async () => {
          await splitPage()
        })()
      }
    }

    editorInstance.core.context.element.wysiwyg.addEventListener(
      'keydown',
      handleKeyDown as EventListener,
    )
  },
  { immediate: true },
)

//const test = ref<{ page: number, content: string }[]>([])

function highlightWrongWords(text: string): string {
  // eslint-disable-next-line no-useless-escape
  const regex = /(\s*)\+\-(.+?)\-\+(\s*)/g
  return text.replace(regex, (_, leading, word, trailing) => {
    const safeLeading = leading !== ' ' ? '' : ' '
    const safeTrailing = trailing !== ' ' ? '' : ' '
    return `<span style="color: red; font-weight: bold; display: inline;">${safeLeading}${word}${safeTrailing}</span>`
  })
}


function convertHtmlToPlainText(html: string): string {
  let result = html.replace(/&nbsp;/g, ' ')
  result = result.replace(/<br\s*\/?>(\n)?/gi, '\n')
  result = result.replace(/<p[^>]*>(.*?)<\/p>/gi, '$1\n')
  result = result.replace(/<div[^>]*>|<\/div>/gi, '')
  result = result.replace(/<[^>]+>/g, '')
  return result.trim()
}

function convertPlainTextToHtml(plainText: string): string {
  if (plainText == '') return 'เพื่อความสะดวกของท่าน กรุณากดปุ่มสแกนไฟล์'
  return plainText
    .replace(/  +/g, (spaces) => '&nbsp;'.repeat(spaces.length))
    .split('\n')
    .map((line) => (line.trim() === '' ? '<br>' : `<p>${line}</p>`))
    .join('')
}

function handleKeyDown(e: KeyboardEvent) {
  if (!editorInstance) return

  // ✅ ตรวจว่ากด Backspace ที่ต้นหน้า
  if (e.key === 'Backspace' && isCursorNearTop() && currentPageIndex.value > 0) {
    e.preventDefault()

    const currentContent = editorInstance.getContents(false)
    const previousPage = documentStore.ocrResults[currentPageIndex.value - 1]

    // รวมข้อความเข้าหน้าก่อน
    if (!previousPage) return
    previousPage.content += currentContent

    // ลบหน้าปัจจุบัน
    documentStore.ocrResults.splice(currentPageIndex.value, 1)

    // ย้าย index กลับไปยังหน้าเดิม
    currentPageIndex.value--

    updatePageNumbers()
    refreshEditor()
  }

  // ✅ ตรวจว่าเกินขอบเขตความสูง → แยกหน้า
  if ((e.key === ' ' || e.key === 'Enter') && isEditorOverflowing()) {
    e.preventDefault()

    const fullHtml = editorInstance.getContents(false)
    const fullText = convertHtmlToPlainText(fullHtml)

    const mid = Math.floor(fullText.length * 0.6) // แบ่งคร่าว ๆ
    const left = fullText.slice(0, mid)
    const right = fullText.slice(mid)

    // อัปเดตหน้าเดิมด้วย left
    if (currentPage.value) {
      currentPage.value.content = convertPlainTextToHtml(left)
    }

    // เพิ่มหน้าใหม่ด้วย right
    documentStore.ocrResults.splice(currentPageIndex.value + 1, 0, {
      page: 0,
      content: convertPlainTextToHtml(right),
    })

    updatePageNumbers()
    currentPageIndex.value++
    refreshEditor()
  }
}

function isCursorNearTop(): boolean {
  const sel = window.getSelection()
  if (!sel || sel.rangeCount === 0) return false
  const range = sel.getRangeAt(0)
  const node = range.startContainer
  const el = node instanceof Text ? node.parentElement : node
  if (!(el instanceof HTMLElement)) return false
  return el.offsetTop < 20
}

function refreshEditor() {
  if (!editorInstance) return
  const page = documentStore.ocrResults[currentPageIndex.value]
  editorInstance.setContents(page?.content || '')
}

function updatePageNumbers() {
  documentStore.ocrResults.forEach((p, i) => (p.page = i + 1))
}

function isEditorOverflowing(): boolean {
  if (!editorInstance) return false
  const wysiwyg = editorInstance.core.context.element.wysiwyg as HTMLElement
  return wysiwyg.scrollHeight > 800
}

async function splitPage() {
  if (!editorInstance) return
  const html = editorInstance.getContents(false)
  const plainText = convertHtmlToPlainText(html)
  const splitIndex = Math.floor(plainText.length * 0.6) // split 60%
  const left = plainText.slice(0, splitIndex)
  const right = plainText.slice(splitIndex)

  // อัปเดตหน้าเดิมด้วยข้อความใหม่
  if (!currentPage.value) return
  currentPage.value.content = convertPlainTextToHtml(left)

  // แทรกหน้าใหม่พร้อมเนื้อหา
  const newPage: PageContent = {
    page: 0, // จะอัปเดตเลขทีหลัง
    content: convertPlainTextToHtml(right),
  }
  documentStore.ocrResults.splice(currentPageIndex.value + 1, 0, newPage)

  // เปลี่ยนไปหน้าใหม่และโหลดเนื้อหา
  currentPageIndex.value++
  updatePageNumbers()
  await nextTick(() => {
    editorInstance?.setContents(newPage.content)
  })
}

function goToPreviousPage() {
  if (currentPageIndex.value <= 0 || !editorInstance) return
  if (currentPage.value) {
    currentPage.value.content = editorInstance.getContents(false)
  }
  currentPageIndex.value--
  refreshEditor()
}

function goToNextPage() {
  if (currentPageIndex.value >= documentStore.ocrResults.length - 1 || !editorInstance) return
  if (currentPage.value) {
    currentPage.value.content = editorInstance.getContents(false)
  }
  currentPageIndex.value++
  refreshEditor()
}

// Additional navigation functions for enhanced UI
function goToFirstPage() {
  if (currentPageIndex.value <= 0 || !editorInstance) return
  if (currentPage.value) {
    currentPage.value.content = editorInstance.getContents(false)
  }
  currentPageIndex.value = 0
  refreshEditor()
}

function goToLastPage() {
  if (currentPageIndex.value >= documentStore.ocrResults.length - 1 || !editorInstance) return
  if (currentPage.value) {
    currentPage.value.content = editorInstance.getContents(false)
  }
  currentPageIndex.value = documentStore.ocrResults.length - 1
  refreshEditor()
}

function goToPage() {
  const targetPage = currentPageDisplay.value
  if (!targetPage || targetPage < 1 || targetPage > documentStore.ocrResults.length || !editorInstance) {
    // Reset to current page if invalid
    currentPageDisplay.value = (currentPageIndex.value + 1)
    return
  }

  if (currentPage.value) {
    currentPage.value.content = editorInstance.getContents(false)
  }
  currentPageIndex.value = targetPage - 1
  refreshEditor()
}

// Watch for page changes to sync display
watch(currentPageIndex, (newIndex) => {
  currentPageDisplay.value = newIndex + 1
})


</script>
<style scoped>
.no-scroll-page {
  height: 80vh;
  overflow: hidden;
}

.outer-wrapper {
  background: white;
  padding: 16px;
  height: 100%;
  box-sizing: border-box;
}

.full-page-row {
  display: flex;
  height: 100%;
  overflow: hidden;
  gap: 16px;
}

/* ===== FLOATING TOOLBAR ===== */
.floating-toolbar {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: slideDown 0.6s ease-out;
}

.floating-toolbar.top-toolbar {
  background: rgba(255, 255, 255, 0.9);
}

.floating-toolbar.document-toolbar {
  background: rgba(255, 255, 255, 0.98);
  min-width: 800px;
}

.toolbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  gap: 20px;
}

.title-section-compact {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon-wrapper-compact {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #032887 0%, #1976d2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(3, 40, 135, 0.3);
}

.title-text-compact {
  display: flex;
  flex-direction: column;
}

.main-title-compact {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  color: #1a1a1a;
  line-height: 1.2;
}

.document-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.doc-icon {
  font-size: 32px;
  color: #032887;
}

.doc-details {
  display: flex;
  flex-direction: column;
}

.doc-name {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1.2;
}

.doc-size {
  font-size: 14px;
  color: #666;
  font-weight: 400;
}

.document-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar-btn {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  color: #666;
  transition: all 0.3s ease;
}

.toolbar-btn:hover {
  background-color: rgba(3, 40, 135, 0.1);
  color: #032887;
  transform: scale(1.1);
}

.toolbar-btn.close-btn:hover {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.toolbar-action-btn {
  height: 36px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 13px;
  min-width: 100px;
  transition: all 0.3s ease;
}

.toolbar-action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.toolbar-divider {
  width: 1px;
  height: 24px;
  background: #e0e0e0;
  margin: 0 4px;
}

/* ===== FULLSCREEN UPLOAD AREA ===== */
.fullscreen-upload-area {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.upload-overlay {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.upload-overlay.drag-active {
  background: linear-gradient(135deg, rgba(3, 40, 135, 0.2) 0%, rgba(25, 118, 210, 0.2) 100%);
  transform: scale(1.02);
}

.upload-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
  pointer-events: none;
}

.fullscreen-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  position: relative;
  z-index: 1;
}

.upload-zone {
  max-width: 600px;
  padding: 60px 40px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: fadeInUp 0.8s ease-out;
}

.upload-icon-large {
  position: relative;
  margin-bottom: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.main-upload-icon {
  font-size: 96px;
  color: #032887;
  filter: drop-shadow(0 8px 16px rgba(3, 40, 135, 0.3));
  animation: float 4s ease-in-out infinite;
}

.upload-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120px;
  height: 120px;
  border: 3px solid rgba(3, 40, 135, 0.3);
  border-radius: 50%;
  animation: ripple 3s infinite;
}

.upload-instructions {
  margin-bottom: 40px;
}

.upload-main-title {
  margin: 0 0 16px 0;
  font-size: 36px;
  font-weight: 700;
  color: #1a1a1a;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  line-height: 1.2;
}

.upload-main-subtitle {
  margin: 0 0 32px 0;
  font-size: 18px;
  color: #666;
  font-weight: 400;
  line-height: 1.4;
}

.file-types-showcase {
  margin-top: 32px;
}

.supported-formats {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.format-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  border-radius: 16px;
  font-size: 16px;
  font-weight: 700;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: default;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.format-item:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.pdf-format {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
  color: white;
}

.docx-format {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
  color: white;
}

.doc-format {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
  color: white;
}

.size-info {
  margin: 0;
  font-size: 16px;
  color: #666;
  font-weight: 600;
}

.upload-cta {
  margin-top: 40px;
}

.select-file-main-btn {
  min-width: 200px;
  height: 56px;
  border-radius: 28px;
  font-weight: 700;
  font-size: 18px;
  box-shadow: 0 8px 24px rgba(3, 40, 135, 0.3);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.select-file-main-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 32px rgba(3, 40, 135, 0.4);
}

/* ===== UPLOAD PROGRESS INDICATOR ===== */
.upload-progress-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.progress-container {
  text-align: center;
  animation: fadeIn 0.5s ease-out;
}

.progress-circle {
  position: relative;
  margin-bottom: 24px;
}

.progress-ring {
  transform: rotate(-90deg);
}

.progress-ring-background {
  fill: none;
  stroke: #e0e0e0;
  stroke-width: 8;
}

.progress-ring-progress {
  fill: none;
  stroke: #032887;
  stroke-width: 8;
  stroke-linecap: round;
  stroke-dasharray: 339.292;
  /* 2 * PI * 54 */
  transition: stroke-dashoffset 0.3s ease;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.progress-percentage {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: #032887;
  line-height: 1;
}

.progress-label {
  display: block;
  font-size: 14px;
  color: #666;
  font-weight: 500;
  margin-top: 4px;
}

.progress-message {
  margin: 0;
  font-size: 16px;
  color: #666;
  font-weight: 500;
}

/* ===== IMMERSIVE DOCUMENT DISPLAY ===== */
.immersive-document-display {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
  background: #f5f5f5;
}

.document-viewer {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 100px 40px 40px 40px;
  box-sizing: border-box;
  transition: transform 0.3s ease;
  transform-origin: center center;
}

.document-frame {
  position: relative;
  max-width: 100%;
  max-height: 100%;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  animation: documentAppear 0.8s ease-out;
}

.document-shadow {
  position: absolute;
  top: 8px;
  left: 8px;
  right: -8px;
  bottom: -8px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  z-index: -1;
  filter: blur(8px);
}

.document-content {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 600px;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.document-iframe {
  width: 100%;
  height: 100%;
  min-height: 600px;
  border: none;
  display: block;
  background: white;
}

.docx-document {
  width: 100%;
  height: 100%;
  min-height: 600px;
  overflow: auto;
  padding: 40px;
  background: white;
  box-sizing: border-box;
}

.processing-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(245, 245, 245, 0.95);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.processing-content {
  text-align: center;
  animation: fadeIn 0.5s ease-out;
}

.processing-animation {
  margin-bottom: 24px;
}

.processing-title {
  margin: 0 0 12px 0;
  font-size: 24px;
  font-weight: 700;
  color: #1a1a1a;
}

.processing-message {
  margin: 0;
  font-size: 16px;
  color: #666;
  font-weight: 500;
}

.left-panel {
  background-image: url('/src/assets/img/Upload_bg.png');
  border-radius: 20px;
  background-size: cover;
  background-position: center;
  display: flex;
  justify-content: center;
  align-items: stretch;
  width: 40%;
  max-width: 640px;
  min-height: 0;
  overflow: hidden;
}

.left-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
  gap: 12px;
  min-height: 0;
  overflow: hidden;
  border-radius: 20px;
}

/* ===== HEADER SECTION ===== */
.top-section {
  padding: 0;
  margin-bottom: 8px;
  flex-shrink: 0;
}

.header-container {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.title-section {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.title-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #032887 0%, #1976d2 100%);
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(3, 40, 135, 0.3);
}

.title-icon {
  color: white;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  font-size: 32px;
}

.title-text {
  flex: 1;
}

.main-title {
  margin: 0;
  font-size: 22px;
  font-weight: 700;
  color: #1a1a1a;
  line-height: 1.2;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.subtitle {
  margin: 2px 0 0 0;
  font-size: 14px;
  color: #666;
  font-weight: 400;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: flex-end;
}

.modern-btn {
  min-width: 140px;
  height: 44px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
  text-transform: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.modern-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.modern-btn.active-btn {
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.3);
}

.modern-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.modern-btn:hover::before {
  left: 100%;
}


/* ===== UPLOAD SECTION ===== */
.center-section {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  padding: 0 8px;
  min-height: 0;
  overflow: hidden;
}

.upload-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 0;
}

.modern-upload-box {
  width: 95%;
  height: 100%;
  max-height: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modern-upload-box.has-file {
  width: 100%;
  height: 100%;
}

.upload-area {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.upload-area.drag-active {
  background: linear-gradient(135deg, rgba(3, 40, 135, 0.1) 0%, rgba(25, 118, 210, 0.1) 100%);
  border: 2px dashed #032887;
  transform: scale(1.02);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px;
  text-align: center;
}

.upload-icon-container {
  position: relative;
  margin-bottom: 24px;
}

.upload-main-icon {
  font-size: 72px;
  color: #032887;
  filter: drop-shadow(0 4px 8px rgba(3, 40, 135, 0.2));
  animation: float 3s ease-in-out infinite;
}

.upload-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100px;
  height: 100px;
  border: 2px solid rgba(3, 40, 135, 0.3);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 1;
  }

  100% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
  }
}

/* Upload Content Styles */
.upload-content {
  margin-bottom: 32px;
}

.upload-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.upload-subtitle {
  margin: 0 0 24px 0;
  font-size: 16px;
  color: #666;
  font-weight: 400;
}

.file-types-info {
  margin-top: 24px;
}

.supported-types {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.file-type-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  cursor: default;
}

.pdf-badge {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(244, 67, 54, 0.3);
}

.docx-badge {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
}

.doc-badge {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
}

.file-type-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.size-limit {
  margin: 0;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.upload-action {
  margin-top: 32px;
}

.select-file-btn {
  min-width: 160px;
  height: 48px;
  border-radius: 24px;
  font-weight: 600;
  font-size: 16px;
  box-shadow: 0 4px 16px rgba(3, 40, 135, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.select-file-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(3, 40, 135, 0.4);
}

.bottom-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60px;
  padding: 16px;
}

.upload-actions {
  text-align: center;
}

.process-btn {
  min-width: 180px;
  height: 48px;
  border-radius: 24px;
  font-weight: 600;
  font-size: 16px;
  box-shadow: 0 4px 16px rgba(3, 40, 135, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 12px;
}

.process-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(3, 40, 135, 0.4);
}

.file-support-info {
  margin: 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}


/* ===== FILE PREVIEW STYLES ===== */
.file-preview-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 16px;
  overflow: hidden;
}

.file-preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  flex-shrink: 0;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-icon {
  font-size: 32px;
  color: #032887;
}

.file-details {
  display: flex;
  flex-direction: column;
}

.file-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1.2;
}

.file-size {
  margin: 2px 0 0 0;
  font-size: 14px;
  color: #666;
  font-weight: 400;
}

/* File Controls */
.file-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-btn {
  color: #666;
  transition: all 0.3s ease;
}

.control-btn:hover {
  background-color: rgba(3, 40, 135, 0.1);
  color: #032887;
  transform: scale(1.1);
}

.remove-file-btn {
  color: #dc3545;
  transition: all 0.3s ease;
}

.remove-file-btn:hover {
  background-color: rgba(220, 53, 69, 0.1);
  transform: scale(1.1);
}

.file-preview-content {
  flex: 1;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
}

.file-preview-content.fullscreen-preview {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(10px);
}

.document-viewer-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
  transform-origin: center center;
}

.file-preview-iframe {
  width: 100%;
  height: 100%;
  border: none;
  display: block;
  border-radius: 8px;
}

.fullscreen-preview .file-preview-iframe {
  max-width: 90vw;
  max-height: 90vh;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.docx-preview {
  width: 100%;
  height: 100%;
  overflow: auto;
  padding: 16px;
  background: white;
  border-radius: 8px;
}

.fullscreen-preview .docx-preview {
  max-width: 90vw;
  max-height: 90vh;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

/* ===== RIGHT PANEL STYLES ===== */
.right-panel {
  flex: 1;
  /* ✅ ขยายเต็มที่เท่าที่เหลือ */
  display: flex;
  flex-direction: column;
  min-width: 0;
}

/* Enhanced Right Header Styles */
.right-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-bottom: 2px solid #e9ecef;
  flex-shrink: 0;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.header-tabs {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.tab-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.tab-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.tab-item.active {
  background: linear-gradient(135deg, #032887 0%, #1565c0 100%);
  color: white;
  box-shadow: 0 4px 20px rgba(3, 40, 135, 0.3);
}

.extract-tab.active {
  background: linear-gradient(135deg, #032887 0%, #1565c0 100%);
}

.words-tab.active {
  background: linear-gradient(135deg, #26a69a 0%, #4db6ac 100%);
}

.tab-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.tab-item.active .tab-icon-wrapper {
  background: rgba(255, 255, 255, 0.2);
}

.tab-icon {
  color: #032887;
  transition: all 0.3s ease;
}

.words-tab .tab-icon {
  color: #26a69a;
}

.tab-item.active .tab-icon {
  color: white;
}

.tab-content {
  flex: 1;
}

.tab-title {
  font-size: 16px;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 2px;
  transition: color 0.3s ease;
}

.tab-item.active .tab-title {
  color: white;
}

.tab-subtitle {
  font-size: 13px;
  color: #666;
  font-weight: 400;
  transition: color 0.3s ease;
}

.tab-item.active .tab-subtitle {
  color: rgba(255, 255, 255, 0.9);
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: transparent;
  transition: all 0.3s ease;
}

.tab-item.active .tab-indicator {
  background: rgba(255, 255, 255, 0.8);
}

.tab-divider {
  width: 1px;
  height: 40px;
  background: linear-gradient(to bottom, transparent, #e0e0e0, transparent);
  margin: 0 8px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-btn {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: white;
  color: #666;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-btn:hover {
  background: #f5f5f5;
  color: #032887;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Enhanced Right Content Styles */
.right-content {
  display: flex;
  flex: 1;
  gap: 15px;
  border: none;
  /* ✅ เอา border ทั่วไปออก */
  text-align: center;
  /* ✅ เพิ่มระยะห่างเท่า margin ด้านล่าง */
  padding: 5px;
  /* ✅ padding ให้ content ไม่ชน */
  margin-top: 5px;
}

.content-section {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 0;
  transition: all 0.3s ease;
}

.extract-section {
  flex: 2;
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
    linear-gradient(135deg, #032887, #1565c0) border-box;
}

.words-section {
  flex: 1;
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
    linear-gradient(135deg, #26a69a, #4db6ac) border-box;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  border-radius: 16px;
}

.extract-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Enhanced Page Info Bar */
.page-info-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 50%, #f1f3f4 100%);
  border-bottom: 2px solid #e9ecef;
  position: relative;
}

.page-info-left,
.page-info-center,
.page-info-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-info-center {
  flex: 1;
  justify-content: center;
}

.page-counter {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, #032887, #1565c0);
  border-radius: 10px;
  color: white;
}

.page-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.page-number {
  font-size: 16px;
  font-weight: 700;
  color: #1a1a1a;
  line-height: 1;
}

.page-total {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

/* Enhanced Page Navigation */
.page-navigation {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.nav-btn {
  width: 36px;
  height: 36px;
  border-radius: 10px;
  background: #f8f9fa;
  color: #666;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #e9ecef;
  position: relative;
  overflow: hidden;
}

.nav-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.nav-btn:hover:not(:disabled)::before {
  left: 100%;
}

.nav-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #032887, #1565c0);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(3, 40, 135, 0.3);
  border-color: #032887;
}

.nav-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
  background: #f1f3f4;
  color: #9e9e9e;
}

.page-input-wrapper {
  margin: 0 8px;
}

.page-input {
  width: 60px;
  text-align: center;
}

.page-input .q-field__control {
  border-radius: 8px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.page-input .q-field__control:hover {
  border-color: #032887;
  background: white;
}

.page-input .q-field__control:focus-within {
  border-color: #032887;
  background: white;
  box-shadow: 0 0 0 3px rgba(3, 40, 135, 0.1);
}

.page-input input {
  text-align: center;
  font-weight: 600;
  color: #1a1a1a;
}

/* Help and Warning Buttons */
.help-btn,
.warning-btn {
  width: 36px;
  height: 36px;
  border-radius: 10px;
  background: white;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.help-btn:hover {
  background: #e3f2fd;
  color: #1976d2;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.2);
}

.warning-btn:hover {
  background: #fff3e0;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 152, 0, 0.2);
}

/* Tooltip Enhancements */
.tooltip-content {
  padding: 8px 0;
}

.tooltip-title {
  font-weight: 700;
  margin-bottom: 8px;
  padding-bottom: 4px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.tooltip-item {
  margin: 4px 0;
  padding-left: 8px;
  line-height: 1.4;
}

/* Fullscreen Mode */
.right-panel.fullscreen-mode {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: white;
}

.right-panel.fullscreen-mode .right-content {
  padding: 24px;
}

.right-panel.fullscreen-mode .content-section {
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.15);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .right-content {
    flex-direction: column;
    gap: 16px;
  }

  .extract-section,
  .words-section {
    flex: none;
    min-height: 300px;
  }

  .header-tabs {
    flex-direction: column;
    gap: 12px;
  }

  .tab-item {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .right-header {
    padding: 16px;
    flex-direction: column;
    gap: 16px;
  }

  .page-info-bar {
    flex-direction: column;
    gap: 16px;
    padding: 16px;
  }

  .page-info-left,
  .page-info-center,
  .page-info-right {
    width: 100%;
    justify-content: center;
  }

  .page-navigation {
    flex-wrap: wrap;
    justify-content: center;
  }

  .tab-content {
    text-align: center;
  }

  .tab-title {
    font-size: 14px;
  }

  .tab-subtitle {
    font-size: 12px;
  }
}

/* Animation Enhancements */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.content-section {
  animation: slideInUp 0.6s ease-out;
}

.tab-item {
  animation: fadeIn 0.4s ease-out;
}

.page-counter,
.page-navigation {
  animation: slideInUp 0.5s ease-out;
}



/* Focus States */
.nav-btn:focus-visible {
  outline: 2px solid #032887;
  outline-offset: 2px;
}

.action-btn:focus-visible {
  outline: 2px solid #032887;
  outline-offset: 2px;
}

.help-btn:focus-visible,
.warning-btn:focus-visible {
  outline: 2px solid currentColor;
  outline-offset: 2px;
}

.editor-container {
  flex: 1;
  padding: 16px;
  overflow: hidden;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.text-editor {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
}

/* ===== WORDS SECTION STYLES ===== */
.empty-state-words {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.empty-content {
  text-align: center;
}

.empty-icon {
  color: #26a69a;
  margin-bottom: 16px;
  filter: drop-shadow(0 2px 4px rgba(38, 166, 154, 0.2));
}

.empty-title {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
}

.empty-subtitle {
  margin: 0;
  font-size: 14px;
  color: #666;
  font-weight: 400;
}

.words-table-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-header {
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
}

.legend {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  font-weight: 600;
  color: #1a1a1a;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.error-color {
  background-color: #e91e63;
}

.table-wrapper {
  flex: 1;
  overflow: hidden;
}

.modern-table {
  height: 100%;
}

.word-cell,
.suggest-cell {
  padding: 12px 16px !important;
}

.word-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  flex-shrink: 0;
}

.status-indicator.has-suggestion {
  background-color: #2196f3;
}

.status-indicator.no-suggestion {
  background-color: #e91e63;
}

.word-text {
  font-weight: 600;
  color: #1a1a1a;
}

.suggest-text {
  color: #666;
  font-weight: 500;
}



/* ===== UTILITY STYLES ===== */
.scroll-hidden {
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.scroll-hidden::-webkit-scrollbar {
  display: none;
}

/* ===== SUN EDITOR OVERRIDES ===== */
::v-deep(.sun-editor) {
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
  border-radius: 8px !important;
  overflow: hidden !important;
}

::v-deep(.sun-editor .se-wrapper) {
  white-space: pre-wrap !important;
  word-break: break-word !important;
  border-radius: 8px !important;
}

::v-deep(.sun-editor .se-wrapper-wysiwyg) {
  white-space: pre-wrap !important;
  word-break: break-word;
  max-width: 100%;
  width: 100%;
  box-sizing: border-box;
  background: #fafafa !important;
  border-radius: 8px !important;
  padding: 16px !important;
}

::v-deep(.sun-editor .se-wrapper-wysiwyg p) {
  margin: 0 !important;
  padding: 2px 0 !important;
  line-height: 1.6 !important;
}

::v-deep(.sun-editor .se-toolbar) {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
  border-bottom: 1px solid #dee2e6 !important;
  border-radius: 8px 8px 0 0 !important;
}

::v-deep(.sun-editor .se-btn) {
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
}

::v-deep(.sun-editor .se-btn:hover) {
  background-color: rgba(3, 40, 135, 0.1) !important;
  transform: scale(1.05) !important;
}

/* ===== ENHANCED RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .floating-toolbar.document-toolbar {
    min-width: 700px;
  }

  .toolbar-action-btn {
    min-width: 80px;
    font-size: 12px;
  }

  .upload-main-title {
    font-size: 32px;
  }

  .document-viewer {
    padding: 80px 20px 20px 20px;
  }
}

@media (max-width: 768px) {
  .floating-toolbar.document-toolbar {
    min-width: 90%;
    left: 5%;
    transform: none;
  }

  .toolbar-content {
    flex-direction: column;
    gap: 12px;
    padding: 16px;
  }

  .document-actions {
    flex-wrap: wrap;
    justify-content: center;
  }

  .upload-zone {
    padding: 40px 20px;
    margin: 20px;
  }

  .upload-main-title {
    font-size: 28px;
  }

  .upload-main-subtitle {
    font-size: 16px;
  }

  .main-upload-icon {
    font-size: 72px;
  }

  .supported-formats {
    flex-direction: column;
    gap: 12px;
  }

  .format-item {
    justify-content: center;
  }

  .document-viewer {
    padding: 120px 10px 10px 10px;
  }

  .document-frame {
    max-width: 100%;
  }

  .floating-toolbar {
    position: fixed;
    top: 10px;
    left: 10px;
    right: 10px;
    transform: none;
  }
}

@media (max-width: 480px) {
  .upload-zone {
    padding: 30px 15px;
    margin: 10px;
  }

  .upload-main-title {
    font-size: 24px;
  }

  .main-upload-icon {
    font-size: 64px;
  }

  .select-file-main-btn {
    min-width: 160px;
    height: 48px;
    font-size: 16px;
  }

  .toolbar-action-btn {
    min-width: 70px;
    font-size: 11px;
    padding: 8px 12px;
  }

  .document-viewer {
    padding: 100px 5px 5px 5px;
  }
}

/* ===== KEYBOARD NAVIGATION ===== */
.upload-overlay:focus {
  outline: 3px solid #032887;
  outline-offset: 4px;
}

.toolbar-btn:focus,
.toolbar-action-btn:focus {
  outline: 2px solid #032887;
  outline-offset: 2px;
}

/* ===== HIGH CONTRAST MODE ===== */
@media (prefers-contrast: high) {
  .floating-toolbar {
    background: white;
    border: 2px solid #000;
  }

  .upload-zone {
    background: white;
    border: 2px solid #000;
  }

  .document-frame {
    border: 2px solid #000;
  }
}

/* ===== REDUCED MOTION ===== */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .main-upload-icon,
  .upload-ripple,
  .upload-pulse {
    animation: none !important;
  }
}

/* ===== METADATA SECTION ===== */
.metadata-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: white;
  border-radius: 12px;
  min-height: 0;
}

/* ===== ENHANCED ANIMATIONS ===== */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }

  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-15px);
  }
}

@keyframes ripple {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 1;
  }

  100% {
    transform: translate(-50%, -50%) scale(1.8);
    opacity: 0;
  }
}

@keyframes documentAppear {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }

  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 1;
  }

  100% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
  }
}

.modern-upload-box {
  animation: slideInUp 0.6s ease-out;
}

.header-container {
  animation: slideInUp 0.4s ease-out;
}

.content-section {
  animation: fadeIn 0.8s ease-out;
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
.modern-btn:focus,
.nav-btn:focus,
.select-file-btn:focus,
.process-btn:focus {
  outline: 2px solid #032887;
  outline-offset: 2px;
}

.upload-area:focus {
  outline: 2px dashed #032887;
  outline-offset: 4px;
}

/* ===== PRINT STYLES ===== */
@media print {

  .left-panel,
  .action-buttons,
  .page-navigation {
    display: none !important;
  }

  .right-panel {
    width: 100% !important;
    box-shadow: none !important;
  }

  .extract-section {
    flex: 1 !important;
  }
}
</style>
