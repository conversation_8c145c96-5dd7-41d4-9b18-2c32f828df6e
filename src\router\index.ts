import { defineRouter } from '#q-app/wrappers'
import {
  createM<PERSON>oryHistory,
  createRouter,
  createWebHashHistory,
  createWebHistory,
} from 'vue-router'
import routes from './routes'

export default defineRouter(function () {
  const createHistory = process.env.SERVER
    ? createMemoryHistory()
    : process.env.VUE_ROUTER_MODE === 'history'
      ? createWebHistory()
      : createWebHashHistory()

  const Router = createRouter({
    scrollBehavior: () => ({ left: 0, top: 0 }),
    routes,
    history: createHistory,
  })

  return Router
})
