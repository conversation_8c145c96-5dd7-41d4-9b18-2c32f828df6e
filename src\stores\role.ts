import { defineStore } from 'pinia'
import type { Role } from 'src/types/Role'
import roleService from 'src/services/role'
import { ref } from 'vue'

export const useRolestore = defineStore('role', () => {
  const roles = ref<Role[]>([])
  const initialrole: Role = {
    role_id: 0,
    role_name: '',
  }
  const editedrole = ref<Role>(JSON.parse(JSON.stringify(initialrole)))
  async function getRole(id: number) {
    //oadingStore.doLoad()
    const res = await roleService.getRole(id)
    editedrole.value = res.data
    //loadingStore.finish()
  }

  async function getRoles() {
    try {
      //loadingStore.doLoad()
      const res = await roleService.getRoles()
      roles.value = res.data
      //loadingStore.finish()
    } catch (e) {
      console.error('Error fetching user:', e)
      //loadingStore.finish()
    }
  }
  return {
    roles,
    initialrole,
    editedrole,
    getRole,
    getRoles,
  }
})
