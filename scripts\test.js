#!/usr/bin/env node

/**
 * Test runner script for the project
 * Provides different test running modes and utilities
 */

const { spawn } = require('child_process')
const path = require('path')

const args = process.argv.slice(2)
const command = args[0] || 'run'

const testCommands = {
  // Run all tests
  run: ['npx', 'vitest', 'run'],
  
  // Run tests in watch mode
  watch: ['npx', 'vitest'],
  
  // Run tests with coverage
  coverage: ['npx', 'vitest', 'run', '--coverage'],
  
  // Run tests with UI
  ui: ['npx', 'vitest', '--ui'],
  
  // Run specific test file
  file: ['npx', 'vitest', 'run', args[1] || ''],
  
  // Run tests for specific type
  types: ['npx', 'vitest', 'run', 'src/test/unit/types/'],
  services: ['npx', 'vitest', 'run', 'src/test/unit/services/'],
  stores: ['npx', 'vitest', 'run', 'src/test/unit/stores/'],
  components: ['npx', 'vitest', 'run', 'src/test/unit/components/'],
}

function runCommand(cmd, args) {
  const child = spawn(cmd, args, {
    stdio: 'inherit',
    shell: true,
    cwd: process.cwd()
  })

  child.on('error', (error) => {
    console.error('Error running command:', error)
    process.exit(1)
  })

  child.on('close', (code) => {
    process.exit(code)
  })
}

function showHelp() {
  console.log(`
Test Runner Script

Usage: node scripts/test.js [command] [options]

Commands:
  run         Run all tests once (default)
  watch       Run tests in watch mode
  coverage    Run tests with coverage report
  ui          Run tests with UI interface
  file <path> Run specific test file
  types       Run only type tests
  services    Run only service tests
  stores      Run only store tests
  components  Run only component tests

Examples:
  node scripts/test.js run
  node scripts/test.js watch
  node scripts/test.js coverage
  node scripts/test.js file src/test/unit/types/User.test.ts
  node scripts/test.js types
`)
}

if (command === 'help' || command === '--help' || command === '-h') {
  showHelp()
  process.exit(0)
}

if (!testCommands[command]) {
  console.error(`Unknown command: ${command}`)
  showHelp()
  process.exit(1)
}

const [cmd, ...cmdArgs] = testCommands[command]
runCommand(cmd, cmdArgs.filter(arg => arg !== ''))
