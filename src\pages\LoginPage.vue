<template>
  <q-layout view="lHh Lpr lFf" class="q-pa-none">
    <!-- Login Page Area -->
     <q-page-container>
      <q-page class="q-pa-none bg-img">
        <div class="fullscreen flex flex-center">
          <q-card
            tag="button"
            class="fixed-top-left q-ma-md q-pa-sm row items-center bg-white guest-btn"
            style="border-radius: 12px; cursor: pointer; border: none;"
            @click="guestLogin"
          >
            <q-icon name="arrow_back" color="primary" class="q-mr-sm" />
            <div class="text-bold q-mr-sm" style="color: #032887;">Back</div>
          </q-card>

          <q-card
              class="q-pa-md login-card bg-white column justify-evenly"
              style="width: 23vw; max-width: 65%; height: 65vh; border-radius: 20px"
            >
              <!-- ส่วนที่ 1: โลโก้ + LOGIN -->
              <div class="column items-center q-mt-sm q-gutter-y-xs">
                <q-img
                  src="@/assets/img/buu_login_logo.png"
                  style="width: 150px"
                />
                <div class="text-h6 text-weight-bold font q-pb-md q-px-md center" style="color: #032887;">Document Retrieval System</div>
              </div>

              <!-- ส่วนที่ 2: ฟอร์ม -->
              <q-form @submit.prevent="handleLogin" class="column q-gutter-y-md q-mx-sm">
                <q-input
                  v-model="email"
                  type="email"
                  label="Email"
                  dense
                  outlined
                  required
                />

                <q-input
                  v-model="password"
                  :type="showPassword ? 'text' : 'password'"
                  label="Password"
                  dense
                  outlined
                  :lazy-rules="!passwordTouched"
                  :rules="[
                    val => {
                      if (!passwordTouched || val === '') return true
                      return val.length >= 4 || 'Password must be at least 4 characters'
                    },
                    val => {
                      if (!passwordTouched || val === '') return true
                      return val.length <= 8 || 'Password must be at most 8 characters'
                    }
                  ]"
                  required
                  @update:model-value="() => passwordTouched = true"
                >
                  <template v-slot:append>
                    <q-icon
                      :name="showPassword ? 'visibility_off' : 'visibility'"
                      class="cursor-pointer"
                      @click="showPassword = !showPassword"
                    />
                  </template>
                </q-input>

                <div class="text-right">
                  <q-btn
                    flat
                    label="Forgot your password?"
                    color="primary"
                    size="sm"
                  />
                </div>
              </q-form>

              <!-- ส่วนที่ 3: ปุ่ม login ล่างสุด (มี margin บน ล่าง) -->
              <div class="q-mx-sm q-mt-md q-mb-sm">
                <div class="row justify-center">
                  <q-btn
                    label="Login"
                    type="submit"
                    class="login-btn"
                    @click="accessLogin"
                  />
                </div>
              </div>
            </q-card>
        </div>
        <LoadingDialog/>
      </q-page>
    </q-page-container>
  </q-layout>
</template>


<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from 'src/stores/user'
import { useDialogOrPopupstore } from 'src/stores/dialogOrPopup'
import LoadingDialog from 'src/components/LoadingDialog.vue'

const userStore = useUserStore()
const dialogOrPopupstore = useDialogOrPopupstore()
const router = useRouter()
const email = ref('<EMAIL>')
const password = ref('@1234')
const passwordTouched = ref(false)
const showPassword = ref(false)

onMounted(async ()=>{
  await userStore.getUsers()
  console.log(userStore.users)
})

function handleLogin() {
  if (!email.value || !password.value) {
    return { success: false, message: 'กรุณากรอก Email และ Password' }
  }

  if (password.value.length <= 4 || password.value.length >= 8) {
    return { success: false, message: 'รหัสผ่านต้องมีความยาวระหว่าง 4 ถึง 8 ตัวอักษร' }
  }

  return { success: true }
}

async function accessLogin() {
  const result = handleLogin()

  if (result.success==false) {
    alert(result.message)
    return
  }

  const user = userStore.users.find(u => u.email === email.value)
  if (!user) {
    alert('ไม่พบอีเมลนี้ในระบบ')
    return
  }

  if (user.password !== password.value) {
    alert('รหัสผ่านไม่ถูกต้อง')
    return
  }
  dialogOrPopupstore.setLoadingShowAndRename(true,'กำลังเข้าสู่ระบบ')
  userStore.setSession(user)
  await new Promise(resolve => setTimeout(resolve, 1000))
  await router.push('/')
  dialogOrPopupstore.setLoadingShowAndRename(false,'')
}

async function guestLogin() {
  await router.push('/')
}



</script>

<style scoped>
.bg-img {
  background-image: url('@/assets/img/chill_wallpaper.png');
  background-size: cover;
  background-position: center;
}

.font{
  font-size: 24px;
}

.login-btn {
  background-color: #2742a3;
  color: white;
  border-radius: 12px;
  font-weight: bold;
  letter-spacing: 2px;
  padding: 12px 20px;
  font-size: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transition: transform 0.5s ease, box-shadow 0.2s ease, filter 0.2s ease, opacity 0.2s ease;
  opacity: 0.75;
}

.login-btn:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.35);
  opacity: 1;
}


.guest-btn {
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease, box-shadow 0.3s ease;
  border: none;
  outline: none;
  padding: 12px;
  font-size: 16px;
  font-weight: bold;
  opacity: 0.6;
  transition: transform 0.5s ease, box-shadow 0.3s ease, opacity 0.3s ease;
}
.guest-btn:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 28px rgba(0, 0, 0, 0.15);
  opacity: 0.95;
}

.login-card {
  border-radius: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: transform 0.5s ease, box-shadow 0.3s ease;
  opacity: 0.75;
}

.login-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 28px rgba(0, 0, 0, 0.25);
  opacity: 1;
}

.glass-card {
  background: rgba(255, 255, 255, 0.15); /* เทาใส */
  backdrop-filter: blur(20px);           /* เบลอพื้นหลัง */
  -webkit-backdrop-filter: blur(20px);   /* รองรับ Safari */
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.25); /* ขอบบาง */
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);   /* เงาอ่อน */
  transition: all 0.3s ease;
  color: #fff; /* หรือเปลี่ยน text ให้ contrast กับพื้นหลัง */
}

@media (max-height: 500px) {
  .login-card {
    height: auto;
    max-height: 90vh;
  }
}

</style>
