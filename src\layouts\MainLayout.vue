<template>
  <q-layout view="lHh Lpr lFf" class="q-pa-none">
    <q-header elevated>
      <q-toolbar>
        <!-- โลโก้ + ชื่อระบบ -->
        <q-img src="@/assets/img/buu_doc_logo_white_text.png" alt="BUU Logo" fit="contain"
          style="height: 40px; max-width: 150px; margin: 2px 2px 2px 1px; cursor: pointer;" @click="gotomain()" />
        <div class="font" v-if="!isCompact">
          ระบบค้นคืนเอกสาร <br />
          มหาวิทยาลัยบูรพา
        </div>

        <q-space />

        <!-- ====== เมนูแบบแท็บ (เดสก์ท็อปเท่านั้น) ====== -->
        <div v-if="!isCompact" class="menu-toolbar-right menu">
          <q-tabs class="modern-tabs" indicator-color="transparent" active-color="white">
            <q-route-tab to="/" exact label="หน้าหลัก" class="modern-tab" />
            <q-route-tab to="/document" label="เอกสาร" class="modern-tab" />
            <q-route-tab to="/report" label="รายงาน" class="modern-tab" v-if="canManageDocument" />
            <q-route-tab to="/DocManage" label="การจัดการเอกสาร" class="modern-tab" v-if="canManageDocument" />
            <q-route-tab to="/user" label="การจัดการผู้ใช้งาน" class="modern-tab"
              v-if="userStore.currentUser && userStore.currentUser.role.role_name == 'Super Admin'" />
          </q-tabs>

          <q-btn round icon="notifications" class="notif-btn" size="11px">
            <q-badge v-if="notifications.length" floating color="red" text-color="white" rounded
              :label="String(notifications.length)" />
          </q-btn>

          <!-- Avatar -->
          <q-avatar size="40px" style="margin-left: 32px; cursor: pointer;">
            <q-menu anchor="bottom right" self="top right" class="q-menu-style" :offset="offset" :style="{
              borderRadius: '16px',
              backgroundColor: 'white',
              boxShadow: '0 6px 20px rgba(0, 0, 0, 0.08)',
              backdropFilter: 'blur(12px)'
            }">
              <q-list style="min-width: 160px; background-color: transparent; box-shadow: none;" class="q-gutter-y-sm">
                <q-item clickable v-close-popup @click="goToProfile" class="q-item-style"
                  style="animation-delay: 0.05s;" v-if="userStore.currentUser">
                  <q-icon name="person" color="primary" class="q-mr-sm" />
                  <q-item-section>User Profile</q-item-section>
                </q-item>
                <q-item clickable v-close-popup @click="userStore.currentUser ? logOut() : logIn()" class="q-item-style"
                  style="animation-delay: 0.15s;">
                  <q-icon :name="userStore.currentUser ? 'logout' : 'login'" color="primary" class="q-mr-sm" />
                  <q-item-section>{{ userStore.currentUser ? 'Logout' : 'Login' }}</q-item-section>
                </q-item>
              </q-list>
            </q-menu>
            <img :src="profileImage" alt="Profile Picture" />
          </q-avatar>
        </div>

        <!-- ====== เมนูแบบไอคอน + ดรอปดาวน์ (มือถือ/แท็บเล็ต) ====== -->
        <div v-else class="menu-toolbar-right menu">
          <q-btn round icon="notifications" class="notif-btn" size="11px">
            <q-badge v-if="notifications.length" floating color="red" text-color="white" rounded
              :label="String(notifications.length)" />
          </q-btn>

          <q-btn round dense flat icon="menu" aria-label="Main menu">
            <q-menu anchor="bottom right" self="top right" :offset="[0, 8]" class="mobile-nav-menu">
              <q-list bordered separator>
                <q-item v-for="item in visibleItems" :key="item.to" clickable v-ripple v-close-popup
                  @click="go(item.to)">
                  <q-item-section>{{ item.label }}</q-item-section>
                </q-item>
              </q-list>
            </q-menu>
          </q-btn>

          <!-- Avatar เหมือนด้านบน -->
          <q-avatar size="36px" style="margin-left: 8px; cursor: pointer;">
            <q-menu anchor="bottom right" self="top right" class="q-menu-style" :offset="offset">
              <q-list style="min-width: 160px" class="q-gutter-y-sm">
                <q-item clickable v-close-popup @click="goToProfile" class="q-item-style" v-if="userStore.currentUser">
                  <q-icon name="person" color="primary" class="q-mr-sm" />
                  <q-item-section>User Profile</q-item-section>
                </q-item>
                <q-item clickable v-close-popup @click="userStore.currentUser ? logOut() : logIn()"
                  class="q-item-style">
                  <q-icon :name="userStore.currentUser ? 'logout' : 'login'" color="primary" class="q-mr-sm" />
                  <q-item-section>{{ userStore.currentUser ? 'Logout' : 'Login' }}</q-item-section>
                </q-item>
              </q-list>
            </q-menu>
            <img :src="profileImage" alt="Profile Picture" />
          </q-avatar>
        </div>
      </q-toolbar>
    </q-header>

    <router-view />

    <q-dialog v-model="dialogOrPopupStore.showUserProfiledialog">
      <UserProfileDialog />
    </q-dialog>
    <LoadingDialog />
  </q-layout>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useDialogOrPopupstore } from 'src/stores/dialogOrPopup'
import { useUserStore } from 'src/stores/user'
import UserProfileDialog from 'src/components/UserProfileDialog.vue'
import LoadingDialog from 'src/components/LoadingDialog.vue'
import { useQuasar } from 'quasar'

const dialogOrPopupStore = useDialogOrPopupstore()

const userStore = useUserStore()
const router = useRouter()


const $q = useQuasar()
const isCompact = computed(() => $q.screen.lt.lg)
const menuItems = computed(() => ([
  { to: '/', label: 'หน้าหลัก', show: true },
  { to: '/document', label: 'เอกสาร', show: true },
  { to: '/report', label: 'รายงาน', show: canManageDocument.value },
  { to: '/DocManage', label: 'การจัดการเอกสาร', show: canManageDocument.value },
  { to: '/user', label: 'การจัดการผู้ใช้งาน', show: !!(userStore.currentUser && userStore.currentUser.role?.role_name === 'Super Admin') }
]))
const visibleItems = computed(() => menuItems.value.filter(i => i.show))
function go(path: string) {
  void router.push(path)
}


const profileImage = computed(() => {
  return userStore.currentUser?.profile || 'https://cdn.quasar.dev/img/avatar.png'
})
const notifications = ref([
  { id: 1, title: 'แจ้งเตือน 1' },
  { id: 2, title: 'แจ้งเตือน 2' },
  { id: 3, title: 'แจ้งเตือน 3' }
])

const offset = computed(() => {
  const halfVh = window.innerHeight * 0.005
  return [0, halfVh]
})


async function gotomain() {
  await router.push('/')
}


async function logIn() {
  if (!userStore.currentUser) {
    await router.push('/Login')
  }
}
async function logOut() {
  if (userStore.currentUser) {
    userStore.clearSession()
  }
  dialogOrPopupStore.setLoadingShowAndRename(true, 'กำลังออกจากระบบ...')
  await new Promise(resolve => setTimeout(resolve, 500))
  dialogOrPopupStore.setLoadingShowAndRename(false, '')
  await router.push('/')
}
function goToProfile() {
  if (userStore.currentUser) {
    dialogOrPopupStore.showUserProfiledialog = true
  } else {
    alert('ไม่สามารถเข้าได้ในขณะที่เป็น Guest')
  }

}

const canManageDocument = computed(() => {
  const user = userStore.currentUser
  return user && (user.role?.role_name === 'Super Admin' || user.role?.role_name === 'Staff')
})
</script>

<style scoped>
@import url('https://fonts.googleapis.com/css?family=Open+Sans+Condensed:700');

.font {
  font-family: 'Kanit', sans-serif;
  font-size: 22px;
  font-weight: 700;
  line-height: 20px;
  color: white;
}

.menu {
  font-family: 'Open Sans Condensed', sans-serif;
}

.menu-toolbar-right {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-right: 12px;
}

.notif-btn {
  background-color: white;
  color: black;
  margin-left: 24px;
}

/* Modern Tab Design */
.modern-tabs {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 0 8px;
  font-family: 'Kanit', sans-serif;
}

.modern-tab {
  position: relative;
  font-size: 20px;
  color: rgba(255, 255, 255, 0.85) !important;
  font-weight: 500;
  letter-spacing: 0.5px;
  padding: 6px 12px;
  transition: color 0.25s ease;
}

.modern-tab::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0%;
  height: 3px;
  background: linear-gradient(to right, #ffffff, #3f8efc, #5e60dd);
  border-radius: 2px;
  transition: width 0.4s ease, left 0.4s ease;
  transform: translateX(-50%);
}

.modern-tab:hover {
  color: white !important;
}

.modern-tab:hover::before,
.q-tab--active.modern-tab::before {
  width: 100%;
  left: 50%;
}

.q-tab--active.modern-tab {
  color: white !important;
}

.fade-down-item {
  animation: fadeInDown 0.4s ease both;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.q-menu-style {
  overflow: hidden;
  background: rgba(240, 243, 250, 0.9);
  backdrop-filter: blur(12px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
  padding: 0;
}

/* รายการแต่ละปุ่ม */
.q-item-style {
  background-color: transparent;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  transition: background-color 0.2s;
  font-weight: bold;
  color: #032887;
  font-size: 16px;
  border-radius: 16px;
  position: relative;
  /* เพื่อให้ q-focus-helper อยู่ใน context */
  overflow: hidden;
}

.q-item-style:hover {
  background-color: rgba(240, 240, 240, 0.8);
}

@media (max-width: 1023px) {
  .font {
    font-size: 18px;
    line-height: 18px;
  }

  .modern-tab {
    font-size: 18px;
  }

  /* เผื่อยังเห็นแท็บบนจอใหญ่กว่า md */
}

@media (max-width: 1023px) {
  .q-img[alt="BUU Logo"] {
    height: 32px !important;
    max-width: 120px !important;
    margin: 2px !important;
  }
}

/* ====== Mobile dropdown polish ไม่ติดไม่รู้ทำไม ====== */
:deep(.mobile-nav-menu) {
  /* กล่องเมนู */
  min-width: 240px;
  border-radius: 16px;
  background: #ffffff;
  box-shadow:
    0 10px 24px rgba(0, 0, 0, 0.12),
    0 3px 8px rgba(0, 0, 0, 0.06);
  padding: 6px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  z-index: 3000;
  /* ให้อยู่เหนือคอนเทนต์อื่น */
  font-family: 'Kanit', 'Sarabun', 'TH Sarabun New', system-ui, -apple-system, sans-serif;
}

/* รายการในเมนู */
:deep(.mobile-nav-menu .q-list) {
  background: transparent;
  /* ให้เห็นมุมโค้งของคอนเทนเนอร์ */
}

:deep(.mobile-nav-menu .q-item) {
  border-radius: 12px;
  padding: 14px 16px;
  min-height: 44px;
  /* ขนาดแตะง่าย ตาม guideline มือถือ */
  transition: background-color .15s ease, transform .04s ease;
}

/* เส้นคั่นบางและเนียน */
:deep(.mobile-nav-menu .q-list--separator .q-item) {
  border-color: rgba(0, 0, 0, 0.06) !important;
}

/* ข้อความเมนู */
:deep(.mobile-nav-menu .q-item__section) {
  font-size: 16px;
  line-height: 1.25;
  color: #032887;
  font-weight: 500;
  letter-spacing: .2px;
}

/* Hover/Focus */
:deep(.mobile-nav-menu .q-item:hover),
:deep(.mobile-nav-menu .q-item:focus-visible) {
  background-color: rgba(3, 40, 135, 0.06);
  /* โทนฟ้าเข้มของหัวเว็บแบบบาง */
}

/* Active press feedback */
:deep(.mobile-nav-menu .q-item:active) {
  transform: translateY(1px);
  background-color: rgba(3, 40, 135, 0.12);
}

/* ระยะห่างรอบเมนูให้ไม่ชิดขอบจอ (เผื่อ notch) */
@supports (padding: max(0px)) {
  :deep(.mobile-nav-menu) {
    margin: 4px;
    padding-top: max(6px, env(safe-area-inset-top));
    padding-bottom: 6px;
  }
}

/* ขนาดพิเศษบนจอเล็กมาก */
@media (max-width: 380px) {
  :deep(.mobile-nav-menu) {
    min-width: 210px;
  }

  :deep(.mobile-nav-menu .q-item__section) {
    font-size: 15px;
  }
}

/* ปรับ ripple ให้ดูนุ่ม */
:deep(.mobile-nav-menu .q-ripple__inner) {
  opacity: 0.12 !important;
}

.brand-logo {
  height: 40px;
  max-width: 150px;
  margin: 2px;
  cursor: pointer;
}

@media (max-width: 1023px) {
  .brand-logo {
    height: 32px !important;
    max-width: 120px !important;
  }
}
</style>
