import { describe, it, expect } from 'vitest'
import type { Department } from 'src/types/Department'

describe('Department Type', () => {
  const mockDepartment: Department = {
    department_id: 1,
    department_name: 'คณะวิศวกรรมศาสตร์'
  }

  it('should create a valid Department object', () => {
    expect(mockDepartment).toBeDefined()
    expect(mockDepartment.department_id).toBe(1)
    expect(mockDepartment.department_name).toBe('คณะวิศวกรรมศาสตร์')
  })

  it('should validate required properties', () => {
    const requiredProperties = ['department_id', 'department_name']
    
    requiredProperties.forEach(prop => {
      expect(mockDepartment).toHaveProperty(prop)
    })
  })

  it('should validate property types', () => {
    expect(typeof mockDepartment.department_id).toBe('number')
    expect(typeof mockDepartment.department_name).toBe('string')
  })

  it('should handle empty department', () => {
    const emptyDepartment: Department = {
      department_id: 0,
      department_name: ''
    }

    expect(emptyDepartment).toBeDefined()
    expect(emptyDepartment.department_id).toBe(0)
    expect(emptyDepartment.department_name).toBe('')
  })

  it('should handle different department names', () => {
    const departments = [
      { department_id: 1, department_name: 'คณะวิศวกรรมศาสตร์' },
      { department_id: 2, department_name: 'คณะวิทยาการสารสนเทศ' },
      { department_id: 3, department_name: 'คณะบริหารธุรกิจ' }
    ]

    departments.forEach(department => {
      expect(department).toHaveProperty('department_id')
      expect(department).toHaveProperty('department_name')
      expect(typeof department.department_id).toBe('number')
      expect(typeof department.department_name).toBe('string')
    })
  })
})
