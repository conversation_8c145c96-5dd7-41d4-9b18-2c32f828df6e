<template>
  <q-dialog v-model="dialogOrPopupStore.showDialogsummry" persistent>
    <q-card class="summary-dialog-card">
      <!-- Header -->
      <q-card-section class="dialog-header">
        <div class="text-h6">📘 การสรุปเนื้อหา</div>
        <q-btn flat dense icon="close" @click="dialogOrPopupStore.showDialogsummry = false" class="text-white" />
      </q-card-section>

      <!-- Body -->
      <q-card-section class="dialog-body">
        <div class="textarea-wrapper">
          <!-- <q-input v-model="summaryText" type="textarea" outlined class="full-width" label="เนื้อหาที่สรุป" :rows="25"
            style="min-height: 500px;" input-style="font-size: 16px; padding: 12px;" /> -->
          <div ref="editorRef"
            style="width: 100%; height: 100%; max-height: 100%; display: flex; flex-direction: column; border-radius: 8px;">
          </div>
          <div v-if="isSummarizing" class="overlay-loading">
            <div class="dot-loader">
              <span></span><span></span><span></span>
            </div>
          </div>
        </div>

      </q-card-section>

      <!-- Actions -->
      <q-card-actions align="between" class="dialog-actions">
        <q-btn label="🔄 สรุปเนื้อหาด้วยบอท" color="primary" :disable="isSummarizing" @click="summarizeContent" />
        <div>
          <q-btn label="บันทึก" color="positive" @click="saveSummary" />
        </div>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { useDialogOrPopupstore } from 'src/stores/dialogOrPopup'
import { nextTick, ref, watch } from 'vue'
import { useDocumentstore } from 'src/stores/document'

const documentStore = useDocumentstore()
const dialogOrPopupStore = useDialogOrPopupstore()
const summaryText = ref(``)

const isSummarizing = ref(false)

const props = defineProps<{
  modelValue: boolean
  initSummaryText: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'summarize-request'): void
  (e: 'update:summary', val: string): void
}>()


watch(() => props.initSummaryText, v => {
  summaryText.value = v
})

const saveSummary = () => {
  emit('update:summary', summaryText.value)
  console.log('บันทึก:', summaryText.value)
  documentStore.editeddocument.summary = summaryText.value
  dialogOrPopupStore.showDialogsummry = false
}

const summarizeContent = () => {
  isSummarizing.value = true
  emit('summarize-request')

  // 💡 For DEMO only — ทำให้เห็น loading
  setTimeout(() => {
    isSummarizing.value = false
  }, 3000)
}

//Sun editor
import SunEditor from 'suneditor'
import type Editor from 'suneditor/src/lib/core'
import 'suneditor/dist/css/suneditor.min.css'
import {
  font,
  paragraphStyle,
  blockquote,
  fontColor,
  hiliteColor,
  textStyle,
  list,
  align,
  horizontalRule,
  lineHeight,
  link,
} from 'suneditor/src/plugins'
import fontSize from 'suneditor/src/plugins/submenu/fontSize'
import formatBlock from 'suneditor/src/plugins/submenu/formatBlock'
import math from 'suneditor/src/plugins/dialog/math'
import 'suneditor/dist/css/suneditor.min.css'
import katex from 'katex'
import 'katex/dist/katex.min.css'
const plugins = [
  font,
  fontSize,
  formatBlock,
  paragraphStyle,
  blockquote,
  fontColor,
  hiliteColor,
  textStyle,
  list,
  align,
  horizontalRule,
  lineHeight,
  link,
  math
]

const editorRef = ref<HTMLElement | null>(null)
let editorInstance: Editor | null = null


watch(
  () => dialogOrPopupStore.showDialogsummry,
  async (visible) => {
    if (!visible) return
    await nextTick()

    if (!editorRef.value) return

    if (editorInstance) {
      editorInstance.destroy()
      editorInstance = null
    }

    const initialContent =
      summaryText.value.length > 0
        ? convertPlainTextToHtml(summaryText.value)
        : '<p>เพื่อความสะดวกของท่าน กรุณากดปุ่มสรุปเนื้อหาด้วยบอท</p>'

    editorInstance = SunEditor.create(editorRef.value, {
      plugins,
      katex,
      minHeight: '450px',
      buttonList: [
        ['undo', 'redo'],
        ['font', 'fontSize', 'formatBlock'],
        ['bold', 'underline', 'italic', 'strike', 'subscript', 'superscript'],
        ['paragraphStyle', 'blockquote'],
        ['fontColor', 'hiliteColor', 'textStyle'],
        ['removeFormat'],
        ['list', 'align', 'horizontalRule', 'lineHeight'],
        ['link', 'math']
      ],
      font: ['Cordia New', 'Sarabun', 'TH Sarabun New', 'Angsana New'],
      defaultTag: 'div',
      value: initialContent,
    })

    editorInstance.onChange = (contents: string) => {
      summaryText.value = contents
    }
  },
  { immediate: true }
)

watch(
  () => summaryText.value,
  (newText) => {
    if (editorInstance) {
      editorInstance.setContents(convertPlainTextToHtml(newText))
    }
  }
)


function convertPlainTextToHtml(plainText: string): string {
  if (plainText == '') return 'เพื่อความสะดวกของท่าน กรุณากดปุ่ม 🔄 สรุปเนื้อหาด้วยบอท'
  return plainText
    .replace(/  +/g, (spaces) => '&nbsp;'.repeat(spaces.length))
    .split('\n')
    .map((line) => (line.trim() === '' ? '<br>' : `<p>${line}</p>`))
    .join('')
}
</script>

<style scoped>
.summary-dialog-card {
  width: 80vw;
  max-width: 1000px;
  max-height: 90vh;
  background-color: white;
  color: black;
}

.dialog-header {
  background-color: #032887;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dialog-body {
  display: flex;
  flex-direction: column;
  height: 100%;
  flex-grow: 1;
  padding: 16px;
}

.dialog-actions {
  padding: 16px;
}

/* ✅ CSS รอโหลดเท่ๆ */
.textarea-wrapper {
  position: relative;
}

.overlay-loading {
  position: absolute;
  top: 8px;
  left: 12px;
  right: 12px;
  bottom: 12px;
  background-color: rgba(255, 255, 255, 0.75);
  backdrop-filter: blur(1px);
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
}

.dot-loader {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.dot-loader span {
  display: block;
  width: 15px;
  height: 15px;
  background-color: #032887;
  border-radius: 50%;
  opacity: 0.6;
  animation: dot-flash 1.4s infinite ease-in-out both;
}

.dot-loader span:nth-child(1) {
  animation-delay: 0s;
}

.dot-loader span:nth-child(2) {
  animation-delay: 0.2s;
}

.dot-loader span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes dot-flash {

  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.6;
  }

  40% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
