import { describe, it, expect, vi, beforeEach } from 'vitest'
import { setActiveP<PERSON>, createP<PERSON> } from 'pinia'
import { useAccessPermissionstore } from 'src/stores/accessPermmission'
import accessPermissionService from 'src/services/accessPermission'
import type { AccessPermission } from 'src/types/AccessPermission'
import { createAxiosResponse } from 'src/test/test-utils'

// Mock the access permission service
vi.mock('src/services/accessPermission')
const mockedAccessPermissionService = vi.mocked(accessPermissionService)

describe('AccessPermission Store', () => {
  let accessPermissionStore: ReturnType<typeof useAccessPermissionstore>

  const mockAccessPermission: AccessPermission = {
    premission_id: 1,
    doc_id: 1,
    roles: [
      { role_id: 1, role_name: 'Admin' },
      { role_id: 2, role_name: 'User' },
    ],
  }

  const mockAccessPermissions: AccessPermission = {
    premission_id: 2,
    doc_id: 2,
    roles: [{ role_id: 1, role_name: 'Admin' }],
  }

  beforeEach(() => {
    setActivePinia(createPinia())
    accessPermissionStore = useAccessPermissionstore()
    vi.clearAllMocks()
  })

  describe('initial state', () => {
    it('should have correct initial state', () => {
      expect(accessPermissionStore.accessPermissions).toBeUndefined()

      expect(accessPermissionStore.initialAccessPermission).toBeDefined()
      expect(accessPermissionStore.initialAccessPermission.premission_id).toBe(0)
      expect(accessPermissionStore.initialAccessPermission.doc_id).toBe(0)
      expect(Array.isArray(accessPermissionStore.initialAccessPermission.roles)).toBe(true)
      expect(accessPermissionStore.initialAccessPermission.roles).toHaveLength(0)

      expect(accessPermissionStore.editedAccessPermission).toBeDefined()
      expect(accessPermissionStore.editedAccessPermission.premission_id).toBe(0)
      expect(accessPermissionStore.editedAccessPermission.doc_id).toBe(0)
      expect(Array.isArray(accessPermissionStore.editedAccessPermission.roles)).toBe(true)
      expect(accessPermissionStore.editedAccessPermission.roles).toHaveLength(0)
    })

    it('should validate initial access permission structure', () => {
      const { initialAccessPermission } = accessPermissionStore

      expect(typeof initialAccessPermission.premission_id).toBe('number')
      expect(typeof initialAccessPermission.doc_id).toBe('number')
      expect(Array.isArray(initialAccessPermission.roles)).toBe(true)
    })

    it('should have editedAccessPermission as deep copy of initialAccessPermission', () => {
      expect(accessPermissionStore.editedAccessPermission).toEqual(
        accessPermissionStore.initialAccessPermission,
      )
      expect(accessPermissionStore.editedAccessPermission).not.toBe(
        accessPermissionStore.initialAccessPermission,
      )
    })
  })

  describe('getAccessPermission action', () => {
    it('should fetch access permission successfully', async () => {
      const mockResponse = createAxiosResponse(mockAccessPermission)
      mockedAccessPermissionService.getAccessPermission.mockResolvedValue(mockResponse)

      await accessPermissionStore.getAccessPermission(1)

      expect(mockedAccessPermissionService.getAccessPermission).toHaveBeenCalledWith(1)
      expect(mockedAccessPermissionService.getAccessPermission).toHaveBeenCalledTimes(1)
    })

    it('should handle different access permission IDs', async () => {
      const mockResponse = createAxiosResponse(mockAccessPermissions)
      mockedAccessPermissionService.getAccessPermission.mockResolvedValue(mockResponse)

      await accessPermissionStore.getAccessPermission(2)

      expect(mockedAccessPermissionService.getAccessPermission).toHaveBeenCalledWith(2)
    })

    it('should handle error when fetching access permission', async () => {
      const mockError = new Error('Access permission not found')
      mockedAccessPermissionService.getAccessPermission.mockRejectedValue(mockError)

      await expect(accessPermissionStore.getAccessPermission(999)).rejects.toThrow(
        'Access permission not found',
      )
      expect(mockedAccessPermissionService.getAccessPermission).toHaveBeenCalledWith(999)
    })
  })

  describe('getAccessPermissions action', () => {
    it('should fetch all access permissions successfully', async () => {
      const mockResponse = createAxiosResponse(mockAccessPermissions)
      mockedAccessPermissionService.getAccessPermissiones.mockResolvedValue(mockResponse)

      await accessPermissionStore.getAccessPermissions()

      expect(mockedAccessPermissionService.getAccessPermissiones).toHaveBeenCalledTimes(1)
      expect(accessPermissionStore.accessPermissions).toEqual(mockAccessPermissions)
    })

    it('should handle error when fetching access permissions', async () => {
      const mockError = new Error('Failed to fetch access permissions')
      mockedAccessPermissionService.getAccessPermissiones.mockRejectedValue(mockError)

      // Mock console.error to avoid error output in tests
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      await accessPermissionStore.getAccessPermissions()

      expect(mockedAccessPermissionService.getAccessPermissiones).toHaveBeenCalledTimes(1)
      expect(consoleSpy).toHaveBeenCalledWith('Error fetching user:', mockError)

      consoleSpy.mockRestore()
    })
  })

  describe('saveAccessPermission action', () => {
    it('should create access permission when premission_id > 0', async () => {
      const mockResponse = createAxiosResponse({ message: 'Created successfully' })
      mockedAccessPermissionService.createAccessPermByDocIdandRoleId.mockResolvedValue(mockResponse)

      accessPermissionStore.editedAccessPermission = { ...mockAccessPermission, premission_id: 1 }

      await accessPermissionStore.saveAccessPermission()

      expect(mockedAccessPermissionService.createAccessPermByDocIdandRoleId).toHaveBeenCalledWith(
        accessPermissionStore.editedAccessPermission,
      )
    })

    it('should update access permission when premission_id <= 0', async () => {
      const mockResponse = createAxiosResponse({ message: 'Updated successfully' })
      mockedAccessPermissionService.updateAccessPermByDocIdandRoleId.mockResolvedValue(mockResponse)

      accessPermissionStore.editedAccessPermission = { ...mockAccessPermission, premission_id: 0 }

      await accessPermissionStore.saveAccessPermission()

      expect(mockedAccessPermissionService.updateAccessPermByDocIdandRoleId).toHaveBeenCalledWith(
        accessPermissionStore.editedAccessPermission,
      )
    })

    it('should handle error when saving access permission', async () => {
      const mockError = new Error('Failed to save access permission')
      mockedAccessPermissionService.createAccessPermByDocIdandRoleId.mockRejectedValue(mockError)

      // Mock console.error to avoid error output in tests
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      accessPermissionStore.editedAccessPermission = { ...mockAccessPermission, premission_id: 1 }

      await accessPermissionStore.saveAccessPermission()

      expect(consoleSpy).toHaveBeenCalledWith('Error fetching user:', mockError)

      consoleSpy.mockRestore()
    })
  })

  describe('saveAccessPermissions action', () => {
    it('should save multiple access permissions', async () => {
      const mockResponse = createAxiosResponse({ message: 'Saved successfully' })
      mockedAccessPermissionService.createAccessPermsByDocIdandRoleId.mockResolvedValue(
        mockResponse,
      )

      await accessPermissionStore.saveAccessPermissions(mockAccessPermission)

      expect(mockedAccessPermissionService.createAccessPermsByDocIdandRoleId).toHaveBeenCalledWith(
        mockAccessPermission,
      )
    })

    it('should handle error when saving multiple access permissions', async () => {
      const mockError = new Error('Failed to save access permissions')
      mockedAccessPermissionService.createAccessPermsByDocIdandRoleId.mockRejectedValue(mockError)

      // Mock console.error to avoid error output in tests
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      await accessPermissionStore.saveAccessPermissions(mockAccessPermission)

      expect(consoleSpy).toHaveBeenCalledWith('Error fetching user:', mockError)

      consoleSpy.mockRestore()
    })
  })

  describe('store structure', () => {
    it('should export correct properties and methods', () => {
      expect(accessPermissionStore).toHaveProperty('accessPermissions')
      expect(accessPermissionStore).toHaveProperty('initialAccessPermission')
      expect(accessPermissionStore).toHaveProperty('editedAccessPermission')
      expect(accessPermissionStore).toHaveProperty('getAccessPermission')
      expect(accessPermissionStore).toHaveProperty('getAccessPermissions')
      expect(accessPermissionStore).toHaveProperty('saveAccessPermission')
      expect(accessPermissionStore).toHaveProperty('saveAccessPermissions')

      expect(typeof accessPermissionStore.getAccessPermission).toBe('function')
      expect(typeof accessPermissionStore.getAccessPermissions).toBe('function')
      expect(typeof accessPermissionStore.saveAccessPermission).toBe('function')
      expect(typeof accessPermissionStore.saveAccessPermissions).toBe('function')
    })

    it('should have reactive state', () => {
      expect(accessPermissionStore.initialAccessPermission).toBeDefined()
      expect(accessPermissionStore.editedAccessPermission).toBeDefined()
    })
  })
})
