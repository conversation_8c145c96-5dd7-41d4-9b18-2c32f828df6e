// src/boot/axios.ts
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { boot } from 'quasar/wrappers'
import axios from 'axios'
import { useRouter } from 'vue-router'
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import type { App } from 'vue'

const instance = axios.create({
  baseURL: 'http://localhost:8080/iverytried',
})

function delay(sec: number) {
  return new Promise((resolve) => {
    setTimeout(() => resolve(sec), sec * 20)
  })
}

instance.interceptors.request.use(async function (config) {
  const token = localStorage.getItem('access_token')
  if (token) {
    console.log('token:', token)
    config.headers.Authorization = `Bearer ${token}`
  }
  await delay(1)
  return config
})

instance.interceptors.response.use(
  async function (res) {
    await delay(1)
    return res
  },
  function (error) {
    const router = useRouter()
    if (error.response && error.response.status === 401) {
      console.log('Hey 401')
      router.replace('/login').catch((err) => {
        console.error('Error navigating to login:', err)
      })
    }
    return Promise.reject(new Error('Unauthorized access'))
  },
)

export default instance
