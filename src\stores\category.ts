import { defineStore } from 'pinia'
import type { Category } from 'src/types/Category'
import categoryServices from 'src/services/category'
import { ref } from 'vue'

export const useCategorystore = defineStore('category', () => {
  const categories = ref<Category[]>([])
  const initialCategory: Category = {
    category_id: 0,
    category_name: '',
  }
  const editedCategory = ref<Category>(JSON.parse(JSON.stringify(initialCategory)))

  async function getCategory(id: number) {
    //oadingStore.doLoad()
    const res = await categoryServices.getCategory(id)
    editedCategory.value = res.data
    //loadingStore.finish()
  }

  async function getCategories() {
    try {
      //loadingStore.doLoad()
      const res = await categoryServices.getCategories()
      categories.value = res.data
      //loadingStore.finish()
    } catch (e) {
      console.error('Error fetching user:', e)
      //loadingStore.finish()
    }
  }
  return {
    categories,
    initialCategory,
    editedCategory,
    getCategory,
    getCategories,
  }
})
