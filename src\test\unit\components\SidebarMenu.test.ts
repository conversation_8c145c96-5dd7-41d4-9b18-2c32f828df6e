import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import SidebarMenu from 'src/components/SidebarMenu.vue'

describe('SidebarMenu Component', () => {
  const mountOptions = {
    global: {
      stubs: {
        QIcon: {
          template: '<i :name="name" />',
          props: ['name'],
        },
        QBtn: {
          template: '<button :icon="icon" :label="label" @click="handleClick" class="q-btn" />',
          props: ['icon', 'label', 'flat', 'dense'],
          emits: ['click'],
          setup(_props: any, { emit }: any) {
            const handleClick = (event: Event) => {
              event.stopPropagation()
              emit('click', event)
            }
            return { handleClick }
          },
        },
      },
    },
  }

  it('should render correctly in closed state', () => {
    const wrapper = mount(SidebarMenu, mountOptions)

    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.sidebar').exists()).toBe(true)
    expect(wrapper.find('.sidebar').classes()).not.toContain('open')
  })

  it('should show menu icon when closed', () => {
    const wrapper = mount(SidebarMenu, mountOptions)

    // Should show menu icon when closed
    expect(wrapper.find('.sidebar-toggle').exists()).toBe(true)
    expect(wrapper.find('.sidebar-toggle__icon').attributes('name')).toBe('menu')
    expect(wrapper.find('.sidebar-content').exists()).toBe(true) // exists but not visible
    expect(wrapper.find('.sidebar-content').isVisible()).toBe(false)
  })

  it('should toggle sidebar when clicked', async () => {
    const wrapper = mount(SidebarMenu, mountOptions)

    // Initially closed
    expect(wrapper.find('.sidebar').classes()).not.toContain('sidebar--open')

    // Click toggle button to open
    await wrapper.find('.sidebar-toggle').trigger('click')

    expect(wrapper.find('.sidebar').classes()).toContain('sidebar--open')
  })

  it('should show sidebar content when open', async () => {
    const wrapper = mount(SidebarMenu, mountOptions)

    // Open the sidebar
    await wrapper.find('.sidebar-toggle').trigger('click')

    expect(wrapper.find('.sidebar-header').exists()).toBe(true)
    expect(wrapper.find('.sidebar-content').isVisible()).toBe(true)
    expect(wrapper.find('.sidebar-header__title').text()).toBe('เมนูหลัก')
  })

  it('should render all menu buttons when open', async () => {
    const wrapper = mount(SidebarMenu, mountOptions)

    // Open the sidebar
    await wrapper.find('.sidebar-toggle').trigger('click')

    const menuButtons = wrapper.findAll('.sidebar-nav__item')
    expect(menuButtons).toHaveLength(3)

    // Check button labels
    expect(menuButtons[0]?.text()).toContain('เนื้อหาทั้งหมด')
    expect(menuButtons[1]?.text()).toContain('เนื้อหาแบบสรุป')
    expect(menuButtons[2]?.text()).toContain('PDF ต้นฉบับ')
  })

  it('should emit menu-selected event when menu button is clicked', async () => {
    const wrapper = mount(SidebarMenu, mountOptions)

    // Open the sidebar
    await wrapper.find('.sidebar-toggle').trigger('click')

    // Click on first menu button
    const firstButton = wrapper.findAll('.sidebar-nav__item')[0]
    if (firstButton) {
      await firstButton.trigger('click')
      expect(wrapper.emitted('menu-selected')).toBeTruthy()
      expect(wrapper.emitted('menu-selected')?.[0]).toEqual(['all'])
    }
  })

  it('should emit correct menu values for different buttons', async () => {
    const wrapper = mount(SidebarMenu, mountOptions)

    // Open the sidebar
    await wrapper.find('.sidebar-toggle').trigger('click')

    const buttons = wrapper.findAll('.sidebar-nav__item')

    // Test first button (all)
    if (buttons[0]) {
      await buttons[0].trigger('click')
      expect(wrapper.emitted('menu-selected')?.[0]).toEqual(['all'])
    }

    // Test that at least one event was emitted
    expect(wrapper.emitted('menu-selected')).toBeTruthy()
    expect(wrapper.emitted('menu-selected')?.length).toBeGreaterThan(0)
  })

  it('should close sidebar after menu selection', async () => {
    const wrapper = mount(SidebarMenu, mountOptions)

    // Open the sidebar
    await wrapper.find('.sidebar-toggle').trigger('click')
    expect(wrapper.find('.sidebar').classes()).toContain('sidebar--open')

    // Click on a menu button
    const firstButton = wrapper.findAll('.sidebar-nav__item')[0]
    if (firstButton) {
      await firstButton.trigger('click')
      // Check that menu-selected event was emitted and sidebar is closed
      expect(wrapper.emitted('menu-selected')).toBeTruthy()
      expect(wrapper.find('.sidebar').classes()).not.toContain('sidebar--open')
    }
  })

  it('should prevent event propagation on menu button clicks', async () => {
    const wrapper = mount(SidebarMenu, mountOptions)

    // Open the sidebar
    await wrapper.find('.sidebar-toggle').trigger('click')

    // Click on menu button and verify it emits the expected event
    const firstButton = wrapper.findAll('.sidebar-nav__item')[0]
    if (firstButton) {
      await firstButton.trigger('click')
      // Check that the button click was handled properly
      expect(wrapper.emitted('menu-selected')).toBeTruthy()
    }
  })

  it('should have correct CSS classes', () => {
    const wrapper = mount(SidebarMenu, mountOptions)

    expect(wrapper.find('.sidebar').exists()).toBe(true)
    expect(wrapper.find('.sidebar').classes()).toContain('sidebar')
  })

  it('should toggle between open and closed states multiple times', async () => {
    const wrapper = mount(SidebarMenu, mountOptions)

    // Initially closed
    expect(wrapper.find('.sidebar').classes()).not.toContain('sidebar--open')

    // Open
    await wrapper.find('.sidebar-toggle').trigger('click')
    expect(wrapper.find('.sidebar').classes()).toContain('sidebar--open')

    // Close
    await wrapper.find('.sidebar-toggle').trigger('click')
    expect(wrapper.find('.sidebar').classes()).not.toContain('sidebar--open')

    // Open again
    await wrapper.find('.sidebar-toggle').trigger('click')
    expect(wrapper.find('.sidebar').classes()).toContain('sidebar--open')
  })

  it('should have correct button icons', async () => {
    const wrapper = mount(SidebarMenu, mountOptions)

    // Open the sidebar
    await wrapper.find('.sidebar-toggle').trigger('click')

    const menuButtons = wrapper.findAll('.sidebar-nav__item')

    // Check that the toggle button has the correct icon
    expect(wrapper.find('.sidebar-toggle__icon').attributes('name')).toBe('close')

    // Check menu button icons through their q-icon children
    expect(menuButtons[0]?.find('.sidebar-nav__icon').attributes('name')).toBe('description')
    expect(menuButtons[1]?.find('.sidebar-nav__icon').attributes('name')).toBe('summarize')
    expect(menuButtons[2]?.find('.sidebar-nav__icon').attributes('name')).toBe('picture_as_pdf')
  })
})
