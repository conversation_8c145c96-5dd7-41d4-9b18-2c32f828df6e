<template>
  <q-dialog v-model="dialogOrPopupStore.showFilewarningDialog">
    <q-card class="q-pa-lg row items-center justify-center"
      style="width: 560px; max-width: 80vw; height: 239px; max-height: 30vw;">
      <q-btn icon="close" flat dense round class="absolute-top-right"
        @click="dialogOrPopupStore.showFilewarningDialog = false" />

      <div class="column items-center">
        <q-img src="/src/assets/img/Vector.png" style="width: 80px; margin-bottom: 16px" spinner-color="red" />
        <div class="text-h6 text-weight-bold text-black-9" style="letter-spacing: 2px;">
          ไฟล์มีขนาดเกิน
          <span class="text-bold text-red" style="margin-left: 6px;">10 MB</span>
        </div>

      </div>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { useDialogOrPopupstore } from 'src/stores/dialogOrPopup';
const dialogOrPopupStore = useDialogOrPopupstore()
</script>
