<template>
  <q-page-container>
    <q-page class="q-pt-md background-image">
      <!-- Fixed Sidebar -->
      <SidebarMenu @menu-selected="selectMenu" :selected-menu="selectedMenu" />

      <!-- Main Content with proper margin for sidebar -->
      <div class="main-content-area">
        <!-- Document Header -->
        <div class="document-header">
          <div class="document-title">
            <q-icon name="description" size="24px" color="primary" />
            <h1>{{ documentStore.editeddocument?.doc_name || 'กำลังโหลดเอกสาร...' }}</h1>
          </div>
          <div class="document-meta">
            <div class="meta-item">
              <q-icon name="category" size="16px" />
              <span>{{ documentStore.editeddocument?.category?.category_name || 'ไม่ระบุประเภท' }}</span>
            </div>
            <div class="meta-item">
              <q-icon name="business" size="16px" />
              <span>{{ documentStore.editeddocument?.department?.department_name || 'ไม่ระบุหน่วยงาน' }}</span>
            </div>
            <div class="meta-item">
              <q-icon name="schedule" size="16px" />
              <span>{{ formatDate(documentStore.editeddocument?.created_date) }}</span>
            </div>
          </div>
        </div>

        <!-- Content Card -->
        <q-card class="document-content-card" flat>
          <!-- Content Area -->
          <div class="content-wrapper">
            <!-- Full Content View -->
            <div v-if="selectedMenu === 'all'" class="content-section">
              <div class="content-header">
                <q-icon name="article" size="20px" color="primary" />
                <span class="content-title">เนื้อหาเอกสารฉบับเต็ม</span>
                <q-space />
                <div class="content-actions">
                  <q-btn flat round icon="text_increase" size="sm" @click="increaseFontSize"
                    title="เพิ่มขนาดตัวอักษร" />
                  <q-btn flat round icon="text_decrease" size="sm" @click="decreaseFontSize" title="ลดขนาดตัวอักษร" />
                  <q-btn flat round icon="content_copy" size="sm" @click="copyContent" title="คัดลอกเนื้อหา" />
                </div>
              </div>
              <div v-html="formattedContent" class="text-content" :style="{ fontSize: fontSize + 'px' }"></div>
            </div>

            <!-- Summary View -->
            <div v-else-if="selectedMenu === 'summary'" class="content-section">
              <div class="content-header">
                <q-icon name="summarize" size="20px" color="orange" />
                <span class="content-title">เนื้อหาแบบสรุป</span>
                <q-space />
                <div class="content-actions">
                  <q-btn flat round icon="text_increase" size="sm" @click="increaseFontSize"
                    title="เพิ่มขนาดตัวอักษร" />
                  <q-btn flat round icon="text_decrease" size="sm" @click="decreaseFontSize" title="ลดขนาดตัวอักษร" />
                  <q-btn flat round icon="content_copy" size="sm" @click="copySummary" title="คัดลอกเนื้อหา" />
                </div>
              </div>
              <div v-html="formattedSummary" class="text-content summary-content"
                :style="{ fontSize: fontSize + 'px' }"></div>
            </div>

            <!-- PDF View -->
            <div v-else-if="selectedMenu === 'pdf'" class="content-section">
              <div class="content-header">
                <q-icon name="picture_as_pdf" size="20px" color="red" />
                <span class="content-title">เอกสาร PDF ต้นฉบับ</span>
                <q-space />
                <div class="content-actions">
                  <q-btn flat round icon="download" size="sm" @click="downloadPdf" title="ดาวน์โหลด PDF" />
                </div>
              </div>
              <div class="pdf-container" :class="{ 'fullscreen': isFullscreen }">
                <iframe :src="pdfUrl" :key="pdfUrl" width="100%" height="800px"
                  style="border: none; border-radius: 8px;" title="PDF Document" />
              </div>
            </div>

            <!-- Empty State -->
            <div v-else class="empty-state">
              <q-icon name="description" size="64px" color="grey-4" />
              <h3>เลือกรูปแบบการดูเอกสาร</h3>
              <p>กรุณาเลือกรูปแบบการดูเอกสารจากเมนูด้านซ้าย</p>
            </div>
          </div>
        </q-card>
      </div>

      <LoadingDialog></LoadingDialog>

      <!-- BACK TO TOP -->
      <div v-show="showBackToTop" class="back-to-top" @click="scrollToTop">
        <q-icon name="expand_less" size="32px" />
      </div>
    </q-page>
  </q-page-container>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, onBeforeUnmount } from 'vue'
import { useRoute } from 'vue-router'
import { useDocumentstore } from 'src/stores/document'
import documentService from 'src/services/document'
import SidebarMenu from 'src/components/SidebarMenu.vue'
import { useQuasar } from 'quasar'
import { useDialogOrPopupstore } from 'src/stores/dialogOrPopup'
import LoadingDialog from 'src/components/LoadingDialog.vue'

const dialogOrPopupstore = useDialogOrPopupstore()
const $q = useQuasar()
const documentStore = useDocumentstore()
const documentContent = ref('')
const summaryContent = ref('')
const selectedMenu = ref('all')
const route = useRoute()
const docId = route.params.id
const pdfUrl = ref<string>('')
const showBackToTop = ref(false)
const fontSize = ref(16)
const isFullscreen = ref(false)

// ✅ formattedContent
const formattedContent = computed(() => {
  if (!documentContent.value) return ''

  let text = documentContent.value

  // normalize all possible newline
  text = text.replace(/\\r\\n|\\r|\\n/g, '\n')
  text = text.replace(/\r\n|\r/g, '\n')  // raw text
  text = text.replace(/\\n/g, '\n')      // unescape literal

  return text.replace(/\n/g, '<br>')
})

// ✅ formattedSummary
const formattedSummary = computed(() => {
  if (!summaryContent.value) return ''

  let text = summaryContent.value

  // normalize all possible newline
  text = text.replace(/\\r\\n|\\r|\\n/g, '\n')
  text = text.replace(/\r\n|\r/g, '\n')  // raw text
  text = text.replace(/\\n/g, '\n')      // unescape literal

  return text.replace(/\n/g, '<br>')
})


async function selectMenu(menu: string) {
  selectedMenu.value = menu
  dialogOrPopupstore.setLoadingShowAndRename(true, 'กำลังโหลดข้อมูล...')
  try {
    const docResponse = await documentService.getDocument(Number(docId))
    documentStore.editeddocument = docResponse.data

    if (menu === 'all') {
      const response = await documentService.getFullDocument(Number(docId))
      const pages = response.data
      const content = pages.map((p: { content: string }) => p.content).join('\n\n')
      documentContent.value = content || 'ไม่พบเนื้อหาในเอกสาร'
    } else if (menu === 'summary') {
      const wiki = await documentService.getSummary(Number(docId))
      summaryContent.value = wiki.data?.summary || 'ยังไม่มีเนื้อหาแบบสรุป'
    } else if (menu === 'pdf') {
      if (pdfUrl.value) {
        URL.revokeObjectURL(pdfUrl.value)
        pdfUrl.value = ''
      }
      const url = await documentService.getPdfDocument(Number(docId))
      pdfUrl.value = url
    } else {
      documentContent.value = 'เลือกเมนูที่ถูกต้อง'
    }
  } catch (error) {
    console.error('Error fetching document content or metadata:', error)
    documentContent.value = 'เกิดข้อผิดพลาดในการโหลดข้อมูล'
  } finally {
    dialogOrPopupstore.setLoadingShowAndRename(false, '')
  }
}

// ✅ Back-to-top logic
function handleScroll() {
  showBackToTop.value = window.scrollY > 300
}

function scrollToTop() {
  window.scrollTo({ top: 0, behavior: 'smooth' })
}

// ✅ Date formatting function
function formatDate(dateString: string | undefined) {
  if (!dateString) return 'ไม่ระบุวันที่'
  try {
    return new Date(dateString).toLocaleDateString('th-TH', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    })
  } catch {
    return 'ไม่ระบุวันที่'
  }
}

// ✅ Font size controls
function increaseFontSize() {
  if (fontSize.value < 24) {
    fontSize.value += 2
  }
}

function decreaseFontSize() {
  if (fontSize.value > 12) {
    fontSize.value -= 2
  }
}

// ✅ Copy functions
async function copyContent() {
  try {
    const textContent = documentContent.value.replace(/<br>/g, '\n').replace(/<[^>]*>/g, '')
    await navigator.clipboard.writeText(textContent)
    $q.notify({
      type: 'positive',
      message: 'คัดลอกเนื้อหาเรียบร้อยแล้ว',
      position: 'top',
      timeout: 2000
    })
  } catch (error) {
    console.error('Failed to copy content:', error)
    $q.notify({
      type: 'negative',
      message: 'ไม่สามารถคัดลอกเนื้อหาได้',
      position: 'top',
      timeout: 2000
    })
  }
}

async function copySummary() {
  try {
    const textContent = summaryContent.value.replace(/<br>/g, '\n').replace(/<[^>]*>/g, '')
    await navigator.clipboard.writeText(textContent)
    $q.notify({
      type: 'positive',
      message: 'คัดลอกเนื้อหาสรุปเรียบร้อยแล้ว',
      position: 'top',
      timeout: 2000
    })
  } catch (error) {
    console.error('Failed to copy summary:', error)
    $q.notify({
      type: 'negative',
      message: 'ไม่สามารถคัดลอกเนื้อหาได้',
      position: 'top',
      timeout: 2000
    })
  }
}


function downloadPdf() {
  if (pdfUrl.value) {
    const link = document.createElement('a')
    link.href = pdfUrl.value
    link.download = `${documentStore.editeddocument?.doc_name || 'document'}.pdf`
    link.click()
  }
}

onMounted(async () => {
  window.addEventListener('scroll', handleScroll)
  await selectMenu('all')
})


onBeforeUnmount(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
.background-image {
  position: relative;
  overflow: hidden;
  min-height: 100vh;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" style="stop-color:%23ffffff;stop-opacity:0.1"/><stop offset="100%" style="stop-color:%23000000;stop-opacity:0.05"/></radialGradient><linearGradient id="b" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%23f8fafc"/><stop offset="50%" style="stop-color:%23e2e8f0"/><stop offset="100%" style="stop-color:%23cbd5e1"/></linearGradient></defs><rect width="100%" height="100%" fill="url(%23b)"/><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="300" cy="700" r="120" fill="url(%23a)"/><circle cx="700" cy="800" r="80" fill="url(%23a)"/></svg>');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

/* Main content area with proper spacing for floating menu */
.main-content-area {
  padding: 24px;
  min-height: 100vh;
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .main-content-area {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .main-content-area {
    padding: 12px;
  }
}

/* Document Header Styles */
.document-header {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 32px;
  margin-top: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.document-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #1976d2, #42a5f5, #64b5f6);
  border-radius: 16px 16px 0 0;
}

.document-title {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}

.document-title h1 {
  margin: 0;
  font-size: 26px;
  font-weight: 700;
  color: #1a1a1a;
  font-family: 'Sarabun', sans-serif;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  line-height: 1.2;
  padding-right: 50px;
  /* Space for floating menu */
}

@media (max-width: 768px) {
  .document-title h1 {
    padding-right: 10px;
    /* Reduced space on mobile */
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .document-title h1 {
    padding-right: 10px;
    /* Further reduced on small screens */
    font-size: 20px;
  }
}

.document-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(25, 118, 210, 0.08);
  border-radius: 20px;
  border: 1px solid rgba(25, 118, 210, 0.15);
  transition: all 0.2s ease;
  color: #666;
  font-size: 14px;
}

.meta-item:hover {
  background: rgba(25, 118, 210, 0.12);
  transform: translateY(-1px);
}

/* Content Card Styles */
.document-content-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12), 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
  overflow: hidden;
  backdrop-filter: blur(10px);
  position: relative;
}

.document-content-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #1976d2, #42a5f5, #64b5f6);
}

.content-wrapper {
  min-height: 600px;
}

.content-section {
  height: 100%;
}

.content-header {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 24px 32px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  position: relative;
  margin-top: 4px;
}

.content-header::before {
  content: '';
  position: absolute;
  left: 32px;
  right: 32px;
  bottom: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(25, 118, 210, 0.3), transparent);
}

.content-title {
  font-size: 18px;
  font-weight: 700;
  color: #1a1a1a;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.content-actions {
  display: flex;
  gap: 4px;
}

.text-content {
  white-space: normal;
  word-break: break-word;
  font-family: 'Sarabun', sans-serif;
  line-height: 1.8;
  color: #2c3e50;
  padding: 32px;
  background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%);
  transition: font-size 0.2s ease;
  position: relative;
}

.text-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 32px;
  right: 32px;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(25, 118, 210, 0.1), transparent);
}

.summary-content {
  background: linear-gradient(135deg, #fff8e1 0%, #ffffff 100%);
  border-left: 4px solid #ff9800;
  position: relative;
}

.summary-content::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 60px;
  height: 60px;
  background: radial-gradient(circle, rgba(255, 152, 0, 0.1) 0%, transparent 70%);
  border-radius: 50%;
}

/* PDF Container */
.pdf-container {
  padding: 24px;
  background: #f5f5f5;
}

.pdf-container.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: white;
  padding: 0;
}

.pdf-container.fullscreen iframe {
  width: 100vw !important;
  height: 100vh !important;
  border-radius: 0 !important;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 24px;
  text-align: center;
  color: #666;
}

.empty-state h3 {
  margin: 16px 0 8px 0;
  font-size: 20px;
  color: #333;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

.background-image::before {
  content: "";
  position: absolute;
  inset: 0;
  background-image: url('/src/assets/img/Docview.jpg');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center 16px;
  z-index: -2;
}

.background-image::after {
  content: "";
  position: absolute;
  inset: 0;
  background-color: rgba(9, 18, 92, 0.64);
  margin-top: 16px;
  z-index: -1;
}

.background-image>* {
  position: relative;
  z-index: 1;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .document-meta {
    flex-direction: column;
    gap: 12px;
  }

  .content-header {
    align-items: flex-start;
    gap: 16px;
  }

  .content-actions {
    align-self: flex-end;
  }
}

@media (max-width: 768px) {
  .document-header {
    padding: 16px;
  }

  .document-title h1 {
    font-size: 20px;
  }

  .text-content {
    padding: 16px;
  }

  .content-header {
    padding: 16px;
  }

  .pdf-container {
    padding: 16px;
  }

  .content-header {
    display: flex;
    /* Arrange items in a row */
    flex-wrap: nowrap;
    /* Prevent wrapping */
    flex-direction: row;
    /* Align items in a row */
    gap: 16px;
    /* Add space between elements */
    padding: 16px;
  }

}


.back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 50%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  width: 56px;
  height: 56px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10000;
}

.back-to-top:hover {
  transform: scale(1.1);
  background: rgba(255, 255, 255, 1);
}

/* Scroll animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.document-header {
  animation: fadeInUp 0.6s ease-out;
}

.document-content-card {
  animation: fadeInUp 0.6s ease-out 0.2s both;
}

/* Enhanced hover effects */
.document-content-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15), 0 8px 16px rgba(0, 0, 0, 0.1);
}

/* จอใหญ่ (Default) */
.content-header {
  display: flex;
  flex-direction: row;
  /* เรียงเป็นแถว */
  align-items: center;
  /* จัดกึ่งกลางแนวตั้ง */
  justify-content: flex-start;
  gap: 16px;
  padding: 24px 32px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  position: relative;
  margin-top: 4px;
}

/* จอขนาดกลาง (<= 1024px) */
@media (max-width: 1024px) {
  .content-header {
    flex-wrap: wrap;
    /* อนุญาตให้ขึ้นบรรทัดใหม่ */
    justify-content: space-between;
    align-items: flex-start;
    /* ให้ข้อความอยู่ด้านบน */
  }

  .content-actions {
    margin-left: auto;
    /* ดันปุ่มไปขวา */
  }
}

/* จอเล็ก (<= 768px) */
@media (max-width: 768px) and (min-width: 400px) {
  .content-header {

    /* เรียงบน-ล่าง */
    /* align-items: flex-start; */
    gap: 12px;
    /* padding: 16px; */
  }

  .content-actions {
    align-self: flex-end;
    /* ให้ปุ่มไปอยู่มุมขวา */
  }
}

@media (max-width: 411px) {
  .content-actions {
    align-self: flex-start;
    /* ให้ปุ่มไปซ้าย */
    margin-left: 0;
    /* เอา margin-left: auto ออก */
  }
}
</style>
