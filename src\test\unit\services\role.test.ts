import { describe, it, expect, vi, beforeEach } from 'vitest'
import roleService from 'src/services/role'
import type { Role } from 'src/types/Role'
import { createAxiosResponse } from 'src/test/test-utils'

// Mock axios
vi.mock('src/boot/axios', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    patch: vi.fn(),
  },
}))

// Import the mocked axios
import axios from 'src/boot/axios'
const mockedAxios = axios as any

describe('Role Service', () => {
  const mockRole: Role = {
    role_id: 1,
    role_name: 'Admin',
  }

  const mockRoles: Role[] = [
    mockRole,
    {
      role_id: 2,
      role_name: 'User',
    },
    {
      role_id: 3,
      role_name: 'Manager',
    },
    {
      role_id: 4,
      role_name: 'Guest',
    },
  ]

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getRole', () => {
    it('should fetch a role by id', async () => {
      const mockResponse = createAxiosResponse(mockRole)
      mockedAxios.get.mockResolvedValue(mockResponse)

      const result = await roleService.getRole(1)

      expect(mockedAxios.get).toHaveBeenCalledWith('/role/1')
      expect(result).toEqual(mockResponse)
    })

    it('should handle error when fetching role', async () => {
      const mockError = new Error('Role not found')
      mockedAxios.get.mockRejectedValue(mockError)

      await expect(roleService.getRole(999)).rejects.toThrow('Role not found')
      expect(mockedAxios.get).toHaveBeenCalledWith('/role/999')
    })

    it('should fetch role with different ids', async () => {
      const mockResponse = createAxiosResponse(mockRoles[1])
      mockedAxios.get.mockResolvedValue(mockResponse)

      const result = await roleService.getRole(2)

      expect(mockedAxios.get).toHaveBeenCalledWith('/role/2')
      expect(result).toEqual(mockResponse)
    })
  })

  describe('getRoles', () => {
    it('should fetch all roles', async () => {
      const mockResponse = createAxiosResponse(mockRoles)
      mockedAxios.get.mockResolvedValue(mockResponse)

      const result = await roleService.getRoles()

      expect(mockedAxios.get).toHaveBeenCalledWith('/roles')
      expect(result).toEqual(mockResponse)
      expect(result.data).toHaveLength(4)
    })

    it('should handle error when fetching roles', async () => {
      const mockError = new Error('Failed to fetch roles')
      mockedAxios.get.mockRejectedValue(mockError)

      await expect(roleService.getRoles()).rejects.toThrow('Failed to fetch roles')
      expect(mockedAxios.get).toHaveBeenCalledWith('/roles')
    })

    it('should handle empty roles response', async () => {
      const mockResponse = createAxiosResponse([])
      mockedAxios.get.mockResolvedValue(mockResponse)

      const result = await roleService.getRoles()

      expect(mockedAxios.get).toHaveBeenCalledWith('/roles')
      expect(result.data).toHaveLength(0)
    })
  })

  describe('service structure', () => {
    it('should export correct methods', () => {
      expect(roleService).toHaveProperty('getRole')
      expect(roleService).toHaveProperty('getRoles')
      expect(typeof roleService.getRole).toBe('function')
      expect(typeof roleService.getRoles).toBe('function')
    })
  })
})
