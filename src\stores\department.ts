import { defineStore } from 'pinia'
import type { Department } from 'src/types/Department'
import departmentServices from 'src/services/department'
import { ref } from 'vue'

export const useDepartmentstore = defineStore('department', () => {
  const departments = ref<Department[]>([])
  const initialdepartment: Department = {
    department_id: 0,
    department_name: '',
    //divistion_name: '', ERD has no divistion_name
  }
  const editeddepartment = ref<Department>(JSON.parse(JSON.stringify(initialdepartment)))

  async function getDepartment(id: number) {
    //oadingStore.doLoad()
    const res = await departmentServices.getDepartment(id)
    editeddepartment.value = res.data
    //loadingStore.finish()
  }

  async function getDepartments() {
    try {
      //loadingStore.doLoad()
      const res = await departmentServices.getDepartments()
      departments.value = res.data
      //loadingStore.finish()
    } catch (e) {
      console.error('Error fetching user:', e)
      //loadingStore.finish()
    }
  }
  return {
    departments,
    initialdepartment,
    editeddepartment,
    getDepartment,
    getDepartments,
  }
})
