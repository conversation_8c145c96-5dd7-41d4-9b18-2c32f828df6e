import { describe, it, expect, vi, beforeEach } from 'vitest'
import userService from 'src/services/user'
import type { User } from 'src/types/User'
import { createAxiosResponse } from 'src/test/test-utils'

// Mock axios
vi.mock('src/boot/axios', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    patch: vi.fn(),
  },
}))

// Import the mocked axios
import axios from 'src/boot/axios'
const mockedAxios = axios as any

describe('User Service', () => {
  const mockUser: User = {
    user_id: 1,
    username: 'testuser',
    password: 'password123',
    full_name: 'Test User',
    role: {
      role_id: 1,
      role_name: 'Admin',
    },
    department: {
      department_id: 1,
      department_name: 'IT Department',
    },
  }

  const mockUsers: User[] = [
    mockUser,
    {
      user_id: 2,
      username: 'user2',
      password: 'password456',
      full_name: 'User Two',
      role: {
        role_id: 2,
        role_name: 'User',
      },
      department: {
        department_id: 2,
        department_name: 'HR Department',
      },
    },
  ]

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getUser', () => {
    it('should fetch a user by id', async () => {
      const mockResponse = createAxiosResponse(mockUser)
      mockedAxios.get.mockResolvedValue(mockResponse)

      const result = await userService.getUser(1)

      expect(mockedAxios.get).toHaveBeenCalledWith('/users/1')
      expect(result).toEqual(mockResponse)
    })

    it('should handle error when fetching user', async () => {
      const mockError = new Error('User not found')
      mockedAxios.get.mockRejectedValue(mockError)

      await expect(userService.getUser(999)).rejects.toThrow('User not found')
      expect(mockedAxios.get).toHaveBeenCalledWith('/users/999')
    })
  })

  describe('getUsers', () => {
    it('should fetch all users', async () => {
      const mockResponse = createAxiosResponse(mockUsers)
      mockedAxios.get.mockResolvedValue(mockResponse)

      const result = await userService.getUsers()

      expect(mockedAxios.get).toHaveBeenCalledWith('/users')
      expect(result).toEqual(mockResponse)
    })

    it('should handle error when fetching users', async () => {
      const mockError = new Error('Failed to fetch users')
      mockedAxios.get.mockRejectedValue(mockError)

      await expect(userService.getUsers()).rejects.toThrow('Failed to fetch users')
    })
  })

  describe('createUser', () => {
    it('should create a new user', async () => {
      const newUserData: Partial<User> = {
        username: 'newuser',
        password: 'newpassword',
        full_name: 'New User',
        role: mockUser.role,
        department: mockUser.department,
      }
      const mockResponse = createAxiosResponse({ ...newUserData, user_id: 3 })
      mockedAxios.post.mockResolvedValue(mockResponse)

      const result = await userService.createUser(newUserData)

      expect(mockedAxios.post).toHaveBeenCalledWith('/users', newUserData)
      expect(result).toEqual(mockResponse)
    })

    it('should handle error when creating user', async () => {
      const newUserData: Partial<User> = { username: 'newuser' }
      const mockError = new Error('Failed to create user')
      mockedAxios.post.mockRejectedValue(mockError)

      await expect(userService.createUser(newUserData)).rejects.toThrow('Failed to create user')
    })
  })

  describe('updateUser', () => {
    it('should update an existing user', async () => {
      const updateData: Partial<User> = {
        full_name: 'Updated User Name',
      }
      const mockResponse = createAxiosResponse({ ...mockUser, ...updateData })
      mockedAxios.put.mockResolvedValue(mockResponse)

      const result = await userService.updateUser(1, updateData)

      expect(mockedAxios.put).toHaveBeenCalledWith('/users/1', updateData)
      expect(result).toEqual(mockResponse)
    })

    it('should handle error when updating user', async () => {
      const updateData: Partial<User> = { full_name: 'Updated Name' }
      const mockError = new Error('Failed to update user')
      mockedAxios.put.mockRejectedValue(mockError)

      await expect(userService.updateUser(1, updateData)).rejects.toThrow('Failed to update user')
    })
  })

  describe('deleteUser', () => {
    it('should delete a user by id', async () => {
      const mockResponse = createAxiosResponse({ message: 'User deleted successfully' })
      mockedAxios.delete.mockResolvedValue(mockResponse)

      const result = await userService.deleteUser(1)

      expect(mockedAxios.delete).toHaveBeenCalledWith('/users/1')
      expect(result).toEqual(mockResponse)
    })

    it('should handle error when deleting user', async () => {
      const mockError = new Error('Failed to delete user')
      mockedAxios.delete.mockRejectedValue(mockError)

      await expect(userService.deleteUser(1)).rejects.toThrow('Failed to delete user')
    })
  })
})
