<template>
  <q-dialog persistent transition-show="scale" transition-hide="scale">
    <q-card class="premium-dialog">
      <!-- Header Section -->
      <div class="dialog-header">
        <div class="icon-container">
          <div class="warning-icon-wrapper">
            <q-icon name="warning" class="main-icon" />
          </div>
        </div>
        <div class="header-text">
          <h2 class="dialog-title">ยกเลิกการอัปโหลด</h2>
          <p class="dialog-subtitle">คุณต้องการยกเลิกการอัปโหลดเอกสารหรือไม่?</p>
        </div>
      </div>

      <!-- Content Section -->
      <div class="dialog-body">
        <div class="warning-list">
          <div class="warning-item">
            <div class="warning-icon">
              <q-icon name="info" />
            </div>
            <span class="warning-text">ข้อมูลที่กรอกทั้งหมดจะหายไป</span>
          </div>

          <div class="warning-item">
            <div class="warning-icon">
              <q-icon name="delete_forever" />
            </div>
            <span class="warning-text">ไฟล์ที่อัปโหลดจะไม่ถูกบันทึก</span>
          </div>

          <div class="warning-item">
            <div class="warning-icon">
              <q-icon name="exit_to_app" />
            </div>
            <span class="warning-text">จะกลับไปหน้าจัดการเอกสาร</span>
          </div>
        </div>
      </div>

      <!-- Actions Section -->
      <div class="dialog-actions">
        <q-btn class="secondary-btn" label="ไม่ยกเลิก" no-caps flat @click="dialogOrPopupStore.showCancelDialog = false"
          icon="arrow_back" />
        <q-btn class="primary-btn danger-btn" label="ยืนยันยกเลิก" no-caps @click="backToDocmanage" icon="exit_to_app"
          :loading="isNavigating" />
      </div>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useDialogOrPopupstore } from 'src/stores/dialogOrPopup';
import { useFileUploadStore } from 'src/stores/fileUploadStore'

const dialogOrPopupStore = useDialogOrPopupstore()
const router = useRouter()
const fileUploadStore = useFileUploadStore()

// Loading state for navigation
const isNavigating = ref(false)

async function backToDocmanage() {
  try {
    isNavigating.value = true
    fileUploadStore.resetFiles()
    dialogOrPopupStore.showCancelDialog = false;
    await router.push('/MainLayout/DocManage')
  } catch (error) {
    console.error('Navigation error:', error)
  } finally {
    isNavigating.value = false
  }
}
</script>

<style scoped>
/* ===== PREMIUM DIALOG STYLING ===== */
.premium-dialog {
  width: 420px;
  max-width: 90vw;
  border-radius: 20px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.12),
    0 8px 25px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.06);
  overflow: hidden;
  position: relative;
}

.premium-dialog::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #f59e0b 0%, #f97316 50%, #ef4444 100%);
}

/* ===== DIALOG HEADER ===== */
.dialog-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 24px 24px 20px 24px;
  background: rgba(255, 255, 255, 0.8);
}

.icon-container {
  flex-shrink: 0;
}

.warning-icon-wrapper {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 20px rgba(245, 158, 11, 0.25);
}

.main-icon {
  font-size: 28px;
  color: white;
}

.header-text {
  flex: 1;
  padding-top: 2px;
}

.dialog-title {
  font-size: 20px;
  font-weight: 700;
  color: #f59e0b;
  margin: 0 0 6px 0;
  line-height: 1.3;
}

.dialog-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  font-weight: 500;
  line-height: 1.4;
}

/* ===== DIALOG BODY ===== */
.dialog-body {
  padding: 0 24px 20px 24px;
}

.warning-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.warning-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 14px 16px;
  background: rgba(254, 243, 199, 0.3);
  border-radius: 12px;
  border: 1px solid rgba(245, 158, 11, 0.1);
  transition: all 0.2s ease;
}

.warning-item:hover {
  background: rgba(254, 243, 199, 0.5);
  border-color: rgba(245, 158, 11, 0.2);
}

.warning-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: rgba(245, 158, 11, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.warning-icon .q-icon {
  font-size: 16px;
  color: #f59e0b;
}

.warning-text {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
  line-height: 1.4;
}

/* ===== DIALOG ACTIONS ===== */
.dialog-actions {
  padding: 20px 24px 24px 24px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  background: rgba(248, 250, 252, 0.5);
  border-top: 1px solid rgba(0, 0, 0, 0.06);
}

.secondary-btn {
  padding: 10px 20px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
  color: #6b7280;
  background: transparent;
  border: none;
  transition: all 0.2s ease;
  min-width: 100px;
}

.secondary-btn:hover {
  background: rgba(107, 114, 128, 0.1);
  color: #4b5563;
}

.primary-btn {
  padding: 10px 20px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
  color: white;
  border: none;
  transition: all 0.2s ease;
  min-width: 120px;
}

.danger-btn {
  background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.danger-btn:hover {
  background: linear-gradient(135deg, #d97706 0%, #ea580c 100%);
  box-shadow: 0 6px 16px rgba(245, 158, 11, 0.4);
  transform: translateY(-1px);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 600px) {
  .premium-dialog {
    width: 95vw;
    margin: 16px;
  }

  .dialog-header {
    padding: 20px 20px 16px 20px;
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .dialog-body {
    padding: 0 20px 16px 20px;
  }

  .dialog-actions {
    padding: 16px 20px 20px 20px;
    flex-direction: column;
    gap: 8px;
  }

  .secondary-btn,
  .primary-btn {
    width: 100%;
    min-width: unset;
  }
}
</style>
