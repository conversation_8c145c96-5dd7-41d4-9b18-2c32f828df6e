import type { Document, OcrR<PERSON>ult, PreviousResponse, Spellcheck_results } from 'src/types/Document'
import axios from 'src/boot/axios'

function getDocument(id: number) {
  return axios.get(`/documents/${id}`)
}

function getDocuments() {
  return axios.get(`/documents`)
}

function deleteDoc(document: Document) {
  const { doc_id, is_public } = document
  return axios.patch(`/documents/delete/${doc_id}`, { is_public })
}

function fetchDocPdf(Document_id: number) {
  return axios.get(`/documents/pdf/${Document_id}`)
}

function addNewDoc(document: Document) {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { doc_id, ...data } = document

  return axios.post(`/documents`, data)
}

// function updateDoc(document: Document) {
//   const { doc_id, ...updatedata } = document
//   return axios.patch(`/documents/update/${doc_id}`, updatedata)
// }

function updateDoc(document: Document, file: FormData) {
  console.log('file before to backend', file)
  return axios
    .put(`/documents/${document.doc_id}`, file, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    .then((res) => {
      console.log('Response data:', res.data)
      return res.data
    })
    .catch((error) => console.error(error))
}

//เอาแค่ upload ก่อน
function saveFile(file: FormData): Promise<OcrResult[]> {
  console.log('file before to backend', file)
  return axios
    .post('/ocr/upload', file, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    .then((res) => {
      console.log('Response data:', res.data)
      return res.data
    })
    .catch((error) => console.error(error))
}

function spellCheck(text: OcrResult[]): Promise<Spellcheck_results[]> {
  console.log('text before to backend', text)
  return axios
    .post('/ocr/spellcheck', text)
    .then((res) => {
      console.log('Res.data: ', res.data)
      return res.data
    })
    .catch((error) => console.log(error))
}

function checkPrevious(doc_name: string): Promise<PreviousResponse> {
  return axios
    .post<PreviousResponse>('/document/checkPrevious', doc_name, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    .then((res) => {
      console.log('Response data:', res.data)
      return res.data
    })
    .catch((error) => {
      console.error(error)
      throw error // ควร throw error กลับไปถ้าต้องการจัดการ error ที่ caller
    })
}

function findDocId(doc: { doc_name: string }): Promise<number> {
  return axios.post('/documents/findDocId', doc).then((res) => res.data)
}

function getFullDocument(docId: number) {
  return axios.get('/documents/content/' + docId)
}

function getSummary(docId: number) {
  return axios.get(`/documents/wiki/${docId}`)
}

function getPdfDocument(docId: number) {
  return axios
    .get(`/documents/serve/${docId}`, {
      responseType: 'blob',
    })
    .then((response) => {
      const blob = new Blob([response.data], { type: 'application/pdf' })
      console.log('Blob created:', blob)
      const url = window.URL.createObjectURL(blob)
      return url
    })
    .catch((error) => {
      console.error('Error fetching PDF document:', error)
      throw error
    })
}

function createdSummary(text: OcrResult[]) {
  console.log('text before to backend', text)
  return axios
    .post('/ocr/summary', text)
    .then((res) => {
      console.log('Res.data: ', res.data)
      return res.data
    })
    .catch((error) => console.log(error))
}

function saveDoc(data: FormData) {
  console.log('file before to backend', data)
  return axios
    .post('/documents', data, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    .then((res) => {
      console.log('Response data:', res.data)
      return res.data
    })
    .catch((error) => console.error(error))
}

async function contentSearch(
  query: string | null,
  is_public: string | null,
  access_role: string | null,
  department_id: number | null,
  category_id: number | null,
  page: number | null,
  page_size: number | null,
) {
  const payload = Object.fromEntries(
    Object.entries({
      query,
      is_public,
      access_role,
      department_id,
      category_id,
      page,
      page_size,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    }).filter(([_, v]) => v !== undefined && v !== null && v !== 0 && v != ''), // กรองทั้ง null และ undefined
  )

  const res = await axios.post('/documents/search', payload)
  console.log('vector search: ', res.data)
  return res.data
}

export default {
  getDocument,
  getDocuments,
  deleteDoc,
  fetchDocPdf,
  addNewDoc,
  updateDoc,
  saveFile,
  getPdfDocument,
  spellCheck,
  checkPrevious,
  findDocId,
  getFullDocument,
  getSummary,
  createdSummary,
  saveDoc,
  contentSearch,
}
