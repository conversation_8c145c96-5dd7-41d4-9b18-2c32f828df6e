// app global css in SCSS form
@font-face {
  font-family: 'Cordia New';
  src: url('/font/Cordia New.woff2') format('truetype');
}

@font-face {
  font-family: 'Angsana New';
  src: url('/font/Angsana New.woff2') format('truetype');
}

@font-face {
  font-family: 'TH Sarabun New';
  src: url('/font/SarabunNew/THSarabunNew.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'TH Sarabun New';
  src: url('/font/SarabunNew/THSarabunNew Italic.ttf') format('truetype');
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: 'TH Sarabun New';
  src: url('/font/SarabunNew/THSarabunNew Bold.ttf') format('truetype');
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'TH Sarabun New';
  src: url('/font/SarabunNew/THSarabunNew BoldItalic.ttf') format('truetype');
  font-weight: bold;
  font-style: italic;
}

@font-face {
  font-family: 'Sarabun';
  src: url('/font/Sarabun/Sarabun-Thin.ttf') format('truetype');
  font-weight: 100;
  font-style: normal;
}
@font-face {
  font-family: 'Sarabun';
  src: url('/font/Sarabun/Sarabun-ThinItalic.ttf') format('truetype');
  font-weight: 100;
  font-style: italic;
}

@font-face {
  font-family: 'Sarabun';
  src: url('/font/Sarabun/Sarabun-ExtraLight.ttf') format('truetype');
  font-weight: 200;
  font-style: normal;
}
@font-face {
  font-family: 'Sarabun';
  src: url('/font/Sarabun/Sarabun-ExtraLightItalic.ttf') format('truetype');
  font-weight: 200;
  font-style: italic;
}

@font-face {
  font-family: 'Sarabun';
  src: url('/font/Sarabun/Sarabun-Light.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
}
@font-face {
  font-family: 'Sarabun';
  src: url('/font/Sarabun/Sarabun-LightItalic.ttf') format('truetype');
  font-weight: 300;
  font-style: italic;
}

@font-face {
  font-family: 'Sarabun';
  src: url('/font/Sarabun/Sarabun-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: 'Sarabun';
  src: url('/font/Sarabun/Sarabun-Italic.ttf') format('truetype');
  font-weight: 400;
  font-style: italic;
}

@font-face {
  font-family: 'Sarabun';
  src: url('/font/Sarabun/Sarabun-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
}
@font-face {
  font-family: 'Sarabun';
  src: url('/font/Sarabun/Sarabun-MediumItalic.ttf') format('truetype');
  font-weight: 500;
  font-style: italic;
}

@font-face {
  font-family: 'Sarabun';
  src: url('/font/Sarabun/Sarabun-SemiBold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
}
@font-face {
  font-family: 'Sarabun';
  src: url('/font/Sarabun/Sarabun-SemiBoldItalic.ttf') format('truetype');
  font-weight: 600;
  font-style: italic;
}

@font-face {
  font-family: 'Sarabun';
  src: url('/font/Sarabun/Sarabun-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
}
@font-face {
  font-family: 'Sarabun';
  src: url('/font/Sarabun/Sarabun-BoldItalic.ttf') format('truetype');
  font-weight: 700;
  font-style: italic;
}

@font-face {
  font-family: 'Sarabun';
  src: url('/font/Sarabun/Sarabun-ExtraBold.ttf') format('truetype');
  font-weight: 800;
  font-style: normal;
}
@font-face {
  font-family: 'Sarabun';
  src: url('/font/Sarabun/Sarabun-ExtraBoldItalic.ttf') format('truetype');
  font-weight: 800;
  font-style: italic;
}

/* 👇 Apply as default for SunEditor content */
.sun-editor-editable,
.sun-editor .se-wrapper-inner {
  font-family: 'TH Sarabun New', sans-serif !important;
}

@media screen and (max-width: 1366px) {
  /* โน้ตบุ๊ค */
  .preview-image {
    height: 70vh; /* ปรับให้พอดี */
  }
}

@media screen and (max-width: 1024px) {
  /* แท็บเล็ต */
  .preview-image {
    height: 65vh;
  }
}

@media screen and (max-width: 768px) {
  /* มือถือ */
  .preview-image {
    height: 60vh;
  }
}

body {
  font-family: 'Kanit', sans-serif;
}
