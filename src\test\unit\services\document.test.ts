import { describe, it, expect, vi, beforeEach } from 'vitest'
import documentService from 'src/services/document'
import type { Document, OcrResult, Spellcheck_results } from 'src/types/Document'
import { createAxiosResponse } from 'src/test/test-utils'

// Mock axios
vi.mock('src/boot/axios', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    patch: vi.fn(),
  },
}))

// Import the mocked axios
import axios from 'src/boot/axios'
const mockedAxios = axios as any

describe('Document Service', () => {
  const mockDocument: Document = {
    doc_id: 1,
    doc_name: 'Test Document',
    summary: 'Test summary',
    is_public: 'Y',
    created_date: '2024-01-01',
    previous: 0,
    department: {
      department_id: 1,
      department_name: 'IT Department',
    },
    category: {
      category_id: 1,
      category_name: 'ประกาศ',
    },
  }

  const mockDocuments: Document[] = [mockDocument]

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getDocument', () => {
    it('should fetch a document by id', async () => {
      const mockResponse = createAxiosResponse(mockDocument)
      mockedAxios.get.mockResolvedValue(mockResponse)

      const result = await documentService.getDocument(1)

      expect(mockedAxios.get).toHaveBeenCalledWith('/documents/1')
      expect(result).toEqual(mockResponse)
    })

    it('should handle error when fetching document', async () => {
      const mockError = new Error('Document not found')
      mockedAxios.get.mockRejectedValue(mockError)

      await expect(documentService.getDocument(999)).rejects.toThrow('Document not found')
    })
  })

  describe('getDocuments', () => {
    it('should fetch all documents', async () => {
      const mockResponse = createAxiosResponse(mockDocuments)
      mockedAxios.get.mockResolvedValue(mockResponse)

      const result = await documentService.getDocuments()

      expect(mockedAxios.get).toHaveBeenCalledWith('/documents')
      expect(result).toEqual(mockResponse)
    })
  })

  describe('deleteDoc', () => {
    it('should delete a document', async () => {
      const mockResponse = createAxiosResponse({ message: 'Document deleted' })
      mockedAxios.patch.mockResolvedValue(mockResponse)

      const result = await documentService.deleteDoc(mockDocument)

      expect(mockedAxios.patch).toHaveBeenCalledWith('/documents/delete/1', { is_public: 'Y' })
      expect(result).toEqual(mockResponse)
    })
  })

  describe('fetchDocPdf', () => {
    it('should fetch document PDF', async () => {
      const mockResponse = createAxiosResponse('PDF content')
      mockedAxios.get.mockResolvedValue(mockResponse)

      const result = await documentService.fetchDocPdf(1)

      expect(mockedAxios.get).toHaveBeenCalledWith('/documents/pdf/1')
      expect(result).toEqual(mockResponse)
    })
  })

  describe('addNewDoc', () => {
    it('should add a new document', async () => {
      const mockResponse = createAxiosResponse({ ...mockDocument, doc_id: 2 })
      mockedAxios.post.mockResolvedValue(mockResponse)

      const result = await documentService.addNewDoc(mockDocument)

      // Should exclude doc_id from the request data
      const expectedData = {
        doc_name: mockDocument.doc_name,
        summary: mockDocument.summary,
        is_public: mockDocument.is_public,
        created_date: mockDocument.created_date,
        previous: mockDocument.previous,
        department: mockDocument.department,
        category: mockDocument.category,
      }

      expect(mockedAxios.post).toHaveBeenCalledWith('/documents', expectedData)
      expect(result).toEqual(mockResponse)
    })
  })

  describe('updateDoc', () => {
    it('should update a document', async () => {
      const mockFormData = new FormData()
      const mockResponse = createAxiosResponse(mockDocument)
      mockedAxios.put.mockResolvedValue(mockResponse)

      const result = await documentService.updateDoc(mockDocument, mockFormData)

      // The actual service uses FormData and multipart/form-data
      expect(mockedAxios.put).toHaveBeenCalledWith('/documents/1', mockFormData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      })
      expect(result).toEqual(mockDocument)
    })
  })

  describe('saveFile', () => {
    it('should save file and return OCR results', async () => {
      const mockFormData = new FormData()
      const mockOcrResults: OcrResult[] = [{ page: 1, content: 'Page 1 content' }]
      const mockResponse = createAxiosResponse(mockOcrResults)
      mockedAxios.post.mockResolvedValue(mockResponse)

      const result = await documentService.saveFile(mockFormData)

      expect(mockedAxios.post).toHaveBeenCalledWith('/ocr/upload', mockFormData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      })
      expect(result).toEqual(mockOcrResults)
    })
  })

  describe('spellCheck', () => {
    it('should perform spell check', async () => {
      const mockOcrResults: OcrResult[] = [{ page: 1, content: 'Test content' }]
      const mockSpellcheckResults: Spellcheck_results[] = [
        {
          page: 1,
          content: 'Test content',
          wordlist: [{ word: 'teh', suggest: 'the' }],
        },
      ]
      const mockResponse = createAxiosResponse(mockSpellcheckResults)
      mockedAxios.post.mockResolvedValue(mockResponse)

      const result = await documentService.spellCheck(mockOcrResults)

      expect(mockedAxios.post).toHaveBeenCalledWith('/ocr/spellcheck', mockOcrResults)
      expect(result).toEqual(mockSpellcheckResults)
    })
  })

  describe('checkPrevious', () => {
    it('should check previous document', async () => {
      const mockPreviousResponse = { previous: 5 }
      const mockResponse = createAxiosResponse(mockPreviousResponse)
      mockedAxios.post.mockResolvedValue(mockResponse)

      const result = await documentService.checkPrevious('Test Document')

      expect(mockedAxios.post).toHaveBeenCalledWith('/document/checkPrevious', 'Test Document', {
        headers: { 'Content-Type': 'multipart/form-data' },
      })
      expect(result).toEqual(mockPreviousResponse)
    })
  })
})
