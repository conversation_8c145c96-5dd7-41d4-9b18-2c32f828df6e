<template>
  <q-dialog v-model="dialogOrPopupstore.isLoadingDialog" persistent>
    <q-card class="loading-dialog-card">
      <div class="loader-container">
        <div class="dual-ring"></div>
        <div class="loader-text">{{dialogOrPopupstore.loadingDialogtitle}}</div>
      </div>
    </q-card>
  </q-dialog>
</template>



<script setup lang="ts">

import { useDialogOrPopupstore } from 'src/stores/dialogOrPopup'

const dialogOrPopupstore = useDialogOrPopupstore()
</script>


<style scoped>
.loading-dialog-card {
  background: rgba(255, 255, 255, 0.6);
  padding: 40px 60px;
  border-radius: 20px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 220px;
  min-height: 220px;
}

.loader-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.dual-ring {
  display: inline-block;
  width: 60px;
  height: 60px;
  margin-bottom: 16px; /* ระยะห่างระหว่างวงกับข้อความ */
}

.dual-ring:after {
  content: " ";
  display: block;
  width: 48px;
  height: 48px;
  margin: auto;
  border-radius: 50%;
  border: 6px solid #3b82f6;
  border-color: #3b82f6 transparent #3b82f6 transparent;
  animation: dual-ring 1.2s linear infinite;
}

@keyframes dual-ring {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loader-text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  letter-spacing: 0.3px;
}
</style>
