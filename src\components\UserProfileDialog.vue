<template>
  <q-dialog v-model="dialogOrPopupStore.showUserProfiledialog" persistent>
    <q-card class="q-pa-md profile-dialog column no-wrap" style="position: relative;">

      <!-- Section: Avatar + Name -->
      <div class="column items-center q-gutter-y-sm q-py-md">
        <q-avatar size="100px" class="cursor-pointer" @click="triggerFileInput">
          <img :src="UpdatedUser.profile" alt="Profile Picture" />
        </q-avatar>
        <input
          type="file"
          ref="fileInput"
          accept=".png, .jpg"
          @change="onFileSelected"
          style="display: none;"
        />
        <div class="text-h6 text-weight-bold text-red" style="cursor: pointer; font-size: 12px;" @click="changeProfileToDefaultData"><strong>Change Profile to Default</strong></div>
        <div class="text-h6 text-weight-bold">{{ fullNameStatic }}</div>
        <div class="text-subtitle2 text-grey">{{ emailStatic }}</div>
      </div>

      <!-- Section: Form -->
      <q-form ref="userForm" class="q-gutter-y-sm col scroll" @submit.prevent>
        <!-- <q-input
          v-model="UpdatedUser.username"
          label="Name"
          outlined
          dense
          class="q-ma-md"
          :rules="[val => !!val || 'Required', checkDuplicateUsername]"
          hide-bottom-space
        /> -->
        <q-input
          v-model="UpdatedUser.full_name"
          label="Full Name"
          outlined
          dense
          class="q-ma-md"
          :rules="[val => !!val || 'Required', checkDuplicateFullName]"
          hide-bottom-space
        />
        <!-- <q-input
          v-model="UpdatedUser.password"
          label="Password"
          type="password"
          outlined
          dense
          class="q-ma-md"
          :rules="[val => !!val || 'Required', checkPasswordLength]"
        /> -->
        <q-input
          v-model="UpdatedUser.email"
          label="Email"
          outlined
          dense
          class="q-ma-md"
          :rules="[val => !!val || 'Required', checkDuplicateEmail]"
          hide-bottom-space
        />
        <!-- <q-select
          v-model="UpdatedUser.role.role_id"
          label="Role"
          outlined
          dense
          class="q-ma-md"
          :options="roleStore.roles"
          option-label="role_name"
          option-value="role_id"
          emit-value
          map-options
        />
        <q-select
          v-model="UpdatedUser.department.department_id"
          label="Department"
          outlined
          dense
          class="q-ma-md"
          :options="departmentStore.departments"
          option-label="department_name"
          option-value="department_id"
          emit-value
          map-options
        /> -->
      </q-form>


      <!-- Section: Buttons -->
      <div class="row justify-between q-gutter-sm q-mt-md q-pa-md">
        <q-btn label="Cancel" outline class="col" color="primary" @click="onCancel" />
        <q-btn label="Update" class="col" color="primary" @click="onSave" :disable="!isFormValid"/>
      </div>
      <div v-if="dialogOrPopupStore.showCatCoffeeGifWaiting" class="gif-overlay">
        <GifWaiting />
      </div>
    </q-card>

  </q-dialog>

</template>


<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { useDialogOrPopupstore } from 'src/stores/dialogOrPopup'
import { useUserStore } from 'src/stores/user'
import { useDepartmentstore } from 'src/stores/department'
import { useRolestore } from 'src/stores/role'
import type { User } from 'src/types/User'
import GifWaiting from './GifWaiting.vue'

const userStore = useUserStore()
const departmentStore = useDepartmentstore()
const roleStore = useRolestore()
const dialogOrPopupStore = useDialogOrPopupstore()

// const duplicateUsername = ref(false)
const duplicateFullName = ref(false)
const duplicateEmail = ref(false)
const userForm = ref()

const updatedUserData = ref<FormData>(new FormData())
const fileInput = ref<HTMLInputElement | null>(null)
const fullNameStatic = userStore.currentUser? userStore.currentUser.full_name:'John Doe'
const emailStatic = userStore.currentUser? userStore.currentUser.email:'<EMAIL>'
const file = ref<File | undefined>(undefined)

const UpdatedUser = ref<User>(JSON.parse(JSON.stringify(userStore.currentUser)))
const defaultUserImage = userStore.currentUser? userStore.currentUser.profile:'https://cdn.quasar.dev/img/avatar.png'
const isFormValid = ref(false)

onMounted(async ()=>{
  await roleStore.getRoles()
  await departmentStore.getDepartments()
})


watch(
  () => UpdatedUser.value,
  async () => {
    await checkFormValidity()
  },
  { deep: true }
)

async function checkFormValidity() {
  if (userForm.value) {
    const result = await userForm.value.validate()
    isFormValid.value = result
  }
}


// function checkDuplicateUsername(val: string) {
//   const conflict = userStore.users.find(
//     (u) => u.username === val && u.user_id !== userStore.currentUser?.user_id
//   )
//   duplicateUsername.value = !!conflict
//   return !conflict || 'Username already exists'
// }

function checkDuplicateFullName(val: string) {
  const conflict = userStore.users.find(
    (u) => u.full_name === val && u.user_id !== userStore.currentUser?.user_id
  )
  duplicateFullName.value = !!conflict
  return !conflict || 'Full name already exists'
}

function checkDuplicateEmail(val: string) {
  const conflict = userStore.users.find(
    (u) => u.email === val && u.user_id !== userStore.currentUser?.user_id
  )
  duplicateEmail.value = !!conflict
  return !conflict || 'Email already exists'
}

// function checkPasswordLength(val: string) {
//   return val.length >= 4 && val.length <= 8 || 'Password must be 4–8 characters'
// }


function onCancel() {
  dialogOrPopupStore.showUserProfiledialog = false
  userForm.value?.resetValidation()
}

function triggerFileInput() {
  fileInput.value?.click()
}

function onFileSelected(event: Event) {
  const target = event.target as HTMLInputElement
  const fileTarget = target.files?.[0]
  file.value = fileTarget as unknown as File
  if (fileTarget) {
    UpdatedUser.value.profile = URL.createObjectURL(file.value)
  }
}

async function onSave() {
  const isValid = await userForm.value?.validate()
  if (!isValid) {
    console.warn("Form validation failed")
    return
  }
  console.log(isValid)
  console.log("UpdatedUser: ",UpdatedUser.value)
  console.log("currentUser: ",userStore.currentUser)
  if(userStore.currentUser){
    updatedUserData.value = new FormData()
    if(userStore.currentUser.username!=UpdatedUser.value.username){
      updatedUserData.value.append('username',UpdatedUser.value.username)
    }
    if(userStore.currentUser.password!=UpdatedUser.value.password){
      updatedUserData.value.append('password',UpdatedUser.value.password)
    }
    if(userStore.currentUser.full_name!=UpdatedUser.value.full_name){
      updatedUserData.value.append('full_name',UpdatedUser.value.full_name)
    }
    if(userStore.currentUser.email!=UpdatedUser.value.email){
      updatedUserData.value.append('email',UpdatedUser.value.email)
    }
    if(userStore.currentUser.role.role_id!=UpdatedUser.value.role.role_id){
      updatedUserData.value.append('role_id',String(UpdatedUser.value.role.role_id))
    }
    if(userStore.currentUser.department.department_id!=UpdatedUser.value.department.department_id){
      updatedUserData.value.append('department_id',String(UpdatedUser.value.department.department_id))
    }
    if(file.value){
          updatedUserData.value.append('profile_file',file.value)
    }
    for (const pair of updatedUserData.value.entries()) {
      console.log(`${pair[0]}:`, pair[1])
    }
    try{
      dialogOrPopupStore.showCatCoffeeGifWaiting = true
      await userStore.updateUser(userStore.currentUser.user_id,updatedUserData.value)
      alert("อัปเดตข้อมูลผู้ใช้สำเร็จ!")
      await refreshUser()
      dialogOrPopupStore.showUserProfiledialog = false
      dialogOrPopupStore.showCatCoffeeGifWaiting = false
    }catch(e){
      console.error(e)
      alert("เกิดข้อผิดพลาดในการอัปเดตข้อมูลผู้ใช้ส่วนตัว")
    }
  }
}

function changeProfileToDefaultData(){
  UpdatedUser.value.profile = defaultUserImage
}

async function refreshUser(){
  await userStore.getUsers()
  userStore.currentUser = userStore.users.find(u=>u.user_id==userStore.currentUser?.user_id)
}
</script>

<style scoped>
.profile-dialog {
  width: 400px;
  max-width: 90vw;
  height: 600px;
  max-height: 90vh;
  border-radius: 20px;
  background: rgba(255, 255, 255, 1); /* สีพื้นหลังโปร่งใส */
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37); /* เงาเบาๆ */
  backdrop-filter: blur(20px); /* เอฟเฟกต์เบลอ */
  -webkit-backdrop-filter: blur(20px); /* สำหรับ Safari */
  border: 1px solid rgba(255, 255, 255, 0.18); /* ขอบเบาๆ */
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

.q-btn{
  transition: transform 0.5s ease, box-shadow 0.3s ease, opacity 0.3s ease;
}
.q-btn:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.35);
}

.q-field__control {
  min-height: 42px; /* หรือเท่ากับ q-input ของ dense */
}

.gif-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999; /* ให้ซ้อนบนสุด */
  background-color: rgba(255, 255, 255, 0.7); /* พื้นหลังโปร่งแสง */
  display: flex;
  justify-content: center;
  align-items: center;
}

</style>
