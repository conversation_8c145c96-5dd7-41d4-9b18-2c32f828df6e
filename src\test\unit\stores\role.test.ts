import { describe, it, expect, vi, beforeEach } from 'vitest'
import { setActive<PERSON><PERSON>, createPinia } from 'pinia'
import { useRolestore } from 'src/stores/role'
import roleService from 'src/services/role'
import type { Role } from 'src/types/Role'
import { createAxiosResponse } from 'src/test/test-utils'

// Mock the role service
vi.mock('src/services/role')
const mockedRoleService = vi.mocked(roleService)

describe('Role Store', () => {
  let roleStore: ReturnType<typeof useRolestore>

  const mockRole: Role = {
    role_id: 1,
    role_name: 'Admin',
  }

  const mockRoles: Role[] = [
    mockRole,
    { role_id: 2, role_name: 'User' },
    { role_id: 3, role_name: 'Manager' },
    { role_id: 4, role_name: 'Guest' },
  ]

  beforeEach(() => {
    setActivePinia(createPinia())
    roleStore = useRolestore()
    vi.clearAllMocks()
  })

  describe('initial state', () => {
    it('should have correct initial state', () => {
      expect(roleStore.roles).toBeDefined()
      expect(Array.isArray(roleStore.roles)).toBe(true)
      expect(roleStore.roles).toHaveLength(0)

      expect(roleStore.initialrole).toBeDefined()
      expect(roleStore.initialrole.role_id).toBe(0)
      expect(roleStore.initialrole.role_name).toBe('')

      expect(roleStore.editedrole).toBeDefined()
      expect(roleStore.editedrole.role_id).toBe(0)
      expect(roleStore.editedrole.role_name).toBe('')
    })

    it('should validate initial role structure', () => {
      const { initialrole } = roleStore

      expect(typeof initialrole.role_id).toBe('number')
      expect(typeof initialrole.role_name).toBe('string')
    })

    it('should have editedrole as deep copy of initialrole', () => {
      expect(roleStore.editedrole).toEqual(roleStore.initialrole)
      expect(roleStore.editedrole).not.toBe(roleStore.initialrole)
    })
  })

  describe('getRole action', () => {
    it('should fetch role successfully', async () => {
      const mockResponse = createAxiosResponse(mockRole)
      mockedRoleService.getRole.mockResolvedValue(mockResponse)

      await roleStore.getRole(1)

      expect(mockedRoleService.getRole).toHaveBeenCalledWith(1)
      expect(mockedRoleService.getRole).toHaveBeenCalledTimes(1)
    })

    it('should handle different role IDs', async () => {
      const mockResponse = createAxiosResponse(mockRoles[1])
      mockedRoleService.getRole.mockResolvedValue(mockResponse)

      await roleStore.getRole(2)

      expect(mockedRoleService.getRole).toHaveBeenCalledWith(2)
    })

    it('should handle error when fetching role', async () => {
      const mockError = new Error('Role not found')
      mockedRoleService.getRole.mockRejectedValue(mockError)

      await expect(roleStore.getRole(999)).rejects.toThrow('Role not found')
      expect(mockedRoleService.getRole).toHaveBeenCalledWith(999)
    })
  })

  describe('getRoles action', () => {
    it('should fetch all roles successfully', async () => {
      const mockResponse = createAxiosResponse(mockRoles)
      mockedRoleService.getRoles.mockResolvedValue(mockResponse)

      await roleStore.getRoles()

      expect(mockedRoleService.getRoles).toHaveBeenCalledTimes(1)
      expect(roleStore.roles).toEqual(mockRoles)
    })

    it('should handle error when fetching roles', async () => {
      const mockError = new Error('Failed to fetch roles')
      mockedRoleService.getRoles.mockRejectedValue(mockError)

      // Mock console.error to avoid error output in tests
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      await roleStore.getRoles()

      expect(mockedRoleService.getRoles).toHaveBeenCalledTimes(1)
      expect(consoleSpy).toHaveBeenCalledWith('Error fetching user:', mockError)

      consoleSpy.mockRestore()
    })

    it('should handle empty roles response', async () => {
      const mockResponse = createAxiosResponse([])
      mockedRoleService.getRoles.mockResolvedValue(mockResponse)

      await roleStore.getRoles()

      expect(roleStore.roles).toEqual([])
      expect(roleStore.roles).toHaveLength(0)
    })
  })

  describe('store structure', () => {
    it('should export correct properties and methods', () => {
      expect(roleStore).toHaveProperty('roles')
      expect(roleStore).toHaveProperty('initialrole')
      expect(roleStore).toHaveProperty('editedrole')
      expect(roleStore).toHaveProperty('getRole')
      expect(roleStore).toHaveProperty('getRoles')

      expect(typeof roleStore.getRole).toBe('function')
      expect(typeof roleStore.getRoles).toBe('function')
    })

    it('should have reactive state', () => {
      expect(roleStore.roles).toBeDefined()
      expect(roleStore.initialrole).toBeDefined()
      expect(roleStore.editedrole).toBeDefined()
    })
  })

  describe('state mutations', () => {
    it('should update roles when getRoles is called', async () => {
      const mockResponse = createAxiosResponse(mockRoles)
      mockedRoleService.getRoles.mockResolvedValue(mockResponse)

      expect(roleStore.roles).toHaveLength(0)

      await roleStore.getRoles()

      expect(roleStore.roles).toHaveLength(4)
      expect(roleStore.roles[0]?.role_name).toBe('Admin')
      expect(roleStore.roles[1]?.role_name).toBe('User')
    })

    it('should update editedrole when getRole is called', async () => {
      const mockResponse = createAxiosResponse(mockRole)
      mockedRoleService.getRole.mockResolvedValue(mockResponse)

      expect(roleStore.editedrole.role_id).toBe(0)

      await roleStore.getRole(1)

      expect(roleStore.editedrole).toEqual(mockRole)
    })
  })

  describe('data validation', () => {
    it('should maintain correct data types', async () => {
      const mockResponse = createAxiosResponse(mockRoles)
      mockedRoleService.getRoles.mockResolvedValue(mockResponse)

      await roleStore.getRoles()

      roleStore.roles.forEach((role) => {
        expect(typeof role.role_id).toBe('number')
        expect(typeof role.role_name).toBe('string')
      })
    })
  })
})
