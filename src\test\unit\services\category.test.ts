import { describe, it, expect, vi, beforeEach } from 'vitest'
import categoryService from 'src/services/category'
import type { Category } from 'src/types/Category'
import { createAxiosResponse } from 'src/test/test-utils'

// Mock axios
vi.mock('src/boot/axios', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    patch: vi.fn(),
  },
}))

// Import the mocked axios
import axios from 'src/boot/axios'
const mockedAxios = axios as any

describe('Category Service', () => {
  const mockCategory: Category = {
    category_id: 1,
    category_name: 'ประกาศ',
  }

  const mockCategories: Category[] = [
    mockCategory,
    {
      category_id: 2,
      category_name: 'ระเบียบ',
    },
    {
      category_id: 3,
      category_name: 'ข้อบังคับ',
    },
    {
      category_id: 4,
      category_name: 'คำสั่ง',
    },
  ]

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getCategory', () => {
    it('should fetch a category by id', async () => {
      const mockResponse = createAxiosResponse(mockCategory)
      mockedAxios.get.mockResolvedValue(mockResponse)

      const result = await categoryService.getCategory(1)

      expect(mockedAxios.get).toHaveBeenCalledWith('document-categories/1')
      expect(result).toEqual(mockResponse)
    })

    it('should handle error when fetching category', async () => {
      const mockError = new Error('Category not found')
      mockedAxios.get.mockRejectedValue(mockError)

      await expect(categoryService.getCategory(999)).rejects.toThrow('Category not found')
      expect(mockedAxios.get).toHaveBeenCalledWith('document-categories/999')
    })

    it('should fetch category with different ids', async () => {
      const mockResponse = createAxiosResponse(mockCategories[1])
      mockedAxios.get.mockResolvedValue(mockResponse)

      const result = await categoryService.getCategory(2)

      expect(mockedAxios.get).toHaveBeenCalledWith('document-categories/2')
      expect(result).toEqual(mockResponse)
    })
  })

  describe('getCategories', () => {
    it('should fetch all categories', async () => {
      const mockResponse = createAxiosResponse(mockCategories)
      mockedAxios.get.mockResolvedValue(mockResponse)

      const result = await categoryService.getCategories()

      expect(mockedAxios.get).toHaveBeenCalledWith('document-categories')
      expect(result).toEqual(mockResponse)
      expect(result.data).toHaveLength(4)
    })

    it('should handle error when fetching categories', async () => {
      const mockError = new Error('Failed to fetch categories')
      mockedAxios.get.mockRejectedValue(mockError)

      await expect(categoryService.getCategories()).rejects.toThrow('Failed to fetch categories')
      expect(mockedAxios.get).toHaveBeenCalledWith('document-categories')
    })

    it('should handle empty categories response', async () => {
      const mockResponse = createAxiosResponse([])
      mockedAxios.get.mockResolvedValue(mockResponse)

      const result = await categoryService.getCategories()

      expect(mockedAxios.get).toHaveBeenCalledWith('document-categories')
      expect(result.data).toHaveLength(0)
    })
  })

  describe('service structure', () => {
    it('should export correct methods', () => {
      expect(categoryService).toHaveProperty('getCategory')
      expect(categoryService).toHaveProperty('getCategories')
      expect(typeof categoryService.getCategory).toBe('function')
      expect(typeof categoryService.getCategories).toBe('function')
    })
  })
})
