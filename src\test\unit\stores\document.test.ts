import { describe, it, expect, vi, beforeEach } from 'vitest'
import { setActiveP<PERSON>, createPinia } from 'pinia'
import { useDocumentstore } from 'src/stores/document'
import documentService from 'src/services/document'
import type { Document, OcrResult, Spellcheck_results } from 'src/types/Document'
import { createAxiosResponse } from 'src/test/test-utils'

// Mock the document service
vi.mock('src/services/document')
const mockedDocumentService = vi.mocked(documentService)

// Mock other stores
vi.mock('src/stores/dialogOrPopup', () => ({
  useDialogOrPopupstore: () => ({
    // mock dialog store methods if needed
  }),
}))

vi.mock('src/stores/accessPermmission', () => ({
  useAccessPermissionstore: () => ({
    // mock access permission store methods if needed
  }),
}))

describe('Document Store', () => {
  let documentStore: ReturnType<typeof useDocumentstore>

  const mockDocument: Document = {
    doc_id: 1,
    doc_name: 'Test Document',
    summary: 'Test summary',
    is_public: 'Y',
    created_date: '2024-01-01',
    previous: 0,
    department: {
      department_id: 1,
      department_name: 'IT Department',
    },
    category: {
      category_id: 1,
      category_name: 'ประกาศ',
    },
  }

  const mockOcrResults: OcrResult[] = [
    { page: 1, content: 'Page 1 content' },
    { page: 2, content: 'Page 2 content' },
  ]

  const mockSpellcheckResults: Spellcheck_results[] = [
    {
      page: 1,
      content: 'Test content with teh error',
      wordlist: [{ word: 'teh', suggest: 'the' }],
    },
  ]

  beforeEach(() => {
    setActivePinia(createPinia())
    documentStore = useDocumentstore()
    vi.clearAllMocks()
  })

  describe('initial state', () => {
    it('should have correct initial state', () => {
      expect(documentStore.documents).toBeDefined()
      expect(Array.isArray(documentStore.documents)).toBe(true)
      expect(documentStore.documents).toHaveLength(0)

      expect(documentStore.initialdocument).toBeDefined()
      expect(documentStore.initialdocument.doc_id).toBe(0)
      expect(documentStore.initialdocument.doc_name).toBe('')
      expect(documentStore.initialdocument.summary).toBe('')
      expect(documentStore.initialdocument.is_public).toBe('')
      expect(documentStore.initialdocument.created_date).toBe('')
      expect(documentStore.initialdocument.previous).toBe(0)
    })

    it('should have correct initial nested objects', () => {
      expect(documentStore.initialdocument.department.department_id).toBe(0)
      expect(documentStore.initialdocument.department.department_name).toBe('')
      expect(documentStore.initialdocument.category.category_id).toBe(0)
      expect(documentStore.initialdocument.category.category_name).toBe('')
    })

    it('should have initial file-related state', () => {
      expect(documentStore.filePreview).toBe('')
      expect(documentStore.file).toBeUndefined()
      expect(documentStore.fileInput).toBeNull()
      expect(documentStore.showTable).toBe(false)
    })

    it('should have initial OCR and spellcheck state', () => {
      expect(Array.isArray(documentStore.ocrResults)).toBe(true)
      expect(documentStore.ocrResults).toHaveLength(0)
      expect(Array.isArray(documentStore.wrongWords)).toBe(true)
      expect(documentStore.wrongWords).toHaveLength(0)
    })
  })

  describe('getDocument action', () => {
    it('should fetch document successfully', async () => {
      const mockResponse = createAxiosResponse(mockDocument)
      mockedDocumentService.getDocument.mockResolvedValue(mockResponse)

      await documentStore.getDocument(1)

      expect(mockedDocumentService.getDocument).toHaveBeenCalledWith(1)
      expect(mockedDocumentService.getDocument).toHaveBeenCalledTimes(1)
    })

    it('should handle different document IDs', async () => {
      const mockResponse = createAxiosResponse({ ...mockDocument, doc_id: 5 })
      mockedDocumentService.getDocument.mockResolvedValue(mockResponse)

      await documentStore.getDocument(5)

      expect(mockedDocumentService.getDocument).toHaveBeenCalledWith(5)
    })
  })

  describe('getDocuments action', () => {
    it('should fetch all documents successfully', async () => {
      const mockDocuments = [mockDocument]
      const mockResponse = createAxiosResponse(mockDocuments)
      mockedDocumentService.getDocuments.mockResolvedValue(mockResponse)

      await documentStore.getDocuments()

      expect(mockedDocumentService.getDocuments).toHaveBeenCalledTimes(1)
    })
  })

  describe('saveDoc action', () => {
    it('should save document successfully', async () => {
      const mockFormData = new FormData()
      const mockResponse = createAxiosResponse({ message: 'Document saved' })
      mockedDocumentService.saveDoc.mockResolvedValue(mockResponse)

      await documentStore.saveDoc(mockFormData)

      expect(mockedDocumentService.saveDoc).toHaveBeenCalledWith(mockFormData)
    })
  })

  describe('deleteDoc action', () => {
    it('should delete document successfully', async () => {
      const mockFormData = new FormData()
      const mockResponse = createAxiosResponse({ message: 'Document deleted' })
      mockedDocumentService.updateDoc.mockResolvedValue(mockResponse)
      mockedDocumentService.getDocuments.mockResolvedValue(createAxiosResponse([]))

      await documentStore.deleteDoc(mockDocument, mockFormData)

      expect(mockedDocumentService.updateDoc).toHaveBeenCalledWith(mockDocument, mockFormData)
      expect(mockedDocumentService.getDocuments).toHaveBeenCalled()
    })
  })

  describe('saveFile action', () => {
    it('should save file and return OCR results', async () => {
      const mockFormData = new FormData()
      mockedDocumentService.saveFile.mockResolvedValue(mockOcrResults)

      const result = await documentStore.saveFile(mockFormData)

      expect(mockedDocumentService.saveFile).toHaveBeenCalledWith(mockFormData)
      expect(result).toEqual(mockOcrResults)
    })
  })

  describe('spellCheck action', () => {
    it('should perform spell check and return results', async () => {
      mockedDocumentService.spellCheck.mockResolvedValue(mockSpellcheckResults)

      const result = await documentStore.spellCheck(mockOcrResults)

      expect(mockedDocumentService.spellCheck).toHaveBeenCalledWith(mockOcrResults)
      expect(result).toEqual(mockSpellcheckResults)
    })
  })

  describe('checkPrevious action', () => {
    it('should check previous document', async () => {
      const mockResponse = { previous: 5 }
      mockedDocumentService.checkPrevious.mockResolvedValue(mockResponse)

      const result = await documentStore.checkPrevious('Test Document')

      expect(mockedDocumentService.checkPrevious).toHaveBeenCalledWith('Test Document')
      expect(result).toEqual(mockResponse)
    })
  })

  describe('findDocId action', () => {
    it('should find document ID', async () => {
      const mockDocId = 123
      mockedDocumentService.findDocId.mockResolvedValue(mockDocId)

      const result = await documentStore.findDocId(mockDocument)

      expect(mockedDocumentService.findDocId).toHaveBeenCalledWith(mockDocument)
      expect(result).toBe(mockDocId)
    })
  })

  describe('resetFile action', () => {
    it('should reset file-related state', () => {
      // Set some values first
      documentStore.filePreview = 'test-preview'
      documentStore.file = new File(['test'], 'test.pdf')
      documentStore.showTable = true

      documentStore.resetFile()

      expect(documentStore.filePreview).toBe('')
      expect(documentStore.file).toBeUndefined()
      expect(documentStore.showTable).toBe(false)
    })
  })

  describe('store structure', () => {
    it('should export correct properties and methods', () => {
      const expectedProperties = [
        'documents',
        'initialdocument',
        'editeddocument',
        'tranferDocument',
        'pdfForm',
        'filePreview',
        'file',
        'fileInput',
        'showTable',
        'ocrResults',
        'wrongWords',
      ]

      const expectedMethods = [
        'getDocument',
        'getDocuments',
        'saveDoc',
        'deleteDoc',
        'saveFile',
        'spellCheck',
        'checkPrevious',
        'findDocId',
        'resetFile',
        'createdSummary',
        'getPdfDocument',
        'getContextByDocId',
        'getAccessPermissionByDocId',
      ]

      expectedProperties.forEach((prop) => {
        expect(documentStore).toHaveProperty(prop)
      })

      expectedMethods.forEach((method) => {
        expect(documentStore).toHaveProperty(method)
        expect(typeof (documentStore as any)[method]).toBe('function')
      })
    })
  })
})
