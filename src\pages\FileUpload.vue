<!-- eslint-disable @typescript-eslint/no-unused-vars -->
<template>
  <q-page-container>
    <!-- <q-page class="q-pa-sm">

      <div class="row no-wrap q-pa-none" style="height: 75%; overflow: hidden;">
        <div style="flex: 0 0 600px;">
          <q-card class="q-pa-none shadow-3 rounded-borders upload-card q-mt-md"
            style="background-image: url('/src/assets/img/Upload_bg.png'); background-size: cover; background-position: center;">

            <table style="width: 100%; border-collapse: collapse">
              <tbody>
                <tr>

                  <td style="
                  padding: 10px;
                  display: flex;
                  align-items: center;
                  gap: 10px;
                  white-space: nowrap;
                ">
                    <h4 class="custom-upload text-white text-bold font2 h3" style="margin: 0">
                      อัปโหลด
                    </h4>
                    <q-icon name="upload_file" size="35px" class="text-white" />
                  </td>


                  <td style="width: 100%; padding: 10px; text-align: right">
                    <div style="
                    display: flex;
                    gap: 10px;
                    justify-content: flex-end;
                    flex-wrap: wrap;
                    max-width: 100%;
                  ">
                      <q-btn label="สแกนไฟล์"
                        :color="documentStore.file ? (activeButton === 'scan' ? 'grey' : 'info') : 'info'"
                        class="scan-btn" :loading="isLoading" @click="scanFile">
                        <template v-slot:loading>
                          <q-spinner-puff color="white" size="20px" />
                        </template>
</q-btn>

<q-btn label="พิสูจน์ตัวอักษร" :color="activeButton === 'proofread' ? 'grey' : 'info'" @click="toggleClicked" />
<q-btn label="แก้ไขข้อมูลที่เกี่ยวข้อง" :color="activeButton === 'edit' ? 'grey' : 'info'" @click="showMetadata" />
</div>
</td>
</tr>
</tbody>
</table>


<div style="display: flex; justify-content: center; align-items: center; height: 500px;">
  <q-card flat class="q-mt-md" :style="{
                background: 'white',
                borderRadius: '12px',
                margin: '0px auto',
                width: '600px',
                maxWidth: '600px',
                height: documentStore.filePreview ? '500px' : '300px',
                overflow: 'hidden',
                position: 'relative'
              }">
    <div class="upload-box text-grey-7" @dragover.prevent @drop="handleDrop" style="
                width: 100%;
                height: 100%;
                padding: 5px;
                box-sizing: border-box;
                border-radius: 12px;
                overflow: hidden;
              ">

      <div v-if="!documentStore.filePreview" class="dashed-border flex column justify-center items-center" style="
                  width: 100%;
                  height: 100%;
                  border: 2px dashed #6c757d;
                  border-radius: 12px;
                  padding: 0;
                  box-sizing: border-box;
                  position: relative;
                ">
        <div class="q-mb-md">
          <q-icon name="cloud_upload" size="xl" color="blue-grey-6" />
          <p class="text-bold text-dark q-mt-sm">ลากไฟล์มาวางที่นี่</p>
          <p class="text-grey-6 text-caption">หรือ คลิกเพื่อเลือกไฟล์</p>
        </div>


        <div style="position: absolute; bottom: 15px; text-align: center;">
          <q-btn label="เลือกไฟล์" color="primary" glossy unelevated size="md" class="q-px-md upload-footer"
            @click="uploadfile()" />
          <p class="text-grey-6 text-caption q-mt-sm">
            รองรับไฟล์ <strong class="text-primary">JPG, PNG และ PDF</strong> ขนาดไม่เกิน
            <strong>10MB</strong>
          </p>
        </div>
      </div>


      <div v-else style="width: 100%; height: 100%; border-radius: 12px; overflow: hidden;">
        <div style="width: 100%; height: 100%; overflow: auto; border-radius: 12px;">
          <img v-if="isImage" :src="documentStore.filePreview"
            style="width: 100%; height: 100%; object-fit: contain; display: block; border-radius: 12px;" />
          <iframe v-if="isPDF" :src="documentStore.filePreview"
            style="width: 100%; height: 100%; border: none; display: block; border-radius: 12px;" />
          <p v-if="isDoc">ไฟล์ Word ไม่สามารถแสดงตัวอย่างได้</p>
        </div>
      </div>
    </div>
  </q-card>
</div>

<div v-if="documentStore.filePreview"
  style="display: flex; justify-content: center; align-items: center; margin-top: 15px;">
  <div style="text-align: center;">
    <q-btn label="อัปโหลด" color="primary" glossy unelevated size="md" class="q-px-md upload-footer"
      @click="uploadfile()" />
    <p class="text-white text-caption q-mt-sm">
      รองรับไฟล์ <strong class="text-yellow">DOCX, JPG, PNG และ PDF</strong> ขนาดไม่เกิน <strong
        class="text-red">10MB</strong>
    </p>
  </div>
</div>









<input type="file" ref="fileInputUi" @change="onFileChange" style="display: none" />
</q-card>
</div>

<div style="flex: 1; min-width: 800px;">
  <q-card class="q-pa-md shadow-3 upload-card2 q-mt-md"
    style="background-image: url('/src/assets/img/Uplaod_bg_2.png'); background-size: cover; background-position: center; border-radius: 0px; padding-top: 30px;">

    <table style="width: 100%; border-collapse: collapse; table-layout: fixed">
      <tbody>
        <tr>

          <td style="width: 350px; text-align: center; margin-right: 10px">
            <div class="q-pa-sm text-bold" style="
                    background-color: white;
                    border: 2px solid black;
                    border-radius: 0px;
                    width: 99%;
                    padding: 10px;
                    margin-top: -17px;
                  ">
              แสดงผลการ <strong>Extract</strong>
              <q-icon name="description" size="26px" class="q-mr-sm" style="margin-left: 5px" />
            </div>
          </td>


          <td style="width: 200px; text-align: center">
            <div class="q-pa-sm text-bold" style="
                    background-color: white;
                    border: 2px solid black;
                    border-radius: 0px;
                    width: 100%;
                    padding: 10px;
                    margin-top: -17px;
                  ">

              <span v-if="!showMeta">แสดงคำที่มีโอกาสผิด</span>
              <span v-else>Meta Data</span>
            </div>
          </td>
        </tr>
        <tr>
          <td colspan="2" style="height: 20px"></td>
        </tr>
        <tr style="width: 400px;
                  height: 550px;">


          <td style="
                  background-color: white;
                  padding: 0;
                  vertical-align: top;
                  width: 400px;
                  height: 550px;
                  border-right: 18px solid transparent;
                  border-radius: 0px;
                  position: relative;
                  overflow: hidden;
                ">
            <GifWaiting v-if="dialogOrPopupStore.showCatCoffeeGifWaiting" class="gif-overlay"
              style="height: 550px; padding: 10px;" />


            <div v-if="dialogOrPopupStore.showCatCoffeeGifWaiting === false" style="
                    overflow-y: auto;
                    height: 550px;
                    padding: 10px;
                  ">
              <div class="container text-left">
                <div v-for="(result, pageIndex) in documentStore.ocrResults" :key="result.page"
                  class="page q-mb-md shadow-1 q-pa-sm"
                  style="background-color: white; border-radius: 0px; border: 1px solid black">
                  <div class="page-header text-bold q-pa-xs flex justify-center items-center"
                    style="border-radius: 0px; font-size: 14px; color: gray">
                    หน้า {{ result.page }}
                  </div>

                  <div class="page-content" style="overflow-y: auto; max-height:90%; padding: 10px">
                    <p v-for="(paragraph, index) in paragraphs[pageIndex]" :key="index" contenteditable="true"
                      @input="updateParagraph(pageIndex, index, $event)" class="q-mb-sm"
                      v-html="highlightWrongWords(paragraph)"></p>
                  </div>
                </div>
              </div>

            </div>
          </td>


          <td style="
                  background-color: white;
                  padding: 15px;
                  vertical-align: top;
                  width: 400px;
                  height: 550px;
                  border-radius: 0px;
                ">
            <div style="max-height: 550px; padding: 10px">
              <div v-if="documentStore.showTable">
                <table style="
                        width: 100%;
                        table-layout: fixed;
                        margin-top: 10px;
                        border-collapse: collapse;
                      ">
                  <tbody>
                    <tr>
                      <td style="
                            width: 50%;
                            vertical-align: middle;
                            text-align: center;
                            padding: 8px;
                            word-wrap: break-word;
                          ">
                        <div style="
                              display: flex;
                              align-items: center;
                              justify-content: center;
                              gap: 5px;
                              white-space: nowrap;
                            ">
                          <div class="square bg-blue" style="
                                width: 14px;
                                height: 14px;
                                min-width: 14px;
                                min-height: 14px;
                                flex-shrink: 0;
                                background-color: blue;
                              "></div>
                          <span class="font1">คำที่สงสัยว่าจะผิด</span>
                        </div>
                      </td>

                      <td style="
                            width: 50%;
                            vertical-align: middle;
                            text-align: center;
                            padding: 8px;
                            word-wrap: break-word;
                          ">
                        <div style="
                              display: flex;
                              align-items: center;
                              justify-content: center;
                              gap: 5px;
                              white-space: nowrap;
                            ">
                          <div class="square bg-pink" style="width: 12px; height: 12px"></div>
                          <span class="font1">คำที่มักใช้ผิด</span>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>

                <div style="height: 100%; overflow: hidden">
                  <q-table :rows="documentStore.wrongWords" :columns="columns" row-key="word" dense bordered
                    virtual-scroll class="custom-table" style="max-height: calc(90vh - 200px); overflow-y: auto"
                    :pagination="{ rowsPerPage: 0 }">
                    <template v-slot:body-cell-word="props">
                              <q-td :props="props">
                                <div style="display: flex; align-items: center; gap: 5px;">
                                  <div :style="{
                                    width: '14px',
                                    height: '14px',
                                    borderRadius: '2px',
                                    backgroundColor: props.row.shouldBe && props.row.shouldBe.trim() !== '' ? 'blue' : '#e91e63'
                                  }"></div>
                                  <span>{{ props.row.word }}</span>
                                </div>
                              </q-td>
                            </template>

                  </q-table>

                </div>
              </div>
            </div>

            <div class="half-box">
              <div style="
                      overflow-y: auto;
                      height: 100%;
                      padding: 10px;
                    " v-if="documentStore.showTable">

              </div>


              <CancelUploadDialog v-model="dialogOrPopupStore.showCancelDialog"></CancelUploadDialog>


              <AcceptUploadDialog v-model="dialogOrPopupStore.showConfirmDialog"></AcceptUploadDialog>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </q-card>
</div>
</div>
</q-page> -->
  </q-page-container>
</template>

<script setup lang="ts"></script>

<style scoped>
.square {
  width: 20px;
  height: 20px;
}

.text-bold {
  font-weight: bold;
  font-size: 1.2rem;
}

:deep(.custom-table thead tr th) {
  background-color: black !important;
  color: white !important;
  font-weight: bold;
  text-align: center;
}

.result-box {
  flex: 1;
  padding: 16px;
  text-align: center;
  background-color: #fff;
}

.content-box {
  min-height: 200px;
  background-color: #fafafa;
  padding: 16px;
  margin-top: 8px;
  border-radius: 6px;
  text-align: left;
}

.h3 {
  margin-top: -10px;
}

.Extract {
  width: 100%;
  min-width: 150px;
  height: auto;
}

.upload-card {
  width: auto;
  height: auto;
  text-align: center;
  margin-right: -50px;
  flex-grow: 1;
  min-height: calc(105vh - 130px);
  margin-top: 0.3rem;
}

.upload-card2 {
  width: auto;
  height: auto;
  text-align: center;
  margin-left: 70px;
  flex-grow: 1.3;
  min-height: calc(105vh - 130px);
  margin-top: 0.3rem;
  height: auto;
}

.boorder {
  color: #e0e0e0;
}

.dashed-border {
  border: 2px dashed #6c757d;
  border-radius: 12px;
  padding: 1rem;
  width: 100%;
  min-height: unset;
  /* ✅ ลบความสูงขั้นต่ำ */
  height: auto;
  /* ✅ ให้สูงตามเนื้อหา */
  text-align: center;
}


.upload-box {
  position: relative;
  width: 100%;
  max-width: 600px;
  min-height: auto;
  /* ลบความสูงขั้นต่ำ */
  height: auto;
  /* ให้สูงพอดีกับเนื้อหา */
  text-align: center;

  /* ลบ border ที่ไม่ใช้ */
  border: none;

  /* ปรับ margin/padding */


  /* จัด layout ภายใน */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  background-color: white;
}


.preview-image {
  max-width: 100%;
  width: 800px;
  height: 500px;
  min-height: calc(100vh - 280px);
  /* ปรับความสูงให้พอดี */
  object-fit: contain;
  /* ป้องกันภาพแตก */
  display: block;
  /* ป้องกัน margin ล่องลอย */
  margin-top: 0;
  /* เอาขอบบนออก */
  align-self: flex-start;
  /* ถ้าใช้ flexbox */
  margin-bottom: 0;
  margin: 0;
  /* ลบ margin ออกให้หมด */
  padding: 0;
  /* ลบ padding ออกให้หมด */
  border: none;
  /* เอาขอบออก */
  border: 1px solid #000000;
}

.upload-footer {
  width: 40%;
  padding: 0.5rem 0;
  text-align: center;
  margin-top: 3px;
}

.full-height {
  height: 100%;
}

.border-right {
  border-right: 2px solid #ccc;
  height: 100%;
}

.flex {
  display: flex;
  align-items: center;
  justify-content: center;
}

.full-height {
  height: 100vh;
}

.input {
  background-color: #e0e0e0;
  border: 1px solid black;
  border-radius: 4px;
}

.font1 {
  font-size: 14px;
}

.font2 {
  font-size: 25px;
  font-weight: bold;
  margin-top: 15px;
}

.container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow-y: auto;
}

.page {
  width: 100%;
  max-width: 21cm;
  height: 29.7cm;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid #ccc;
  background-color: white;
  overflow-y: auto;
  max-height: 575px;
  /* กำหนดความสูงและเพิ่ม scroll เมื่อเนื้อหาเกิน */
}

.page-content {
  overflow-y: auto;
  max-height: 350px;
  /* ให้สามารถเลื่อนดูเฉพาะเนื้อหาภายใน */
}

.box {
  width: 30vw;
  /* กว้าง 30% ของหน้าจอ */
  height: 30vw;
  /* ทำให้เป็นสี่เหลี่ยม */
}

.text {
  font-size: 14px;
  text-align: left;
}

.meta-input {
  width: 100%;
  /* ใช้เต็มพื้นที่ */
  max-width: 300px;
  /* แต่ไม่เกิน 240px */
  min-width: 180px;
  /* ไม่ให้เล็กเกินไป */
  height: 35px;
  /* ขนาดมาตรฐาน */
  padding: 5px;
  box-sizing: border-box;
  /* ✅ ป้องกัน padding ทำให้ขนาดเกิน */
}

/* ปุ่ม */
.action-btn {
  min-width: 100px;
  /* ป้องกันปุ่มเล็กเกินไป */
  max-width: 150px;
  /* จำกัดขนาดปุ่ม */
  flex-shrink: 0;
  /* ❌ ป้องกันปุ่มหดตัว */
}

.button-container {
  display: flex;
  justify-content: center;
  /* จัดให้อยู่ตรงกลาง */
  align-items: center;
  /* จัดให้อยู่แนวเดียวกัน */
  gap: 30px;
  /* ระยะห่างระหว่างปุ่ม */
  flex-wrap: nowrap;
  /* ❌ ป้องกันปุ่มตกบรรทัด */
  margin-top: 50px;
}

.button-cancel {
  display: flex;
  justify-content: center;
  /* จัดให้อยู่ตรงกลาง */
  align-items: center;
  /* จัดให้อยู่แนวเดียวกัน */
  gap: 20px;
  /* ระยะห่างระหว่างปุ่ม */
  flex-wrap: nowrap;
  /* ❌ ป้องกันปุ่มตกบรรทัด */
}

.full-page-row {
  display: flex;
  height: calc(100vh - 60px);
  /* ลบ header ถ้ามี */
  overflow: hidden;
}

.left-panel {
  width: 40%;
  max-width: 640px;
  background: #002f7d;
  display: flex;
  justify-content: center;
  align-items: center;
}

.upload-box {
  width: 90%;
  max-width: 500px;
  height: calc(500 / 1080 * 100vh);
  /* 500px เมื่อจอสูง 1080px */
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-sizing: border-box;
}

.right-panel {
  width: 60%;
  background: linear-gradient(to bottom, #f0f0f0, #bdbdbd);
  display: flex;
  flex-direction: column;
}

.right-header {
  display: flex;
  height: 50px;
  background: white;
  border: 1px solid #555;
  font-weight: bold;
  align-items: center;
  text-align: center;
}

.half-title {
  width: 50%;
  border-right: 1px solid #555;
  padding: 10px;
}

.right-content {
  display: flex;
  flex: 1;
}

.half-box {
  width: 50%;
  background: white;
  border: 1px solid #555;
  margin: 5px;
  overflow-y: auto;
}
</style>
