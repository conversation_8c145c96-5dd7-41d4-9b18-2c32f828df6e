import { describe, it, expect, vi, beforeEach } from 'vitest'
import accessPermissionService from 'src/services/accessPermission'
import type { AccessPermission } from 'src/types/AccessPermission'
import { createAxiosResponse } from 'src/test/test-utils'

// Mock axios
vi.mock('src/boot/axios', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    patch: vi.fn(),
  },
}))

// Import the mocked axios
import axios from 'src/boot/axios'
const mockedAxios = axios as any

describe('AccessPermission Service', () => {
  const mockAccessPermission: AccessPermission = {
    premission_id: 1,
    doc_id: 1,
    roles: [
      { role_id: 1, role_name: 'Admin' },
      { role_id: 2, role_name: 'User' },
    ],
  }

  const mockAccessPermissions: AccessPermission[] = [
    mockAccessPermission,
    {
      premission_id: 2,
      doc_id: 2,
      roles: [{ role_id: 1, role_name: 'Admin' }],
    },
  ]

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getAccessPermission', () => {
    it('should fetch an access permission by id', async () => {
      const mockResponse = { data: mockAccessPermission }
      mockedAxios.get.mockResolvedValue(mockResponse)

      const result = await accessPermissionService.getAccessPermission(1)

      expect(mockedAxios.get).toHaveBeenCalledWith('/access-permmission/1')
      expect(result).toEqual(mockResponse)
    })

    it('should handle error when fetching access permission', async () => {
      const mockError = new Error('Access permission not found')
      mockedAxios.get.mockRejectedValue(mockError)

      await expect(accessPermissionService.getAccessPermission(999)).rejects.toThrow(
        'Access permission not found',
      )
      expect(mockedAxios.get).toHaveBeenCalledWith('/access-permmission/999')
    })
  })

  describe('getAccessPermissiones', () => {
    it('should fetch all access permissions', async () => {
      const mockResponse = { data: mockAccessPermissions }
      mockedAxios.get.mockResolvedValue(mockResponse)

      const result = await accessPermissionService.getAccessPermissiones()

      expect(mockedAxios.get).toHaveBeenCalledWith('/access-permmissions')
      expect(result).toEqual(mockResponse)
      expect(result.data).toHaveLength(2)
    })

    it('should handle error when fetching access permissions', async () => {
      const mockError = new Error('Failed to fetch access permissions')
      mockedAxios.get.mockRejectedValue(mockError)

      await expect(accessPermissionService.getAccessPermissiones()).rejects.toThrow(
        'Failed to fetch access permissions',
      )
    })
  })

  describe('createAccessPermByDocIdandRoleId', () => {
    it('should create access permission', async () => {
      const mockResponse = { data: { ...mockAccessPermission, premission_id: 3 } }
      mockedAxios.post.mockResolvedValue(mockResponse)

      const result =
        await accessPermissionService.createAccessPermByDocIdandRoleId(mockAccessPermission)

      // Should exclude premission_id from the request data
      const expectedData = {
        doc_id: mockAccessPermission.doc_id,
        roles: mockAccessPermission.roles,
      }

      expect(mockedAxios.post).toHaveBeenCalledWith('access-permmission', expectedData)
      expect(result).toEqual(mockResponse)
    })

    it('should handle error when creating access permission', async () => {
      const mockError = new Error('Failed to create access permission')
      mockedAxios.post.mockRejectedValue(mockError)

      await expect(
        accessPermissionService.createAccessPermByDocIdandRoleId(mockAccessPermission),
      ).rejects.toThrow('Failed to create access permission')
    })
  })

  describe('createAccessPermsByDocIdandRoleId', () => {
    it('should create multiple access permissions', async () => {
      const mockResponse = { data: { message: 'Access permissions created' } }
      mockedAxios.post.mockResolvedValue(mockResponse)

      const result =
        await accessPermissionService.createAccessPermsByDocIdandRoleId(mockAccessPermission)

      const expectedData = {
        doc_id: mockAccessPermission.doc_id,
        roles: mockAccessPermission.roles,
      }

      expect(mockedAxios.post).toHaveBeenCalledWith('access-permmission', expectedData)
      expect(result).toEqual(mockResponse)
    })
  })

  describe('updateAccessPermByDocIdandRoleId', () => {
    it('should update access permission', async () => {
      const mockResponse = { data: mockAccessPermission }
      mockedAxios.post.mockResolvedValue(mockResponse)

      const result =
        await accessPermissionService.updateAccessPermByDocIdandRoleId(mockAccessPermission)

      const expectedData = {
        doc_id: mockAccessPermission.doc_id,
        roles: mockAccessPermission.roles,
      }

      expect(mockedAxios.post).toHaveBeenCalledWith('access-permmission/1', expectedData)
      expect(result).toEqual(mockResponse)
    })

    it('should handle error when updating access permission', async () => {
      const mockError = new Error('Failed to update access permission')
      mockedAxios.post.mockRejectedValue(mockError)

      await expect(
        accessPermissionService.updateAccessPermByDocIdandRoleId(mockAccessPermission),
      ).rejects.toThrow('Failed to update access permission')
    })
  })

  describe('service structure', () => {
    it('should export correct methods', () => {
      expect(accessPermissionService).toHaveProperty('getAccessPermission')
      expect(accessPermissionService).toHaveProperty('getAccessPermissiones')
      expect(accessPermissionService).toHaveProperty('createAccessPermByDocIdandRoleId')
      expect(accessPermissionService).toHaveProperty('createAccessPermsByDocIdandRoleId')
      expect(accessPermissionService).toHaveProperty('updateAccessPermByDocIdandRoleId')

      expect(typeof accessPermissionService.getAccessPermission).toBe('function')
      expect(typeof accessPermissionService.getAccessPermissiones).toBe('function')
      expect(typeof accessPermissionService.createAccessPermByDocIdandRoleId).toBe('function')
      expect(typeof accessPermissionService.createAccessPermsByDocIdandRoleId).toBe('function')
      expect(typeof accessPermissionService.updateAccessPermByDocIdandRoleId).toBe('function')
    })
  })
})
