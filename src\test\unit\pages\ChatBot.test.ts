import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { Quasar } from 'quasar'
import ChatBot from '../../../pages/ChatBot.vue'

// Mock axios
vi.mock('axios', () => ({
  default: {
    post: vi.fn(() => Promise.resolve({
      data: {
        answer: 'Test response',
        ref: []
      }
    }))
  }
}))

describe('ChatBot.vue', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('renders correctly when showChatPage is true', () => {
    const wrapper = mount(ChatBot, {
      global: {
        plugins: [Quasar],
        stubs: {
          Teleport: true
        }
      }
    })

    expect(wrapper.exists()).toBe(true)
  })

  it('has initial welcome message', () => {
    const wrapper = mount(ChatBot, {
      global: {
        plugins: [Quasar],
        stubs: {
          Teleport: true
        }
      }
    })

    // Check if the component has the initial message
    const vm = wrapper.vm as any
    expect(vm.messages).toHaveLength(1)
    expect(vm.messages[0].role).toBe('bot')
    expect(vm.messages[0].content).toContain('สวัสดีครับ')
  })

  it('has quick suggestions', () => {
    const wrapper = mount(ChatBot, {
      global: {
        plugins: [Quasar],
        stubs: {
          Teleport: true
        }
      }
    })

    const vm = wrapper.vm as any
    expect(vm.quickSuggestions).toHaveLength(4)
    expect(vm.quickSuggestions[0].label).toBe('วิธีค้นหาเอกสาร')
  })

  it('can format time correctly', () => {
    const wrapper = mount(ChatBot, {
      global: {
        plugins: [Quasar],
        stubs: {
          Teleport: true
        }
      }
    })

    const vm = wrapper.vm as any
    const testDate = new Date('2024-01-01T10:30:00')
    const formattedTime = vm.formatTime(testDate)
    expect(formattedTime).toMatch(/\d{2}:\d{2}/)
  })

  it('can format file size correctly', () => {
    const wrapper = mount(ChatBot, {
      global: {
        plugins: [Quasar],
        stubs: {
          Teleport: true
        }
      }
    })

    const vm = wrapper.vm as any
    expect(vm.formatFileSize(1024)).toBe('1 KB')
    expect(vm.formatFileSize(1048576)).toBe('1 MB')
    expect(vm.formatFileSize(0)).toBe('0 Bytes')
  })

  it('generates local responses for different question types', () => {
    const wrapper = mount(ChatBot, {
      global: {
        plugins: [Quasar],
        stubs: {
          Teleport: true
        }
      }
    })

    const vm = wrapper.vm as any
    
    // Test search help
    const searchResponse = vm.generateLocalResponse('วิธีค้นหาเอกสาร')
    expect(searchResponse).toContain('วิธีการค้นหาเอกสารในระบบ')
    
    // Test upload help
    const uploadResponse = vm.generateLocalResponse('การอัปโหลดเอกสาร')
    expect(uploadResponse).toContain('วิธีการอัปโหลดเอกสาร')
    
    // Test account help
    const accountResponse = vm.generateLocalResponse('การจัดการบัญชี')
    expect(accountResponse).toContain('การจัดการบัญชีผู้ใช้')
    
    // Test default response
    const defaultResponse = vm.generateLocalResponse('สวัสดี')
    expect(defaultResponse).toContain('สวัสดีครับ!')
  })
})
