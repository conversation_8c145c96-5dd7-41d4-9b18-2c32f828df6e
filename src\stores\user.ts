import { defineStore } from 'pinia'
import type { User } from 'src/types/User'
import userService from 'src/services/user'
import { ref } from 'vue'
const data1: User[] = [
  {
    user_id: 1,
    username: '<PERSON>',
    password: 'FIM',
    full_name: '<PERSON><PERSON> Ya<PERSON>',
    role: {
      role_id: 1,
      role_name: '',
    },
    department: {
      department_id: 1,
      department_name: '',
      //divistion_name: '', ERD does not have divistion_name
    },
    email: '',
    profile:'',
  },
]
export const useUserStore = defineStore('user', () => {
  const initialUser: User = {
    user_id: 0,
    username: '',
    password: '',
    full_name: '',
    role: {
      role_id: 0,
      role_name: '',
    },
    department: {
      department_id: 1,
      department_name: '',
      //divistion_name: '', ERD does not have divistion_name
    },
    email: '',
    profile:'',
  }

  const editedUser = ref<User & { files: File[] }>(JSON.parse(JSON.stringify(initialUser)))
  const users = ref<User[]>([])
  const currentUser = ref<User>()
  const form = ref(data1)
  const token = ref<string>('')


  async function getUser(id: number) {
    //loadingStore.doLoad()
    try {
      const res = await userService.getUser(id)
      editedUser.value = res.data
      //user.value.user_id = editedUser.value.user_id
    } catch (error) {
      console.error('Error fetching user:', error)
    } finally {
      //loadingStore.finish()
    }
  }

  async function getUsers() {
    try {
      const res = await userService.getUsers()
      users.value = res.data
    } catch (error) {
      console.error('Error fetching user:', error)
    } finally {
      //loadingStore.finish()
    }
  }

  async function updateUser(id: number,formdata: FormData){
    try {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const res = await userService.updateUser(id,formdata)
    } catch (error) {
      console.error('Error fetching user:', error)
    } finally {
      //loadingStore.finish()
    }
  }

  function setSession(u: User, t?: string) {
    currentUser.value = u
    if (t) token.value = t
    // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any
    const { password, ...safeUser } = (u as any) || {}
    sessionStorage.setItem('user', JSON.stringify(safeUser))
    if (t) {
      sessionStorage.setItem('token', t)
      localStorage.setItem('access_token', t)
    }
  }

  function loadSession() {
    const u = sessionStorage.getItem('user')
    const t = sessionStorage.getItem('token')
    if (u) currentUser.value = JSON.parse(u)
    if (t) {
      token.value = t
      localStorage.setItem('access_token', t)
    }
  }

  function clearSession() {
    currentUser.value = undefined
    token.value = ''
    sessionStorage.removeItem('user')
    sessionStorage.removeItem('token')
    localStorage.removeItem('access_token')
  }

  function userloginSuccess(u: User, t?: string) {
    setSession(u, t)
  }

  function userlogout() {
    clearSession()
  }

  async function createUsersByCSV(csv:FormData){
    try{
      await userService.createUsersByCSV(csv)
    }catch(e){
      console.log(e)
    }
  }

  return {
    data1,
    initialUser,
    form,
    currentUser,
    editedUser,
    users,
    token,
    getUser,
    getUsers,
    updateUser,
    setSession,
    clearSession,
    userloginSuccess,
    userlogout,
    loadSession,
    createUsersByCSV
  }
})
