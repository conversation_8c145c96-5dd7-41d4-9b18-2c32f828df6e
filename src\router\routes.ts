import { type RouteRecordRaw } from 'vue-router'
// import AddOneUser from 'src/pages/AddOneUser.vue'
// import AddManyUsers from 'src/pages/AddManyUsers.vue'

const routes: RouteRecordRaw[] = [
  { path: '/Login', component: () => import('pages/LoginPage.vue') },
  {
    path: '/',
    component: () => import('layouts/MainLayout.vue'),
    // component: () => import('layouts/MainLayout.vue'),
    children: [
      { path: '', component: () => import('pages/HomePage.vue') },
      { path: 'DocManage', component: () => import('pages/DocumentManagement.vue') },
      //set to show waiting dialog GIF
      // { path: '', component: () => import('../components/GifWaiting.vue') },
      { path: 'uploadDocManage', component: () => import('pages/FileUpload2.vue') },
      {
        path: 'user',
        component: () => import('pages/UserManagement.vue'),
      },
      {
        path: 'editDocManage',
        component: () => import('pages/FileEdit.vue'),
      },
      { path: 'report', component: () => import('pages/ReportPage.vue') },
      { path: 'document', component: () => import('pages/DocumentPage.vue') },
      { path: '/document/:id', component: () => import('pages/DocumentView.vue') },
    ],
  },
  {
    path: '/:catchAll(.*)*',
    component: () => import('pages/ErrorNotFound.vue'),
  },
  {
    path: '/userTable',
    component: () => import('pages/UserManagement.vue'),
  },

  // { path: '/add-one-user', component: AddOneUser },
  // { path: '/add-many-users', component: AddManyUsers },
]

export default routes
