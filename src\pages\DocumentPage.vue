<template>
  <q-page-container>
    <q-page class="q-pt-md background-image">
      <q-card-section class="text-white">
        <div class="text-h5 row items-center">
          <div class="col-auto">
            <div class="text-h4">เอกสาร</div>
          </div>
          <!-- <q-icon name="fa-folder-settings" size="35px" class="text-white" /> -->
          <q-space />
          <div class="row q-gutter-md">
            <q-select class="bg-white text-black dropdown" v-model="selectedType" :options="docTypes"
              label="ประเภทของเอกสาร" placeholder="เลือกประเภท" outlined dense @update:model-value="resetIfDefault" />

            <div class="search-col">
              <q-input outlined dense v-model="search" placeholder="ค้นหา"
                class="custom-search bg-white text-black searcher border-none"
                @keyup.enter="contentSearch(search, selectedstatus, access_role, selectedDept, selectedType, page, page_size)">


                <!-- ปุ่ม toggle ค้นหาด้วยเนื้อหา -->
                  <q-btn flat round dense icon="find_in_page" :color="isSearchByContentSelected ? 'primary' : 'grey-6'"
                    @click="isSearchByContentSelected = !isSearchByContentSelected">
                    <q-tooltip class="text-subtitle2" :offset="[0, 8]">
                      เปิด/ปิด การค้นหาด้วยเนื้อหา
                    </q-tooltip>
                  </q-btn>

                <template v-slot:prepend>
                  <q-icon name="search" />
                </template>

                <template v-slot:append>
                  <!-- ปุ่ม dropdown แสดงจำนวน -->
                  <q-btn flat round dense :label="documentLimitLabel" :color="'primary'" :disable="!isSearchByContentSelected">
                    <q-menu>
                      <q-list dense style="min-width: 100px;">
                        <q-item v-for="option in [1, 2, 3, 5, 7]" :key="option" clickable v-close-popup
                          @click="page_size = option">
                          <q-item-section>{{ option }}</q-item-section>
                        </q-item>
                      </q-list>
                    </q-menu>

                    <q-tooltip class="text-subtitle2" :offset="[0, 8]">
                      เลือกจำนวนเอกสารในการค้นหาด้วยเนื้อหา
                    </q-tooltip>
                  </q-btn>
                </template>
              </q-input>
            </div>
            <!-- ยังไม่ได้ใส่ฟังชั่น enter -->
            <q-btn class="btn-search" color="info" label="ค้นหา" v-model="search"
              @click="contentSearch(search, selectedstatus, access_role, selectedDept, selectedType, page, page_size)" />
          </div>
        </div>
      </q-card-section>

      <q-card-section style="max-height: 90vh; overflow: hidden">
        <div class="table-container">
          <q-table class="custom-table" :rows="filteredDocuments" :columns="columns" row-key="id" bordered dense flat
            virtual-scroll :rows-per-page-options="[0, 10, 20, 30, 50]" :pagination="{ rowsPerPage: 0 }">
            <!-- คลิกชื่อเอกสารแล้วเด้งไปหน้า DocumentView -->
            <template v-slot:body-cell-doc_name="props">
              <q-td :props="props">
                <q-btn flat dense class="text-left text-primary" @click="goToDocumentPage(props.row.doc_id)">
                  {{ props.row.doc_name }}
                   <template v-if="isToday(props.row.created_date)">
                    <span v-if="isToday(props.row.created_date)" class="new-badge q-ml-sm">NEW</span>
                  </template>
                </q-btn>
              </q-td>
            </template>
          </q-table>
        </div>
      </q-card-section>
    </q-page>
  </q-page-container>
  <LoadingDialog />
  <router-view />
</template>

<script setup lang="ts">

import { ref, computed, onMounted, watchEffect } from 'vue'
import documentService from 'src/services/document'
import type { Document } from 'src/types/Document'
import { useDocumentstore } from 'src/stores/document'
import { useDepartmentstore } from 'src/stores/department'
import { useCategorystore } from 'src/stores/category'
import { useRouter } from 'vue-router'
import { useDialogOrPopupstore } from 'src/stores/dialogOrPopup'
import LoadingDialog from 'src/components/LoadingDialog.vue'
import { useUserStore } from 'src/stores/user'
import { useAccessPermissionstore } from 'src/stores/accessPermmission'
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import type { AccessPermission } from 'src/types/AccessPermission'

const router = useRouter()

async function goToDocumentPage(docId: number) {
  await router.push(`/document/${docId}`)
}

const documentStore = useDocumentstore()
const departmentStore = useDepartmentstore()
const categoryStore = useCategorystore()
const dialogOrPopupstore = useDialogOrPopupstore()
const userStore = useUserStore()
const accessPermissionStore = useAccessPermissionstore()

const search = ref('')
const selectedType = ref<string | null>(null)
const selectedstatus = ref<string | null>('Y')

const page = ref(1)
const page_size = ref(5)
const isSearchByContentSelected = ref(false)
const selectedDept = computed(() => userStore.currentUser?.department?.department_name || 'มหาวิทยาลัย')
//mock test
const access_role = computed(() => userStore.currentUser?.role.role_name || '')
const documentLimitLabel = computed(() => {
  return String(page_size.value)
})

const docTypes = ['ประเภท', 'ประกาศ', 'ระเบียบ', 'ข้อบังคับ', 'คำสั่ง']
const columns = [
  { name: 'index', label: 'ลำดับ', field: 'index', align: 'left' as const, style: 'width: 80px', headerStyle: 'width: 80px' },
  {
    name: 'doc_name', label: 'ชื่อเอกสาร', field: 'doc_name', align: 'left' as const,
    style: 'min-width: 300px',
    headerStyle: 'min-width: 300px'
  },
]


const filteredDocuments = computed(() => {
  let docs = [...documentStore.documents]

  if (selectedType.value && selectedType.value !== 'ประเภท') {
    docs = docs.filter((doc) => doc.category.category_name === selectedType.value)
  }

  if (selectedstatus.value && selectedstatus.value !== 'default') {
    docs = docs.filter((doc) => String(doc.is_public) === selectedstatus.value)
  }

  if (search.value && !isSearchByContentSelected.value) {
    docs = docs.filter((doc) => doc.doc_name.includes(search.value))
  }

  docs.sort((a, b) => {
    const parseDate = (dateStr: string) => {
      const [day, month, year] = dateStr.split("/").map(Number)
      return new Date(year!, month! - 1, day) // month - 1 เพราะ JavaScript นับเดือนจาก 0-11
    }

    const dateA = parseDate(a.created_date)
    const dateB = parseDate(b.created_date)

    return dateB.getTime() - dateA.getTime()
  })


  return docs.map((doc, index) => ({
    ...doc,
    index: index + 1,
  }))
})

watchEffect(() => {
  console.log('📦 filteredDocuments:', filteredDocuments.value)
})

const resetIfDefault = (newValue: string) => {
  selectedType.value = newValue === 'ประเภท' ? null : newValue
}


const fetchDocuments = async () => {
  try {
    if (userStore.currentUser) {
      await accessPermissionStore.getAccessPermissionsByUser(
        userStore.currentUser.role.role_id,
        userStore.currentUser.department.department_id
      )
    } else {
      const response = await documentService.getDocuments()
      documentStore.documents = response.data.map((doc: Document) => ({
        ...doc,
        type: doc.category.category_name,
        created_date: new Date(doc.created_date).toLocaleDateString('th-TH', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric',
        }),
      }))
    }
  } catch (error) {
    console.error('Failed to fetch documents:', error)
  }
}


onMounted(async () => {
  dialogOrPopupstore.setLoadingShowAndRename(true, 'กำลังโหลดข้อมูล')
  await departmentStore.getDepartments()
  await categoryStore.getCategories()
  await fetchDocuments()
  dialogOrPopupstore.setLoadingShowAndRename(false, '')
})
//Onsearch(() => {
//  console.log('กำลังค้นหา:', this.search)
//})

async function contentSearch(
  query: string | null,
  is_public: string | null,
  access_role: string | null,
  department: string | null,
  category: string | null,
  page: number | null,
  page_size: number | null,
) {
  if (isSearchByContentSelected.value && query) {
    dialogOrPopupstore.setLoadingShowAndRename(true, 'กำลังหาข้อมูล')
    try {
      console.log(`query=${query ?? ''}, is_public=${is_public ?? ''}, access_role=${access_role ?? ''}, department=${department ?? ''}, category=${category ?? ''}, page=${page ?? ''}, page_size=${page_size ?? ''}`);
      const dep_id = departmentStore.departments.find(dept => dept.department_name === department)?.department_id;
      const cat_id = categoryStore.categories.find(cat => cat.category_name === category)?.category_id;
      const res = await documentStore.contentSearch(
        query ?? '',
        is_public == 'default' ? '' : is_public,
        access_role ?? '',
        dep_id ?? 0,
        cat_id ?? 0,
        page ?? 1,
        page_size ?? 5,
      );
      if (res) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        documentStore.documents = res.map((doc: any) => {
          const departmentName = doc.department?.trim().toLowerCase() ?? '';
          const categoryName = doc.category?.trim().toLowerCase() ?? '';

          const department = departmentStore.departments.find(
            dep => dep.department_name.trim().toLowerCase() === departmentName
          );

          const category = categoryStore.categories.find(
            cat => cat.category_name.trim().toLowerCase() === categoryName
          );
          return {
            ...doc,
            department: {
              department_id: department?.department_id ?? 0,
              department_name: department?.department_name ?? '',
            },
            category: {
              category_id: category?.category_id ?? 0,
              category_name: category?.category_name ?? '',
            },
          };
        });

      }
      dialogOrPopupstore.setLoadingShowAndRename(false, '')
    } catch (e) {
      console.log(e);
      dialogOrPopupstore.setLoadingShowAndRename(false, '')
    }
  } else {
    await fetchDocuments();
    dialogOrPopupstore.setLoadingShowAndRename(false, '')
  }
}

function isToday(dateString: string) {
  const date = new Date(dateString)
  const today = new Date()

  return (
    date.getFullYear() === today.getFullYear() &&
    date.getMonth() === today.getMonth() &&
    date.getDate() === today.getDate()
  )
}


</script>

<style scoped>
.q-btn {
  font-size: 16px;
}

.q-select,
.q-input {
  font-size: 14px;
  min-width: 200px;
}

.dropdown {
  width: 100px;
  border-radius: 3px !important;
}

.table-container {
  max-height: calc(100vh - 200px);
  /* ปรับให้พอดีกับขนาดจอ */
  overflow-y: auto;
  /* ให้ Scroll เฉพาะตัวนี้ */
}

::v-deep(.custom-table thead tr) {
  background-color: #CAE3FF !important;
  color: rgb(0, 0, 0) !important;
}



::v-deep(.custom-table tbody tr:first-child td) {
  border-top: none !important;
  /* ลบเส้นด้านบนเเถวที่1 */
}



.text {
  font-size: 18px;
}

.t {
  min-height: calc(100vh - 66px);
  overflow-y: auto;
}

.searcher {
  width: 240px;
  max-width: 250px;
  height: 40px;
  padding: 0 0px;
  box-sizing: border-box;
  border-radius: 3px !important;
  overflow: hidden;
}

.button-container {
  display: flex;
  justify-content: center;
  /* จัดให้อยู่ตรงกลาง */
  align-items: center;
  /* จัดให้อยู่แนวเดียวกัน */
  gap: 20px;
  /* ระยะห่างระหว่างปุ่ม */
  flex-wrap: nowrap;
  /* ❌ ป้องกันปุ่มตกบรรทัด */
}

.action-btn {
  min-width: 100px;
  /* ป้องกันปุ่มเล็กเกินไป */
  max-width: 150px;
  /* จำกัดขนาดปุ่ม */
  flex-shrink: 0;
  /* ❌ ป้องกันปุ่มหดตัว */
}

.background-image {
  position: relative;
  overflow: hidden;
}

.background-image::before {
  content: "";
  position: absolute;
  inset: 0;
  background-image: url('/src/assets/img/Document_BG.jpg');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center 16px;
  z-index: 0;
}


.background-image::after {
  content: "";
  position: absolute;
  inset: 0;
  background-color: rgba(17, 35, 173, 0.64);
  margin-top: 16px;
  z-index: 1;
}

.background-image>* {
  position: relative;
  z-index: 2;
  /* ✅ ข้อความอยู่บนสุด ไม่โดนจาง */

}

/* =======================
   Base (mobile-first)
   ======================= */
.toolbar {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  gap: 8px;
}

.filters {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  gap: 8px;
}

.w-full-xs,
.filters .q-select,
.filters .q-input,
.filters .q-btn {
  width: 100%;
}

.searcher {
  width: 100%;
  min-width: 0;
}

.dropdown {
  width: 100%;
}

.table-container {
  max-height: calc(100vh - 280px);
}

/* very small screens */
@media (max-width: 400px) {
  .filters .q-input .q-field__append {
    align-items: flex-start;
  }
}

/* =======================
   Tablet ≥ 490px
   ======================= */
@media (min-width: 490px) {
  .toolbar {
    flex-direction: row;
    align-items: center;
    gap: 12px;
  }

  .filters {
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
    gap: 12px;
  }

  .searcher {
    width: clamp(240px, 40vw, 300px);
    min-width: 220px;
  }

  .dropdown {
    width: clamp(120px, 18vw, 140px);
  }

  .table-container {
    max-height: calc(100vh - 240px);
  }

  .filters .q-input .q-field__append {
    align-items: center;
  }
}

/* =======================
   Notebook ≥ 769px
   ======================= */
@media (min-width: 769px) {
  .filters {
    flex-wrap: wrap;
  }

  .searcher {
    width: clamp(260px, 32vw, 320px);
    min-width: 240px;
  }

  .dropdown {
    width: clamp(120px, 12vw, 140px);
  }

  .table-container {
    max-height: calc(100vh - 220px);
  }
}

/* =======================
   Desktop ≥ 1200px
   ======================= */
@media (min-width: 1200px) {
  .toolbar {
    gap: 12px;
  }

  .filters {
    gap: 12px;
  }

  .searcher {
    width: 320px;
    min-width: 260px;
  }

  .dropdown {
    width: 140px;
  }

  .table-container {
    max-height: calc(100vh - 200px);
  }
}

/* =======================
   Small screens ≤ 489px
   ======================= */
@media (max-width: 489px) {
  .search-col {
    flex: 1 1 100%;
    max-width: 100%;
  }

  .q-input.custom-search.searcher {
    width: 100% !important;
    max-width: 100% !important;
  }

  .q-input.custom-search .q-field__append {
    flex-wrap: wrap;
    justify-content: flex-start;
    row-gap: 4px;
  }

  .btn-search {
    flex: 1 1 100%;
    max-width: 100%;
    width: 100%;
  }

  .btn-search .q-btn__content {
    justify-content: center;
  }

  .q-select,
  .q-input {
    min-width: 0;
  }
}

/* ให้ข้อความในหัว/เซลล์ตารางขึ้นบรรทัดใหม่ได้ */
::v-deep(.custom-table th),
::v-deep(.custom-table td) {
  white-space: normal;
  /* ยอมขึ้นบรรทัด */
  overflow-wrap: anywhere;
  /* กันคำยาวๆ ไม่ล้น */
  word-break: break-word;
  /* เผื่อ browser เก่า */
}

/* q-btn ภายในเซลล์ให้ยอมตัดบรรทัด */
::v-deep(.custom-table .q-btn) {
  height: auto;
  /* ให้สูงตามเนื้อหา */
  line-height: inherit;
  white-space: normal;
  padding: 0;
  /* ให้พอดีกับแถว */
  text-align: left;
}

/* คอนเทนต์ในปุ่มให้ wrap ได้จริง */
::v-deep(.custom-table .q-btn .q-btn__content) {
  white-space: normal;
  flex-wrap: wrap;
  text-align: left;
}

@media (max-width: 480px) {
  ::v-deep(.custom-table .q-btn .q-btn__content) {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    /* แสดง 2 บรรทัด */
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

::v-deep(.custom-table thead th) {
  white-space: nowrap;
}

/* มือถือ (Quasar xs < 600px) */
@media (max-width: 599.98px) {

  /* ลดขนาดตัวอักษร + ระยะบรรทัด + padding ของแถวข้อมูล */
  ::v-deep(.custom-table tbody td) {
    font-size: 13px;
    line-height: 1.5;
    padding: 6px 8px;
  }

  /* ให้ปุ่มในเซลล์ย่อขนาดตามกัน */
  ::v-deep(.custom-table tbody td .q-btn),
  ::v-deep(.custom-table tbody td .q-btn .q-btn__content) {
    font-size: inherit;
    line-height: inherit;
  }
}

/* NEW badge แบบทองวิบวับ */
::v-deep(.new-badge) {
  position: relative;
  display: inline-block;
  padding: 2px 8px;
  border-radius: 999px;
  font-size: 11px;
  font-weight: 900;
  letter-spacing: 0.5px;
  color: #3b2f00;
  background: linear-gradient(135deg, #7a5c00, #cba135, #ffde59, #cba135, #7a5c00);
  box-shadow: 0 0 8px rgba(255, 215, 0, 0.6),
              inset 0 0 12px rgba(255, 215, 0, 0.35);
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.7);
  overflow: hidden;
}

/* เส้นแสงวิ่งผ่าน */
::v-deep(.new-badge)::after {
  content: "";
  position: absolute;
  top: -60%;
  left: -40%;
  width: 35%;
  height: 220%;
  background: linear-gradient(
    to right,
    transparent,
    rgba(255, 255, 255, 0.85),
    transparent
  );
  transform: rotate(25deg);
  animation: shine 1.8s cubic-bezier(.4, 0, .2, 1) infinite;
}

/* ดวงดาวประกาย */
::v-deep(.new-badge)::before {
  content: "✦";
  position: absolute;
  right: 6px;
  top: -2px;
  font-size: 10px;
  color: #fff7b3;
  animation: twinkle 1.4s ease-in-out infinite alternate;
}

/* กันปุ่ม q-btn ตัดเอฟเฟกต์ออก */
::v-deep(.q-btn .q-btn__content) {
  overflow: visible;
}

@keyframes shine {
  0%   { left: -50%; }
  55%  { left: 130%; }
  100% { left: 130%; }
}

@keyframes twinkle {
  from { transform: scale(.9) rotate(10deg); opacity: .6; }
  to   { transform: scale(1.25) rotate(0); opacity: 1; }
}

</style>
