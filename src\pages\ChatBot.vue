<template>
  <Teleport to="body">
    <transition name="luxury-chat-fade" appear>
      <div v-if="dialogOrPopupStore.showChatPage" class="luxury-chat-container">
        <!-- Luxury Header -->
        <div class="luxury-chat-header">
          <div class="header-background"></div>
          <div class="header-content">
            <div class="bot-profile">
              <div class="bot-avatar-container">
                <q-avatar size="48px" class="luxury-bot-avatar">
                  <q-icon name="person" size="28px" color="white" />
                  <div class="avatar-ring"></div>
                </q-avatar>
                <div class="status-indicator online"></div>
              </div>
              <div class="bot-info">
                <div class="bot-name">BUU Assistant</div>
                <!-- statusbot-->
                <!-- <div class="bot-status">
                  <div class="status-dot"></div>
                  <span>ออนไลน์ - พร้อมช่วยเหลือ</span>
                </div> -->
              </div>
            </div>
            <div class="header-actions">
              <!--minimizeChat ย่อจอเเบบไม่ลบข้อความ-->
              <!-- <q-btn flat round icon="minimize" size="md" class="action-btn minimize-btn" @click="minimizeChat">
                <q-tooltip class="luxury-tooltip">ย่อหน้าต่าง</q-tooltip>
              </q-btn> -->
              <q-btn flat round icon="close" size="md" class="action-btn close-btn" @click="closeChat">
                <q-tooltip class="luxury-tooltip">ปิดแชท</q-tooltip>
              </q-btn>
            </div>
          </div>
        </div>

        <!-- Welcome Section -->
        <div class="welcome-section" v-if="messages.length <= 1">
          <div class="welcome-content">
            <div class="welcome-icon">
              <q-icon name="waving_hand" size="48px" color="primary" />
            </div>
            <h3 class="welcome-title">สวัสดีครับ!</h3>
            <p class="welcome-subtitle">ยินดีต้อนรับสู่ระบบช่วยเหลือของมหาวิทยาลัยบูรพา</p>
            <div class="quick-suggestions">
              <div class="suggestions-title">คำถามที่พบบ่อย:</div>
              <div class="suggestion-buttons">
                <q-btn v-for="suggestion in quickSuggestions" :key="suggestion.id" flat no-caps class="suggestion-btn"
                  @click="askQuickQuestion(suggestion.question)">
                  <q-icon :name="suggestion.icon" size="16px" class="suggestion-icon" />
                  <span>{{ suggestion.label }}</span>
                </q-btn>
              </div>
            </div>
          </div>
        </div>

        <!-- Messages Area -->
        <div ref="chatContentRef" class="luxury-chat-content">
          <div class="messages-container">
            <!-- Chat Messages -->
            <div v-for="(msg, index) in messages" :key="index" :class="['luxury-message-row', msg.role]">
              <!-- Bot Message -->
              <template v-if="msg.role === 'bot'">
                <div class="message-avatar">
                  <q-avatar size="36px" class="bot-message-avatar">
                    <q-icon name="smart_toy" size="20px" color="white" />
                  </q-avatar>
                </div>
                <div class="message-content">
                  <div class="luxury-message-bubble bot-bubble">
                    <div class="message-text" v-html="msg.content"></div>
                    <div class="message-time">{{ formatTime(msg.timestamp) }}</div>
                  </div>
                </div>
              </template>

              <!-- User Message -->
              <template v-else>
                <div class="message-content user-content">
                  <div class="luxury-message-bubble user-bubble">
                    <div class="message-text">{{ msg.content }}</div>
                    <div class="message-time">{{ formatTime(msg.timestamp) }}</div>
                  </div>
                </div>
              </template>
            </div>

            <!-- Typing Indicator -->
            <div v-if="isTyping" class="luxury-message-row bot">
              <div class="message-avatar">
                <q-avatar size="36px" class="bot-message-avatar">
                  <q-icon name="smart_toy" size="20px" color="white" />
                </q-avatar>
              </div>
              <div class="message-content">
                <div class="typing-indicator">
                  <div class="typing-text">กำลังพิมพ์</div>
                  <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Luxury Input Area -->
        <div class="luxury-input-area">
          <div class="input-container">
            <div class="input-wrapper">
              <q-input ref="inputRef" v-model="currentInput" type="textarea" autogrow
                placeholder="พิมพ์ข้อความของคุณ..." outlined dense class="luxury-input" @keyup.enter="ask"
                :loading="isTyping">
                <!-- <template v-slot:prepend>
                  <q-btn flat round icon="attach_file" size="sm" class="attach-btn" @click="attachFile">
                    <q-tooltip class="luxury-tooltip">แนบไฟล์</q-tooltip>
                  </q-btn>
                </template> -->
                <template v-slot:append>
                  <q-btn flat round icon="send" size="sm" class="send-btn"
                    :class="{ 'send-btn--active': currentInput.trim() }" @click="ask"
                    :disable="!currentInput.trim() || isTyping">
                    <q-tooltip class="luxury-tooltip">ส่งข้อความ</q-tooltip>
                  </q-btn>
                </template>
              </q-input>
            </div>
            <div class="input-footer">
              <span class="footer-text">กด Enter เพื่อส่งข้อความ • Powered by DOTE</span>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, nextTick, onUnmounted, watch } from 'vue'
import axios from 'axios'
import { useDialogOrPopupstore } from '../stores/dialogOrPopup'
// import { useQuasar } from 'quasar'

const dialogOrPopupStore = useDialogOrPopupstore()
// const $q = useQuasar()

const messages = ref<{ role: 'user' | 'bot'; content: string; timestamp: Date }[]>([
  {
    role: 'bot',
    content: 'สวัสดีครับ! ยินดีต้อนรับสู่ระบบช่วยเหลือของมหาวิทยาลัยบูรพา ผมพร้อมช่วยเหลือเรื่องการค้นหาเอกสาร การใช้งานระบบ และตอบคำถามต่างๆ ครับ',
    timestamp: new Date()
  }
])

const currentInput = ref('')
const isTyping = ref(false)
const dots = ref('')
const chatContentRef = ref<HTMLDivElement | null>(null)
const inputRef = ref<HTMLInputElement | null>(null)
let typingInterval: NodeJS.Timeout | null = null
const sessionId = ref(localStorage.getItem('chat_session') || crypto.randomUUID())
localStorage.setItem('chat_session', sessionId.value)

// Quick suggestions for common questions
const quickSuggestions = ref([
  {
    id: 1,
    icon: 'search',
    label: 'วิธีค้นหาเอกสาร',
    question: 'ช่วยแนะนำวิธีการค้นหาเอกสารในระบบหน่อยครับ'
  },
  {
    id: 2,
    icon: 'upload',
    label: 'การอัปโหลดเอกสาร',
    question: 'ต้องการทราบวิธีการอัปโหลดเอกสารเข้าระบบ'
  },
  {
    id: 3,
    icon: 'person',
    label: 'การจัดการบัญชี',
    question: 'มีปัญหาเกี่ยวกับการจัดการบัญชีผู้ใช้'
  },
  {
    id: 4,
    icon: 'help',
    label: 'ช่วยเหลือทั่วไป',
    question: 'ต้องการความช่วยเหลือเกี่ยวกับการใช้งานระบบ'
  }
])

function startTypingAnimation() {
  let count = 0
  typingInterval = setInterval(() => {
    count = (count + 1) % 4
    dots.value = '.'.repeat(count)
  }, 500)
}

function stopTypingAnimation() {
  if (typingInterval) clearInterval(typingInterval)
  typingInterval = null
  dots.value = ''
}

async function ask() {
  const question = currentInput.value.trim()
  if (!question) return

  messages.value.push({ role: 'user', content: question, timestamp: new Date() })
  currentInput.value = ''
  await scrollToBottom()

  isTyping.value = true
  startTypingAnimation()

  try {
    const res = await axios.post('http://localhost:8080/iverytried/chatbot', {
      query: question,
      is_public: 'Y',
      access_role: 'Public',
      session_id: sessionId.value,
      chat_history: messages.value.map((msg) => ({
        role: msg.role,
        content: msg.content
      }))
    })

    const answerText = formatAnswerHtmlWithStructure(res);
    messages.value.push({ role: 'bot', content: answerText, timestamp: new Date() })

  } catch (err) {
    console.error('Chatbot API error:', err)
    // Fallback to local AI responses
    const localResponse = generateLocalResponse(question)
    messages.value.push({ role: 'bot', content: localResponse, timestamp: new Date() })

  } finally {
    isTyping.value = false
    stopTypingAnimation()
    await scrollToBottom()
  }
}

function generateLocalResponse(userMessage: string): string {
  const message = userMessage.toLowerCase()

  // Document search help
  if (message.includes('ค้นหา') || message.includes('search') || message.includes('หา')) {
    return `
      <div class="response-card">
        <h4>🔍 วิธีการค้นหาเอกสารในระบบ</h4>
        <div class="step-list">
          <div class="step">
            <span class="step-number">1</span>
            <div class="step-content">
              <strong>เข้าสู่หน้าเอกสาร</strong><br>
              คลิกที่เมนู "เอกสาร" ในแถบนำทางด้านบน
            </div>
          </div>
          <div class="step">
            <span class="step-number">2</span>
            <div class="step-content">
              <strong>ใช้ช่องค้นหา</strong><br>
              พิมพ์คำค้นหาในช่องค้นหาด้านบนของหน้า
            </div>
          </div>
          <div class="step">
            <span class="step-number">3</span>
            <div class="step-content">
              <strong>กรองผลลัพธ์</strong><br>
              ใช้ตัวกรองตามประเภท วันที่ หรือผู้เขียน
            </div>
          </div>
          <div class="step">
            <span class="step-number">4</span>
            <div class="step-content">
              <strong>เลือกเอกสาร</strong><br>
              คลิกที่เอกสารที่ต้องการเพื่อดูรายละเอียด
            </div>
          </div>
        </div>
        <div class="tip-box">
          💡 <strong>เคล็ดลับ:</strong> ใช้เครื่องหมายคำพูด " " เพื่อค้นหาวลีที่แน่นอน
        </div>
      </div>
    `
  }

  // Upload help
  if (message.includes('อัปโหลด') || message.includes('upload') || message.includes('เพิ่ม')) {
    return `
      <div class="response-card">
        <h4>📤 วิธีการอัปโหลดเอกสาร</h4>
        <div class="step-list">
          <div class="step">
            <span class="step-number">1</span>
            <div class="step-content">
              <strong>เข้าสู่หน้าจัดการเอกสาร</strong><br>
              คลิกที่เมนู "การจัดการเอกสาร"
            </div>
          </div>
          <div class="step">
            <span class="step-number">2</span>
            <div class="step-content">
              <strong>คลิกปุ่มเพิ่มเอกสาร</strong><br>
              หาปุ่ม "เพิ่มเอกสารใหม่" และคลิก
            </div>
          </div>
          <div class="step">
            <span class="step-number">3</span>
            <div class="step-content">
              <strong>เลือกไฟล์</strong><br>
              คลิก "เลือกไฟล์" และเลือกจากคอมพิวเตอร์
            </div>
          </div>
          <div class="step">
            <span class="step-number">4</span>
            <div class="step-content">
              <strong>กรอกข้อมูล</strong><br>
              ใส่ชื่อเอกสาร คำอธิบาย และหมวดหมู่
            </div>
          </div>
          <div class="step">
            <span class="step-number">5</span>
            <div class="step-content">
              <strong>บันทึก</strong><br>
              คลิกปุ่ม "บันทึก" เพื่อเสร็จสิ้น
            </div>
          </div>
        </div>
        <div class="info-box">
          📋 <strong>ไฟล์ที่รองรับ:</strong> PDF, DOC, DOCX, TXT (ขนาดไม่เกิน 10MB)
        </div>
      </div>
    `
  }

  // Account management
  if (message.includes('บัญชี') || message.includes('account') || message.includes('โปรไฟล์') || message.includes('profile')) {
    return `
      <div class="response-card">
        <h4>👤 การจัดการบัญชีผู้ใช้</h4>
        <div class="step-list">
          <div class="step">
            <span class="step-number">1</span>
            <div class="step-content">
              <strong>เข้าสู่โปรไฟล์</strong><br>
              คลิกที่รูปโปรไฟล์มุมขวาบน
            </div>
          </div>
          <div class="step">
            <span class="step-number">2</span>
            <div class="step-content">
              <strong>เลือกเมนู</strong><br>
              คลิก "โปรไฟล์ของฉัน" จากเมนูดรอปดาวน์
            </div>
          </div>
          <div class="step">
            <span class="step-number">3</span>
            <div class="step-content">
              <strong>แก้ไขข้อมูล</strong><br>
              เปลี่ยนแปลงข้อมูลส่วนตัวตามต้องการ
            </div>
          </div>
          <div class="step">
            <span class="step-number">4</span>
            <div class="step-content">
              <strong>เปลี่ยนรหัสผ่าน</strong><br>
              คลิก "เปลี่ยนรหัสผ่าน" หากต้องการ
            </div>
          </div>
          <div class="step">
            <span class="step-number">5</span>
            <div class="step-content">
              <strong>บันทึกการเปลี่ยนแปลง</strong><br>
              คลิก "บันทึก" เพื่อยืนยันการเปลี่ยนแปลง
            </div>
          </div>
        </div>
        <div class="warning-box">
          ⚠️ <strong>หมายเหตุ:</strong> หากลืมรหัสผ่าน กรุณาติดต่อผู้ดูแลระบบ
        </div>
      </div>
    `
  }

  // Default response
  return `
    <div class="response-card">
      <h4>🤖 สวัสดีครับ!</h4>
      <p>ขอบคุณสำหรับคำถามครับ ผมเป็นผู้ช่วยอัจฉริยะของมหาวิทยาลัยบูรพา</p>

      <div class="help-topics">
        <h5>📚 หัวข้อที่ผมสามารถช่วยได้:</h5>
        <ul>
          <li>🔍 การค้นหาและจัดการเอกสาร</li>
          <li>📤 การอัปโหลดไฟล์เอกสาร</li>
          <li>👤 การจัดการบัญชีผู้ใช้</li>
          <li>🛠️ การใช้งานระบบต่างๆ</li>
          <li>🔧 การแก้ไขปัญหาเบื้องต้น</li>
        </ul>
      </div>

      <div class="suggestion-text">
        <strong>💬 ลองถามคำถามเช่น:</strong><br>
        "วิธีค้นหาเอกสาร" หรือ "การอัปโหลดไฟล์" หรือ "แก้ไขโปรไฟล์"
      </div>
    </div>
  `
}

function formatAnswerHtmlWithStructure(res: { data: { answer: string; ref?: { doc_id: number }[] } }): string {
  const answer = res.data.answer?.trim() || '';
  const refs = res.data.ref;

  const htmlParts: string[] = [];
  const lines = answer.split('\n');
  let insideList = false;

  // 🔧 ฟังก์ชันช่วยแปลง inline markdown เช่น **bold**
  function convertMarkdownInline(text: string): string {
    return text
      .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')
      .replace(/__(.+?)__/g, '<strong>$1</strong>')
      .replace(/\*(.+?)\*/g, '<em>$1</em>') // เพิ่ม italic แบบ *...*
      .replace(/_(.+?)_/g, '<em>$1</em>');
  }

  for (let line of lines) {
    line = line.trim();

    if (!line) {
      if (insideList) {
        htmlParts.push('</ul>');
        insideList = false;
      }
      continue;
    }

    // ✅ หัวข้อแบบ **หัวข้อ:**
    if (/^\*{1,2}.+?\*{1,2}:?$/.test(line)) {
      if (insideList) {
        htmlParts.push('</ul>');
        insideList = false;
      }

      const headingText = line.replace(/\*{1,2}/g, '').replace(/:$/, '');
      htmlParts.push(`<p><strong>${convertMarkdownInline(headingText)}</strong></p>`);
      continue;
    }

    // ✅ รายการ bullet (- หรือ *)
    if (/^[-*]\s+/.test(line)) {
      if (!insideList) {
        htmlParts.push('<ul>');
        insideList = true;
      }
      const listItem = line.replace(/^[-*]\s+/, '');
      htmlParts.push(`<li>${convertMarkdownInline(listItem)}</li>`);
      continue;
    }

    // ✅ ย่อหน้าธรรมดา
    if (insideList) {
      htmlParts.push('</ul>');
      insideList = false;
    }
    htmlParts.push(`<p>${convertMarkdownInline(line)}</p>`);
  }

  if (insideList) htmlParts.push('</ul>');

  let answerText = htmlParts.join('');

  // ✅ อ้างอิงเอกสาร
  if (Array.isArray(refs) && refs.length > 0) {
    const uniqueDocIds = Array.from(new Set(refs.map(r => r.doc_id)));

    const refLinks = uniqueDocIds
      .map(docId =>
        `<a href="http://localhost:9000/document/${docId}" target="_blank" style="text-decoration: none; color: #1976d2;">
          📄 เอกสาร ${docId}
        </a>`
      )
      .join(' , ');

    answerText += `<br/><br/><strong>📄 อ้างอิง:</strong> ${refLinks}`;
  }

  return answerText;
}


async function scrollToBottom() {
  await nextTick()
  const el = chatContentRef.value
  if (el) {
    el.scrollTo({
      top: el.scrollHeight,
      behavior: 'smooth'
    })
  }
}

function closeChat() {
  dialogOrPopupStore.showChatPage = false
}
// minimizeChat ย่อจอเเบบไม่ลบข้อความ
// function minimizeChat() {
//   dialogOrPopupStore.showChatPage = false
// }

function askQuickQuestion(question: string) {
  currentInput.value = question
  void ask()
}

// file upload
// function attachFile() {
//   // Create file input element
//   const fileInput = document.createElement('input')
//   fileInput.type = 'file'
//   fileInput.accept = '.pdf,.doc,.docx,.txt,.jpg,.jpeg,.png'
//   fileInput.multiple = false

//   fileInput.onchange = (event) => {
//     const file = (event.target as HTMLInputElement).files?.[0]
//     if (file) {
//       handleFileUpload(file)
//     }
//   }

//   fileInput.click()
// }

// function handleFileUpload(file: File) {
//   // Check file size (max 10MB)
//   const maxSize = 10 * 1024 * 1024
//   if (file.size > maxSize) {
//     $q.notify({
//       type: 'negative',
//       message: 'ไฟล์มีขนาดใหญ่เกินไป (สูงสุด 10MB)',
//       position: 'top'
//     })
//     return
//   }

//   // Add file message to chat
//   messages.value.push({
//     role: 'user',
//     content: `📎 แนบไฟล์: ${file.name} (${formatFileSize(file.size)})`,
//     timestamp: new Date()
//   })

//   // Simulate file processing
//   isTyping.value = true
//   setTimeout(() => {
//     messages.value.push({
//       role: 'bot',
//       content: `ได้รับไฟล์ "${file.name}" เรียบร้อยแล้วครับ<br><br>
//         <strong>รายละเอียดไฟล์:</strong><br>
//         • ชื่อไฟล์: ${file.name}<br>
//         • ขนาด: ${formatFileSize(file.size)}<br>
//         • ประเภท: ${file.type || 'ไม่ระบุ'}<br><br>
//         <em>ต้องการให้ผมช่วยอะไรเกี่ยวกับไฟล์นี้ไหมครับ?</em>`,
//       timestamp: new Date()
//     })
//     isTyping.value = false
//     void scrollToBottom()
//   }, 2000)

//   void scrollToBottom()
// }

// function formatFileSize(bytes: number): string {
//   if (bytes === 0) return '0 Bytes'
//   const k = 1024
//   const sizes = ['Bytes', 'KB', 'MB', 'GB']
//   const i = Math.floor(Math.log(bytes) / Math.log(k))
//   return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
// }

function formatTime(timestamp: Date): string {
  return timestamp.toLocaleTimeString('th-TH', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

watch(
  () => dialogOrPopupStore.showChatPage,
  async (val) => {
    if (val) {
      await nextTick()
      inputRef.value?.focus()
    }
  }
)

onUnmounted(() => stopTypingAnimation())
</script>

<style scoped>
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap');

/* ========================================
   LUXURY CHATBOT PAGE DESIGN
   ======================================== */

.luxury-chat-container {
  position: fixed;
  bottom: 24px;
  right: 24px;
  width: 480px;
  height: 700px;
  display: flex;
  flex-direction: column;
  z-index: 9999;
  background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.96) 0%,
      rgba(248, 250, 252, 0.96) 100%);
  backdrop-filter: blur(28px) saturate(1.3);
  border-radius: 28px;
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow:
    0 30px 90px rgba(0, 0, 0, 0.16),
    0 12px 35px rgba(0, 0, 0, 0.12),
    0 6px 20px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  overflow: hidden;
  font-family: 'Inter', 'Kanit', sans-serif;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.luxury-chat-container:hover {
  transform: translateY(-2px);
  box-shadow:
    0 35px 100px rgba(0, 0, 0, 0.18),
    0 15px 40px rgba(0, 0, 0, 0.14),
    0 8px 25px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

/* Luxury Header */
.luxury-chat-header {
  position: relative;
  background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
  color: white;
  padding: 20px 24px;
  border-radius: 24px 24px 0 0;
  overflow: hidden;
}

.header-background {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent);
  transition: left 1.5s ease;
}

.luxury-chat-header:hover .header-background {
  left: 100%;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 2;
}

.bot-profile {
  display: flex;
  align-items: center;
  gap: 16px;
}

.bot-avatar-container {
  position: relative;
}

.luxury-bot-avatar {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border: 3px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3);
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  position: relative;
}

.avatar-ring {
  position: absolute;
  inset: -3px;
  border-radius: 50%;
  background: linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4);
  animation: rotate 3s linear infinite;
  opacity: 0.8;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.status-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  border: 3px solid white;
}

.status-indicator.online {
  background: linear-gradient(135deg, #10b981, #059669);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.3);
  animation: statusPulse 2s infinite;
}

@keyframes statusPulse {

  0%,
  100% {
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.3);
  }

  50% {
    box-shadow: 0 0 0 6px rgba(16, 185, 129, 0.1);
  }
}

.bot-info {
  flex: 1;
}

.bot-name {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 4px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.bot-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  opacity: 0.9;
  font-weight: 500;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: linear-gradient(135deg, #10b981, #059669);
  animation: pulse 2s infinite;
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

.header-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  inset: 0;
  background: rgba(255, 255, 255, 0.1);
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.action-btn:hover {
  color: white;
  transform: scale(1.1);
}

.action-btn:hover::before {
  opacity: 1;
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  color: #fca5a5;
}

/* Welcome Section */
.welcome-section {
  padding: 32px 24px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
}

.welcome-content {
  text-align: center;
}

.welcome-icon {
  margin-bottom: 16px;
}

.welcome-title {
  color: #1e293b;
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 12px 0;
  font-family: 'Kanit', sans-serif;
}

.welcome-subtitle {
  color: #64748b;
  font-size: 15px;
  line-height: 1.6;
  margin: 0 0 24px 0;
  font-weight: 500;
}

.quick-suggestions {
  margin-top: 20px;
}

.suggestions-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
}

.suggestion-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.suggestion-btn {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  color: #475569;
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 16px;
  padding: 16px 12px;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  min-height: 70px;
  position: relative;
  overflow: hidden;
}

.suggestion-btn::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.suggestion-btn:hover {
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.suggestion-btn:hover::before {
  opacity: 1;
}

.suggestion-icon {
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.suggestion-btn:hover .suggestion-icon {
  transform: scale(1.2);
}

.suggestion-btn span {
  position: relative;
  z-index: 2;
}

/* Messages Area */
.luxury-chat-content {
  flex: 1;
  overflow-y: auto;
  background: linear-gradient(135deg, #fafbfc 0%, #f1f5f9 100%);
  position: relative;
  padding: 0;
}

.messages-container {
  padding: 32px 24px 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-width: 100%;
}

/* Message Rows */
.luxury-message-row {
  display: flex;
  gap: 12px;
  align-items: flex-end;
  animation: messageSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.luxury-message-row.user {
  flex-direction: row-reverse;
}

.message-avatar {
  flex-shrink: 0;
}

.bot-message-avatar {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border: 2px solid rgba(59, 130, 246, 0.2);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.user-message-avatar {
  background: linear-gradient(135deg, #10b981, #059669);
  border: 2px solid rgba(16, 185, 129, 0.2);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
}

.message-content {
  flex: 1;
  max-width: 320px;
}

.user-content {
  display: flex;
  justify-content: flex-end;
}

/* Message Bubbles */
.luxury-message-bubble {
  border-radius: 24px 24px 24px 8px;
  padding: 20px 24px;
  position: relative;
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.08),
    0 3px 12px rgba(0, 0, 0, 0.04),
    0 1px 4px rgba(0, 0, 0, 0.02);
  border: 1px solid rgba(0, 0, 0, 0.04);
  backdrop-filter: blur(12px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.luxury-message-bubble:hover {
  transform: translateY(-1px);
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.12),
    0 4px 15px rgba(0, 0, 0, 0.06),
    0 2px 6px rgba(0, 0, 0, 0.04);
}

.bot-bubble {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  color: #1e293b;
  border-radius: 24px 24px 24px 8px;
  border-left: 4px solid #3b82f6;
}

.user-bubble {
  background: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);
  color: white;
  border-radius: 24px 24px 8px 24px;
  border: 1px solid rgba(59, 130, 246, 0.2);
  box-shadow:
    0 6px 20px rgba(59, 130, 246, 0.25),
    0 3px 12px rgba(59, 130, 246, 0.15);
}

.message-text {
  font-size: 15px;
  line-height: 1.7;
  margin-bottom: 12px;
  font-weight: 500;
  letter-spacing: 0.01em;
  word-spacing: 0.05em;
}

.bot-bubble .message-text {
  color: #374151;
  font-family: 'Inter', 'Kanit', sans-serif;
}

.user-bubble .message-text {
  color: white;
  font-weight: 600;
}

.message-time {
  font-size: 12px;
  opacity: 0.75;
  text-align: right;
  font-weight: 600;
  letter-spacing: 0.02em;
  margin-top: 8px;
}

.bot-bubble .message-time {
  color: #6b7280;
}

.user-bubble .message-time {
  color: rgba(255, 255, 255, 0.9);
}

/* Typing Indicator */
.typing-indicator {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px 20px 20px 4px;
  padding: 16px 20px;
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.08),
    0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  gap: 12px;
}

.typing-text {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.typing-dots {
  display: flex;
  gap: 4px;
  align-items: center;
}

.typing-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  animation: typingDot 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
  animation-delay: 0s;
}

.typing-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typingDot {

  0%,
  60%,
  100% {
    transform: scale(1);
    opacity: 0.5;
  }

  30% {
    transform: scale(1.3);
    opacity: 1;
  }
}

/* Luxury Input Area */
.luxury-input-area {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  padding: 24px 28px 20px;
  border-radius: 0 0 24px 24px;
  backdrop-filter: blur(12px);
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.04);
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.input-wrapper {
  position: relative;
}

.luxury-input {
  border-radius: 20px;
  background: #f8fafc;
  border: 2px solid rgba(59, 130, 246, 0.12);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: 'Inter', sans-serif;
  font-size: 15px;
  line-height: 1.6;
  padding: 16px 20px;
}

.luxury-input:focus-within {
  background: white;
  border-color: #3b82f6;
  box-shadow:
    0 0 0 4px rgba(59, 130, 246, 0.12),
    0 6px 20px rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
}

.luxury-input textarea {
  font-size: 15px !important;
  line-height: 1.6 !important;
  font-weight: 500 !important;
}

.attach-btn {
  color: #6b7280;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 10px;
}

.attach-btn:hover {
  color: #3b82f6;
  transform: scale(1.1);
  background: rgba(59, 130, 246, 0.1);
}

.send-btn {
  color: #6b7280;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 10px;
}

.send-btn--active {
  color: white;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.send-btn--active:hover {
  transform: scale(1.2);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.input-footer {
  text-align: center;
  padding-top: 8px;
}

.footer-text {
  font-size: 12px;
  color: #9ca3af;
  font-weight: 600;
  letter-spacing: 0.02em;
  opacity: 0.8;
}

/* Animations */
@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Luxury Chat Fade Transition */
.luxury-chat-fade-enter-active,
.luxury-chat-fade-leave-active {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.luxury-chat-fade-enter-from {
  opacity: 0;
  transform: translateY(30px) scale(0.9);
}

.luxury-chat-fade-leave-to {
  opacity: 0;
  transform: translateY(30px) scale(0.9);
}

/* Scrollbar Styling */
.luxury-chat-content::-webkit-scrollbar {
  width: 6px;
}

.luxury-chat-content::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 6px;
}

.luxury-chat-content::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #cbd5e1, #94a3b8);
  border-radius: 6px;
  transition: all 0.3s ease;
  opacity: 0.6;
}

.luxury-chat-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #94a3b8, #64748b);
  opacity: 1;
}

/* Better text selection */
.message-text::selection {
  background: rgba(59, 130, 246, 0.2);
  color: inherit;
}

.bot-bubble .message-text::selection {
  background: rgba(59, 130, 246, 0.15);
}

.user-bubble .message-text::selection {
  background: rgba(255, 255, 255, 0.3);
}

/* Improved readability for long text */
.message-text {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Better spacing for Thai text */
.message-text:lang(th) {
  word-break: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* Luxury Tooltip */
.luxury-tooltip {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  color: white;
  font-family: 'Inter', sans-serif;
  font-size: 12px;
  font-weight: 500;
  border-radius: 8px;
  padding: 8px 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .luxury-chat-container {
    width: 420px;
    height: 640px;
    right: 16px;
    bottom: 16px;
  }

  .suggestion-buttons {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .suggestion-btn {
    min-height: 70px;
    padding: 16px 14px;
    font-size: 14px;
  }

  .message-content {
    max-width: 320px;
  }

  .messages-container {
    padding: 28px 20px 20px;
  }
}

@media (max-width: 480px) {
  .luxury-chat-container {
    width: calc(100vw - 24px);
    height: calc(100vh - 120px);
    right: 12px;
    bottom: 12px;
    max-width: 380px;
    max-height: 600px;
  }

  .welcome-section {
    padding: 28px 24px;
  }

  .luxury-chat-header {
    padding: 20px 24px;
  }

  .messages-container {
    padding: 24px 20px 20px;
    gap: 20px;
  }

  .message-content {
    max-width: calc(100% - 60px);
  }

  .luxury-input-area {
    padding: 20px 24px 16px;
  }

  .luxury-message-bubble {
    padding: 18px 20px;
    font-size: 14px;
  }

  .message-text {
    font-size: 14px;
    line-height: 1.6;
  }

  .response-card {
    padding: 24px 20px;
    margin: 10px 0;
  }

  .response-card h4 {
    font-size: 16px;
  }

  .step {
    padding: 14px;
    gap: 14px;
  }

  .step-number {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
}

/* Enhanced Response Cards */
.response-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  padding: 28px;
  border: 1px solid rgba(59, 130, 246, 0.12);
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.06),
    0 3px 12px rgba(0, 0, 0, 0.04);
  margin: 12px 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.response-card:hover {
  transform: translateY(-2px);
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.08),
    0 4px 15px rgba(0, 0, 0, 0.06);
}

.response-card h4 {
  color: #1e293b;
  font-size: 18px;
  font-weight: 700;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 12px;
  font-family: 'Kanit', sans-serif;
  letter-spacing: 0.01em;
}

.response-card h5 {
  color: #374151;
  font-size: 16px;
  font-weight: 600;
  margin: 20px 0 12px 0;
  font-family: 'Kanit', sans-serif;
}

.response-card p {
  color: #64748b;
  font-size: 15px;
  line-height: 1.7;
  margin: 0 0 18px 0;
  font-weight: 500;
  letter-spacing: 0.01em;
}

.response-card ul {
  margin: 0;
  padding-left: 24px;
  color: #64748b;
}

.response-card li {
  margin-bottom: 12px;
  font-size: 15px;
  line-height: 1.6;
  font-weight: 500;
}

/* Step List Styling */
.step-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin: 20px 0;
}

.step {
  display: flex;
  gap: 16px;
  align-items: flex-start;
  padding: 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 16px;
  border: 1px solid rgba(59, 130, 246, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.step:hover {
  transform: translateX(4px);
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-color: rgba(59, 130, 246, 0.15);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.08);
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #3b82f6, #6366f1);
  color: white;
  border-radius: 50%;
  font-size: 14px;
  font-weight: 700;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.step-content {
  flex: 1;
  padding-top: 4px;
}

.step-content strong {
  color: #1e293b;
  font-size: 16px;
  font-weight: 700;
  display: block;
  margin-bottom: 8px;
  font-family: 'Kanit', sans-serif;
  letter-spacing: 0.01em;
}

.step-content br+text {
  color: #64748b;
  font-size: 14px;
  line-height: 1.6;
  font-weight: 500;
}

/* Info Boxes */
.tip-box,
.info-box,
.warning-box,
.contact-box,
.support-box {
  padding: 16px 20px;
  border-radius: 16px;
  margin-top: 20px;
  font-size: 14px;
  line-height: 1.6;
  font-weight: 500;
  border-left: 4px solid;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tip-box {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-left-color: #3b82f6;
  color: #1e40af;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.08);
}

.info-box {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border: 1px solid rgba(34, 197, 94, 0.2);
  border-left-color: #10b981;
  color: #166534;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.08);
}

.warning-box {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 1px solid rgba(245, 158, 11, 0.2);
  border-left-color: #f59e0b;
  color: #92400e;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.08);
}

.contact-box,
.support-box {
  background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
  border: 1px solid rgba(139, 92, 246, 0.2);
  border-left-color: #8b5cf6;
  color: #6b21a8;
  box-shadow: 0 4px 15px rgba(139, 92, 246, 0.08);
}

.tip-box:hover,
.info-box:hover,
.warning-box:hover,
.contact-box:hover,
.support-box:hover {
  transform: translateY(-2px);
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.1),
    0 3px 12px rgba(0, 0, 0, 0.06);
}

/* Feature Grid */
.feature-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin: 16px 0;
}

.feature-item {
  display: flex;
  gap: 12px;
  align-items: flex-start;
  padding: 12px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.feature-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.feature-content {
  flex: 1;
}

.feature-content strong {
  color: #1e293b;
  font-size: 13px;
  font-weight: 600;
  display: block;
  margin-bottom: 4px;
}

/* Help Topics */
.help-topics {
  margin: 16px 0;
}

.suggestion-text {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  padding: 12px 16px;
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  margin-top: 16px;
  font-size: 13px;
  color: #475569;
}

.suggestion-text strong {
  color: #1e293b;
}

/* Troubleshoot List */
.troubleshoot-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin: 16px 0;
}

.trouble-item {
  padding: 12px 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  font-size: 13px;
  line-height: 1.5;
}

.trouble-item strong {
  color: #1e293b;
  font-weight: 600;
  display: block;
  margin-bottom: 4px;
}

/* Mobile Responsive for Response Cards */
@media (max-width: 480px) {
  .feature-grid {
    grid-template-columns: 1fr;
  }

  .step {
    gap: 8px;
  }

  .step-number {
    width: 24px;
    height: 24px;
    font-size: 11px;
  }

  .response-card {
    padding: 16px;
  }

  .response-card h4 {
    font-size: 15px;
  }
}

.luxury-chat-container {
  bottom: 80px;
  /* ยกขึ้น */
}
</style>
